"use client";

import { useState } from 'react';

export default function WorkingTest() {
  const [message, setMessage] = useState('This should work!');

  return (
    <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif' }}>
      <h1>Working Test Page</h1>
      <p>{message}</p>
      <button 
        onClick={() => setMessage('Button was clicked!')}
        style={{ 
          padding: '10px 20px', 
          backgroundColor: '#0070f3', 
          color: 'white', 
          border: 'none',
          borderRadius: '4px',
          cursor: 'pointer'
        }}
      >
        Click me
      </button>
    </div>
  );
} 