import React, { useState, useRef, useEffect } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';

interface HeaderProps {
  className?: string;
}

export default function Header({ className = '' }: HeaderProps) {
  const [isSearchExpanded, setIsSearchExpanded] = useState(false);
  const [inputWidth, setInputWidth] = useState(0);
  const [isScrolled, setIsScrolled] = useState(false);
  const searchInputRef = useRef<HTMLInputElement>(null);
  const pathname = usePathname();
  
  // Check if we're at dashboard root or other page (for styling)
  const isDashboardRoot = pathname === '/dashboard';
  
  const toggleSearch = () => {
    setIsSearchExpanded(!isSearchExpanded);
  };
  
  useEffect(() => {
    if (isSearchExpanded && searchInputRef.current) {
      // Focus the input when expanded
      searchInputRef.current.focus();
      setInputWidth(240); // Set width to expanded state
    } else {
      setInputWidth(0); // Set width to collapsed state
    }
  }, [isSearchExpanded]);

  // Add scroll event listener
  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 10) {
        setIsScrolled(true);
      } else {
        setIsScrolled(false);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Get current page title based on pathname
  const getPageTitle = () => {
    if (pathname === '/dashboard') return 'Dashboard';
    if (pathname === '/dashboard/projects') return 'Projekt';
    if (pathname === '/dashboard/employees') return 'Anställda';
    if (pathname === '/dashboard/estimates') return 'Offerter';
    if (pathname === '/dashboard/fakturor') return 'Fakturor';
    if (pathname === '/dashboard/settings') return 'Inställningar';
    return '';
  };

  return (
    <header 
      className={`
        sticky top-0 z-10 
        transition-all duration-300 ease-in-out
        ${isScrolled ? 'h-14' : 'h-[72px]'} 
        w-full border-b
        ${isDashboardRoot ? 'border-transparent' : 'border-gray-200/50 dark:border-gray-800/50'} 
        backdrop-blur-[2px] backdrop-saturate-[1.8] bg-white/70 dark:bg-gray-900/70
        shadow-sm
        ${className}
      `}
    >
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-4">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-3">
          <div className="flex items-center gap-3">
            <button
              onClick={() => window.history.back()} 
              className="flex items-center justify-center w-8 h-8 rounded-full bg-white/90 dark:bg-gray-800/90 shadow-sm border border-gray-200/70 dark:border-gray-700/60 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors backdrop-blur-sm backdrop-saturate-150"
            >
              <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="M15 18l-6-6 6-6" />
              </svg>
            </button>
            
            <div>
              <h1 className="text-xl font-medium text-gray-900 dark:text-white">
                {getPageTitle()}
              </h1>
            </div>
          </div>
          
          <div className="flex items-center space-x-3">
            {/* Expandable Search */}
            <div className="relative flex items-center">
              <div 
                className="relative overflow-hidden transition-all duration-300 ease-in-out flex items-center"
                style={{ width: `${inputWidth}px` }}
              >
                <input 
                  ref={searchInputRef}
                  type="text" 
                  placeholder="Sök..." 
                  className="w-full pl-10 pr-4 py-2 rounded-full bg-white/90 dark:bg-gray-800/90 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500/50 backdrop-blur-sm backdrop-saturate-150 border border-gray-200/70 dark:border-gray-700/60 shadow-sm"
                  onBlur={() => {
                    // Small delay to allow for clicks on the search icon
                    setTimeout(() => setIsSearchExpanded(false), 200);
                  }}
                />
              </div>
              <button 
                onClick={toggleSearch}
                className={`p-2 rounded-full hover:bg-gray-100/60 dark:hover:bg-gray-700/60 text-gray-600 dark:text-gray-300 ${isSearchExpanded ? 'absolute left-2 z-10' : 'bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm backdrop-saturate-150 border border-gray-200/70 dark:border-gray-700/60 shadow-sm'} transition-all duration-300`}
                aria-label="Search"
              >
                <svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M21 21L15 15M17 10C17 13.866 13.866 17 10 17C6.13401 17 3 13.866 3 10C3 6.13401 6.13401 3 10 3C13.866 3 17 6.13401 17 10Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
              </button>
            </div>
            
            <button className="p-2 rounded-full hover:bg-gray-100/60 dark:hover:bg-gray-700/60 text-gray-600 dark:text-gray-300 transition-all duration-200 backdrop-blur-sm backdrop-saturate-150 bg-white/90 dark:bg-gray-800/90 border border-gray-200/70 dark:border-gray-700/60 shadow-sm">
              <svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M15 17H20L18.5951 15.5951C18.2141 15.2141 18 14.6973 18 14.1585V11C18 8.38757 16.3304 6.16509 14 5.34142V5C14 3.89543 13.1046 3 12 3C10.8954 3 10 3.89543 10 5V5.34142C7.66962 6.16509 6 8.38757 6 11V14.1585C6 14.6973 5.78595 15.2141 5.40493 15.5951L4 17H9M15 17V18C15 19.6569 13.6569 21 12 21C10.3431 21 9 19.6569 9 18V17M15 17H9" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </button>
            
            <button className="p-2 rounded-full hover:bg-gray-100/60 dark:hover:bg-gray-700/60 text-gray-600 dark:text-gray-300 transition-all duration-200 backdrop-blur-sm backdrop-saturate-150 bg-white/90 dark:bg-gray-800/90 border border-gray-200/70 dark:border-gray-700/60 shadow-sm">
              <svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 6V12L16 14M22 12C22 17.5228 17.5228 22 12 22C6.47715 22 2 17.5228 2 12C2 6.47715 6.47715 2 12 2C17.5228 2 22 6.47715 22 12Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </button>
            
            <div className="w-px h-5 bg-gray-300/30 dark:bg-gray-600/30"></div>
            
            <button className="flex items-center space-x-2 p-1.5 rounded-full hover:bg-gray-100/60 dark:hover:bg-gray-700/60 transition-all duration-200 backdrop-blur-sm backdrop-saturate-150 bg-white/90 dark:bg-gray-800/90 border border-gray-200/70 dark:border-gray-700/60 shadow-sm">
              <div className="w-8 h-8 rounded-full ring-2 ring-white/10 bg-blue-500 flex items-center justify-center text-white font-medium text-sm">
                YM
              </div>
            </button>
          </div>
        </div>
      </div>
    </header>
  );
} 