"use client";

import React, { useState } from 'react';
import Link from 'next/link';
import { Sidebar, SidebarBody, SidebarLink } from './ui/sidebar';
import { 
  LayoutDashboard, 
  Briefcase,
  Users, 
  FileSpreadsheet, 
  Receipt, 
  Settings, 
  User, 
  BarChart3,
  <PERSON><PERSON>hart,
  LogOut,
  Building
} from 'lucide-react';
import { motion } from 'framer-motion';
import Image from 'next/image';
import { useAuth } from '@/app/auth/auth-provider';
import { useRouter } from 'next/navigation';
import { ModusLogo } from '@/components/ui/modus-logo';

interface DashboardSidebarProps {
  collapsed?: boolean;
  setCollapsed?: React.Dispatch<React.SetStateAction<boolean>>;
}

export default function DashboardSidebar({ collapsed = false, setCollapsed }: DashboardSidebarProps) {
  const router = useRouter();
  const { signOut } = useAuth();
  
  // Logout function
  const handleLogout = async () => {
    try {
      console.log('[DashboardSidebar] Logout initiated');
      await signOut();
      // Force redirect to login page
      router.push('/login');
    } catch (error) {
      console.error('[DashboardSidebar] Error logging out:', error);
      // Even if logout fails, redirect to login
      router.push('/login');
    }
  };
  
  // Links from the original sidebar
  const links = [
    {
      label: "Dashboard",
      href: "/dashboard-new",
      icon: (
        <BarChart3 className="text-neutral-700 dark:text-neutral-200 h-5 w-5 flex-shrink-0" />
      ),
    },
    {
      label: "Customers",
      href: "/dashboard/customers",
      icon: (
        <Building className="text-neutral-700 dark:text-neutral-200 h-5 w-5 flex-shrink-0" />
      ),
    },
    {
      label: "Offerter",
      href: "/dashboard/estimates",
      icon: (
        <FileSpreadsheet className="text-neutral-700 dark:text-neutral-200 h-5 w-5 flex-shrink-0" />
      ),
    },
    {
      label: "Projects",
      href: "/dashboard/projects",
      icon: (
        <Briefcase className="text-neutral-700 dark:text-neutral-200 h-5 w-5 flex-shrink-0" />
      ),
    },
    {
      label: "Fakturor",
      href: "/dashboard/fakturor",
      icon: (
        <Receipt className="text-neutral-700 dark:text-neutral-200 h-5 w-5 flex-shrink-0" />
      ),
    },
    {
      label: "Anställda",
      href: "/dashboard/employees",
      icon: (
        <Users className="text-neutral-700 dark:text-neutral-200 h-5 w-5 flex-shrink-0" />
      ),
    },
    {
      label: "Settings",
      href: "/dashboard/settings",
      icon: (
        <Settings className="text-neutral-700 dark:text-neutral-200 h-5 w-5 flex-shrink-0" />
      ),
    },
  ];

  return (
    <Sidebar open={!collapsed} setOpen={setCollapsed ? (open) => setCollapsed(!open) : undefined}>
      <SidebarBody className="justify-between gap-10">
        <div className="flex flex-col flex-1 overflow-y-auto overflow-x-hidden">
          {!collapsed ? <Logo /> : <LogoIcon />}
          <div className="mt-8 flex flex-col gap-2">
            {links.map((link, idx) => (
              <SidebarLink key={idx} link={link} />
            ))}
          </div>
        </div>
        <div className="flex flex-col gap-2">
          {/* Logout button */}
          <button 
            onClick={handleLogout}
            className="flex items-center justify-start gap-2 group/sidebar py-2 w-full hover:bg-neutral-200 dark:hover:bg-neutral-700 rounded-md px-2 transition-colors"
          >
            <LogOut className="text-red-500 dark:text-red-400 h-5 w-5 flex-shrink-0" />
            <motion.span
              animate={{
                display: !collapsed ? "inline-block" : "none",
                opacity: !collapsed ? 1 : 0,
              }}
              className="text-red-500 dark:text-red-400 text-sm group-hover/sidebar:translate-x-1 transition duration-150 whitespace-pre inline-block !p-0 !m-0"
            >
              Logga ut
            </motion.span>
          </button>
          
          <SidebarLink
            link={{
              label: "Youssef Mekidiche",
              href: "#",
              icon: (
                <div className="w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center text-white font-semibold text-sm">
                  YM
                </div>
              ),
            }}
          />
        </div>
      </SidebarBody>
    </Sidebar>
  );
}

export const Logo = () => {
  return (
    <Link
      href="/dashboard"
      className="font-normal flex space-x-3 items-center text-sm py-1 relative z-20"
    >
      <ModusLogo size="md" variant="dark" className="flex-shrink-0" />
      <motion.span
        initial={{ opacity: 0 }}
        animate={{
          opacity: 1,
          transition: { duration: 0.5 }
        }}
        className="font-medium whitespace-pre text-xl bg-gradient-to-r from-blue-600 via-indigo-500 to-emerald-400 bg-clip-text text-transparent font-bold relative"
      >
        Modus
        <motion.span
          className="absolute inset-0 bg-gradient-to-r from-emerald-400 via-blue-600 to-indigo-500 bg-clip-text text-transparent"
          initial={{ opacity: 0 }}
          animate={{
            opacity: [0, 0.5, 0],
            transition: {
              duration: 3,
              repeat: Infinity,
              repeatType: "reverse"
            }
          }}
        >
          Modus
        </motion.span>
      </motion.span>
    </Link>
  );
};

export const LogoIcon = () => {
  return (
    <Link
      href="/dashboard"
      className="font-normal flex space-x-2 items-center text-sm text-black py-1 relative z-20"
    >
      <ModusLogo size="md" variant="dark" className="flex-shrink-0" />
    </Link>
  );
};