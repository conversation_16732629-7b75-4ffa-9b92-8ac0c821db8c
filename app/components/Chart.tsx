import React from 'react';

interface ChartProps {
  className?: string;
}

export default function Chart({ className = '' }: ChartProps) {
  // This is a simplified chart component with a static bar graph
  // In a real application, you would use a library like Chart.js, Recharts, or D3.js
  
  const chartData = [
    { month: 'Jan', value: 30, secondValue: 20 },
    { month: 'Feb', value: 40, secondValue: 30 },
    { month: 'Mar', value: 45, secondValue: 35 },
    { month: 'Apr', value: 60, secondValue: 45 },
    { month: 'May', value: 70, secondValue: 55 },
    { month: 'Jun', value: 65, secondValue: 50 },
    { month: 'Jul', value: 80, secondValue: 60 },
    { month: 'Aug', value: 90, secondValue: 70 },
    { month: 'Sep', value: 85, secondValue: 65 },
    { month: 'Oct', value: 95, secondValue: 75 },
    { month: 'Nov', value: 75, secondValue: 60 },
    { month: 'Dec', value: 85, secondValue: 70 },
  ];
  
  const maxValue = Math.max(...chartData.map(item => item.value));
  
  return (
    <div className={`bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4 ${className}`}>
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Project Completion</h3>
        
        <div className="flex space-x-2">
          <select className="text-sm border border-gray-300 dark:border-gray-600 rounded px-2 py-1 bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-200">
            <option>This Year</option>
            <option>Last Year</option>
            <option>All Time</option>
          </select>
        </div>
      </div>
      
      <div className="h-80 mt-4">
        <div className="flex h-full">
          {/* Y-axis labels */}
          <div className="text-xs text-gray-500 dark:text-gray-400 pr-2 flex flex-col justify-between h-64 mt-auto">
            <div>100%</div>
            <div>75%</div>
            <div>50%</div>
            <div>25%</div>
            <div>0%</div>
          </div>
          
          {/* Chart itself */}
          <div className="flex-1 flex items-end justify-between h-64 border-b border-l border-gray-200 dark:border-gray-700 relative">
            {/* Horizontal grid lines */}
            <div className="absolute inset-0 flex flex-col justify-between pointer-events-none">
              <div className="border-b border-dashed border-gray-200 dark:border-gray-700 h-0"></div>
              <div className="border-b border-dashed border-gray-200 dark:border-gray-700 h-0"></div>
              <div className="border-b border-dashed border-gray-200 dark:border-gray-700 h-0"></div>
              <div className="border-b border-dashed border-gray-200 dark:border-gray-700 h-0"></div>
            </div>
            
            {/* Bars */}
            {chartData.map((item, index) => (
              <div key={index} className="flex flex-col items-center group">
                <div className="relative h-full flex items-end">
                  {/* Main bar */}
                  <div
                    className="w-8 bg-blue-500 dark:bg-blue-600 rounded-t transition-all duration-300 ease-in-out group-hover:bg-blue-600 dark:group-hover:bg-blue-700 z-10"
                    style={{ height: `${(item.value / maxValue) * 100}%` }}
                  >
                    {/* Tooltip on hover */}
                    <div className="opacity-0 group-hover:opacity-100 absolute -top-10 left-1/2 transform -translate-x-1/2 bg-gray-800 text-white text-xs rounded py-1 px-2 whitespace-nowrap transition-opacity duration-200">
                      {item.value}%
                    </div>
                  </div>
                  
                  {/* Secondary bar (offset) */}
                  <div
                    className="w-8 bg-blue-300 dark:bg-blue-800 rounded-t transition-all duration-300 ease-in-out absolute -left-2"
                    style={{ height: `${(item.secondValue / maxValue) * 100}%` }}
                  ></div>
                </div>
                <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">{item.month}</div>
              </div>
            ))}
          </div>
        </div>
      </div>
      
      <div className="flex justify-between items-center mt-6">
        <div className="flex space-x-6">
          <div className="flex items-center">
            <div className="w-3 h-3 bg-blue-500 dark:bg-blue-600 rounded-sm mr-2"></div>
            <span className="text-sm text-gray-600 dark:text-gray-400">Completed</span>
          </div>
          <div className="flex items-center">
            <div className="w-3 h-3 bg-blue-300 dark:bg-blue-800 rounded-sm mr-2"></div>
            <span className="text-sm text-gray-600 dark:text-gray-400">In Progress</span>
          </div>
        </div>
        
        <div className="text-gray-500 dark:text-gray-400 text-sm">
          <span className="font-medium text-green-500">↗</span> 15% increase from last year
        </div>
      </div>
    </div>
  );
} 