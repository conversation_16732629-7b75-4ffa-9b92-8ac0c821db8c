import React, { useState } from 'react';

// Define financial data for different time periods
const financialData = {
  vecka: {
    labels: ['<PERSON><PERSON><PERSON>', 'Tis', 'Ons', 'Tor', 'Fre', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>'],
    intäkter: [12000, 15000, 18500, 17000, 22000, 7500, 5000],
    kostnader: [10000, 11500, 13500, 12000, 14500, 4500, 3000]
  },
  månad: {
    labels: ['Vecka 1', 'Vecka 2', 'Vecka 3', 'Vecka 4'],
    intäkter: [52000, 67000, 75500, 50500],
    kostnader: [41000, 51500, 58500, 36500]
  },
  kvartal: {
    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'Maj', 'Jun', 'Jul', 'Aug', 'Sep', 'Okt', 'Nov', 'Dec'],
    intäkter: [152000, 167000, 175500, 183000, 192000, 210000, 205000, 215000, 220000, 225000, 235000, 245000],
    kostnader: [121000, 134500, 138500, 146500, 152500, 165000, 159000, 172000, 176000, 178000, 182500, 187500]
  },
  år: {
    labels: ['2019', '2020', '2021', '2022', '2023', '2024'],
    intäkter: [1520000, 1670000, 1755000, 1950000, 2150000, 2450000],
    kostnader: [1210000, 1345000, 1385000, 1520000, 1650000, 1875000]
  }
};

interface FinancialItemProps {
  label: string;
  amount: string;
  percentage: number;
  percentageType: 'increase' | 'decrease';
}

// Financial item display
const FinancialItem: React.FC<FinancialItemProps> = ({ label, amount, percentage, percentageType }) => {
  return (
    <div className="mb-6">
      <div className="text-gray-600 dark:text-gray-400 mb-1">{label}</div>
      <div className="flex items-center">
        <div className="text-3xl font-bold text-gray-900 dark:text-white mr-3">{amount}</div>
        <div className={`flex items-center ${percentageType === 'increase' ? 'text-green-500' : 'text-red-500'}`}>
          {percentageType === 'increase' ? (
            <svg className="w-4 h-4 mr-1" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M12 5L19 12M19 12H5M19 12V5" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
            </svg>
          ) : (
            <svg className="w-4 h-4 mr-1" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M12 19L5 12M5 12H19M5 12V19" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
            </svg>
          )}
          {`${percentageType === 'increase' ? '+' : '-'}${percentage}%`}
        </div>
      </div>
      <div className="text-sm text-gray-500 dark:text-gray-400">jämfört med föregående period</div>
    </div>
  );
};

interface HighlightItemProps {
  title: string;
  subtitle: string;
  value: string;
  color: string;
}

// Highlight item display
const HighlightItem: React.FC<HighlightItemProps> = ({ title, subtitle, value, color }) => {
  return (
    <div className={`bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700`}>
      <div className={`text-sm ${color} mb-2`}>{title}</div>
      <div className="text-lg font-semibold text-gray-900 dark:text-white mb-1">{subtitle}</div>
      <div className={`${color}`}>{value}</div>
    </div>
  );
};

// Financial chart component
const FinancialChart: React.FC<{ data: any, timeFrame: 'vecka' | 'månad' | 'kvartal' | 'år' }> = ({ data, timeFrame }) => {
  const { labels, intäkter, kostnader } = data[timeFrame];
  const maxValue = Math.max(...intäkter, ...kostnader) * 1.1; // Add 10% padding

  // Calculate profit values
  const profit = intäkter.map((val: number, idx: number) => val - kostnader[idx]);
  
  return (
    <div className="w-full h-full">
      <div className="flex h-full flex-col">
        <div className="flex-1">
          <div className="flex h-full">
            {/* Y-axis labels */}
            <div className="flex flex-col justify-between pr-2 pt-2 pb-6 text-xs text-gray-500 dark:text-gray-400 w-[50px] flex-shrink-0">
              <span>{formatCurrency(maxValue)}</span>
              <span>{formatCurrency(maxValue * 0.75)}</span>
              <span>{formatCurrency(maxValue * 0.5)}</span>
              <span>{formatCurrency(maxValue * 0.25)}</span>
              <span>0</span>
            </div>
            
            {/* Chart area */}
            <div className="relative flex-1 border-b border-l border-gray-200 dark:border-gray-700">
              {/* Horizontal grid lines */}
              <div className="absolute inset-0 flex flex-col justify-between">
                {[0, 1, 2, 3].map((i) => (
                  <div key={i} className="border-b border-dashed border-gray-200 dark:border-gray-700 h-0" />
                ))}
              </div>
              
              {/* Bars */}
              <div className="absolute inset-0 flex">
                {labels.map((label: string, idx: number) => (
                  <div key={idx} className="flex-1 flex items-end justify-center pb-6 group relative" style={{ height: '100%' }}>
                    {/* Income bar */}
                    <div
                      className="w-3 bg-blue-500 dark:bg-blue-600 rounded-t-sm mx-px z-10 group-hover:opacity-90 transition-opacity duration-200"
                      style={{ height: `${(intäkter[idx] / maxValue) * 100}%` }}
                      title={`Intäkter: ${formatCurrency(intäkter[idx])}`}
                    >
                      {/* Tooltip */}
                      <div className="absolute opacity-0 group-hover:opacity-100 bottom-full mb-2 left-1/2 transform -translate-x-1/2 bg-gray-800 text-white text-xs rounded py-1 px-2 whitespace-nowrap transition-opacity duration-200 pointer-events-none z-20">
                        Intäkter: {formatCurrency(intäkter[idx])}
                      </div>
                    </div>
                    
                    {/* Expense bar */}
                    <div
                      className="w-3 bg-red-500 dark:bg-red-600 rounded-t-sm mx-px group-hover:opacity-90 transition-opacity duration-200"
                      style={{ height: `${(kostnader[idx] / maxValue) * 100}%` }}
                      title={`Kostnader: ${formatCurrency(kostnader[idx])}`}
                    >
                      {/* Tooltip */}
                      <div className="absolute opacity-0 group-hover:opacity-100 bottom-full mb-2 left-1/2 transform -translate-x-1/2 bg-gray-800 text-white text-xs rounded py-1 px-2 whitespace-nowrap transition-opacity duration-200 pointer-events-none z-20">
                        Kostnader: {formatCurrency(kostnader[idx])}
                      </div>
                    </div>
                    
                    {/* Profit line */}
                    <div
                      className="absolute w-full h-0.5 bg-green-500 dark:bg-green-600 z-0"
                      style={{ bottom: `${(profit[idx] / maxValue) * 100}%` }}
                      title={`Vinst: ${formatCurrency(profit[idx])}`}
                    >
                      {/* Tooltip */}
                      <div className="absolute opacity-0 group-hover:opacity-100 bottom-full mb-1 right-0 bg-gray-800 text-white text-xs rounded py-1 px-2 whitespace-nowrap transition-opacity duration-200 pointer-events-none z-20">
                        Vinst: {formatCurrency(profit[idx])}
                      </div>
                    </div>
                    
                    {/* X-axis label */}
                    <div className="absolute bottom-0 left-0 right-0 text-center text-xs text-gray-500 dark:text-gray-400">
                      {label}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
        
        {/* Legend */}
        <div className="flex items-center justify-center pt-4 space-x-6 text-sm">
          <div className="flex items-center">
            <div className="w-3 h-3 bg-blue-500 dark:bg-blue-600 rounded-sm mr-2"></div>
            <span className="text-gray-600 dark:text-gray-400">Intäkter</span>
          </div>
          <div className="flex items-center">
            <div className="w-3 h-3 bg-red-500 dark:bg-red-600 rounded-sm mr-2"></div>
            <span className="text-gray-600 dark:text-gray-400">Kostnader</span>
          </div>
          <div className="flex items-center">
            <div className="w-4 h-0.5 bg-green-500 dark:bg-green-600 mr-2"></div>
            <span className="text-gray-600 dark:text-gray-400">Vinst</span>
          </div>
        </div>
      </div>
    </div>
  );
};

// Helper function to format currency
const formatCurrency = (amount: number) => {
  if (amount >= 1000000) {
    return `${(amount / 1000000).toFixed(1)} M kr`;
  } else if (amount >= 1000) {
    return `${(amount / 1000).toFixed(0)} k kr`;
  } else {
    return `${amount} kr`;
  }
};

const EconomicOverview: React.FC = () => {
  const [timeFrame, setTimeFrame] = useState<'vecka' | 'månad' | 'kvartal' | 'år'>('månad');

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white">Ekonomisk översikt</h2>
        <button className="text-blue-600 dark:text-blue-400 hover:underline flex items-center">
          Visa detaljer
          <svg className="w-4 h-4 ml-1" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M9 5L16 12L9 19" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
          </svg>
        </button>
      </div>

      <div className="inline-flex rounded-md shadow-sm mb-8">
        <button
          type="button"
          className={`px-4 py-2 text-sm font-medium rounded-l-lg ${
            timeFrame === 'vecka'
              ? 'bg-gray-200 dark:bg-gray-700 text-gray-900 dark:text-white'
              : 'bg-white dark:bg-gray-800 text-gray-500 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700'
          }`}
          onClick={() => setTimeFrame('vecka')}
        >
          Vecka
        </button>
        <button
          type="button"
          className={`px-4 py-2 text-sm font-medium ${
            timeFrame === 'månad'
              ? 'bg-gray-200 dark:bg-gray-700 text-gray-900 dark:text-white'
              : 'bg-white dark:bg-gray-800 text-gray-500 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700'
          }`}
          onClick={() => setTimeFrame('månad')}
        >
          Månad
        </button>
        <button
          type="button"
          className={`px-4 py-2 text-sm font-medium ${
            timeFrame === 'kvartal'
              ? 'bg-gray-200 dark:bg-gray-700 text-gray-900 dark:text-white'
              : 'bg-white dark:bg-gray-800 text-gray-500 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700'
          }`}
          onClick={() => setTimeFrame('kvartal')}
        >
          Kvartal
        </button>
        <button
          type="button"
          className={`px-4 py-2 text-sm font-medium rounded-r-lg ${
            timeFrame === 'år'
              ? 'bg-gray-200 dark:bg-gray-700 text-gray-900 dark:text-white'
              : 'bg-white dark:bg-gray-800 text-gray-500 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700'
          }`}
          onClick={() => setTimeFrame('år')}
        >
          År
        </button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2">
          <FinancialItem 
            label="Intäkter" 
            amount="245 000 kr" 
            percentage={12} 
            percentageType="increase" 
          />
          <FinancialItem 
            label="Kostnader" 
            amount="187 500 kr" 
            percentage={8} 
            percentageType="increase" 
          />
          <FinancialItem 
            label="Vinst" 
            amount="57 500 kr" 
            percentage={24} 
            percentageType="increase" 
          />
        </div>
        <div className="lg:col-span-1 flex flex-col">
          <div className="text-lg font-semibold text-gray-900 dark:text-white mb-2">Finansiell graf</div>
          <div className="w-full h-[370px]">
            <FinancialChart data={financialData} timeFrame={timeFrame} />
          </div>
        </div>
      </div>

      <div className="mt-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Ekonomiska höjdpunkter</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <HighlightItem 
            title="Största projektet" 
            subtitle="Köksinredning Villa Sjövik" 
            value="85 000 kr" 
            color="text-blue-600 dark:text-blue-400" 
          />
          <HighlightItem 
            title="Högsta vinstmarginal" 
            subtitle="Badrumsrenovering Strandvägen" 
            value="32% marginal" 
            color="text-green-600 dark:text-green-400" 
          />
          <HighlightItem 
            title="Kommande betalningar" 
            subtitle="3 fakturor" 
            value="120 000 kr totalt" 
            color="text-amber-600 dark:text-amber-400" 
          />
        </div>
      </div>
    </div>
  );
};

export default EconomicOverview; 