'use client';

import React from 'react';
import { SidebarDemo } from '../components/ui/sidebar-demo';

export default function SidebarDemoPage() {
  return (
    <div className="p-4 min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">Sidebar Component Demo</h1>
        <p className="text-gray-600 dark:text-gray-400">
          An interactive demo of the responsive sidebar component with both desktop and mobile views.
        </p>
      </div>
      
      <SidebarDemo />
      
      <div className="mt-8 p-6 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">Usage Instructions</h2>
        <ul className="list-disc pl-5 space-y-2 text-gray-600 dark:text-gray-400">
          <li>On desktop, hover over the sidebar to expand it</li>
          <li>On mobile, use the menu icon to toggle the sidebar</li>
          <li>The sidebar is fully responsive and adapts to both desktop and mobile views</li>
          <li>The component is built with Framer Motion for smooth animations</li>
          <li>All icons are from Lucide React library</li>
        </ul>
      </div>
    </div>
  );
} 