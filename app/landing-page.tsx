'use client'

import React from 'react';
import Link from 'next/link';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>hart3, <PERSON>, FileText, <PERSON><PERSON><PERSON>, CheckCircle, Star } from 'lucide-react';
import { ModusLogo } from '@/components/ui/modus-logo';

export function LandingPage() {
  return (
    <div className="bg-black text-white font-sans">
      <div className="relative min-h-screen overflow-hidden">
        {/* Spline 3D background */}
        <div className="fixed inset-0 z-0">
          <iframe
            src="https://my.spline.design/claritystream-a72K0KUwFoZV82QBzvu52Kai/"
            frameBorder="0"
            width="100%"
            height="100%"
            className="w-full h-full"
          />
        </div>

        {/* Glass floating navbar */}
        <nav className="fixed top-6 left-1/2 transform -translate-x-1/2 z-50 bg-opacity-5 border-opacity-10 bg-white border-white border rounded-full pt-3 pr-4 pb-3 pl-4 shadow-xl backdrop-blur-md">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <ModusLogo size="sm" variant="light" />
              <span className="ml-2 text-sm font-medium">Modus</span>
            </div>
            <div className="hidden md:flex items-center space-x-6 text-xs text-gray-300 ml-8">
              <a href="#features" className="hover:text-white transition-colors">Features</a>
              <a href="#pricing" className="hover:text-white transition-colors">Pricing</a>
              <a href="#about" className="hover:text-white transition-colors">About</a>
              <a href="#contact" className="hover:text-white transition-colors">Contact</a>
            </div>
            <div className="flex items-center space-x-3 ml-8">
              <Link href="/auth/login" className="hidden md:inline-block text-xs font-medium hover:text-white transition-colors">
                Login
              </Link>
              <Link href="/auth/register" className="hover:bg-gray-200 transition-colors text-xs font-medium text-black bg-white rounded-full pt-1.5 pr-3 pb-1.5 pl-3">
                Start Free Trial
              </Link>
            </div>
          </div>
        </nav>


        {/* Hero content */}
        <div className="relative z-10 flex flex-col items-center justify-center px-6 pt-32 pb-32 md:pt-40 md:pb-40 text-center min-h-screen">
          <div className="absolute top-1/2 left-1/2 w-[600px] h-[600px] -translate-x-1/2 -translate-y-1/2 bg-white opacity-5 blur-[100px] rounded-full pointer-events-none" />

          <div className="flex items-center justify-center mb-6">
            <ModusLogo size="xl" variant="light" className="mr-4" />
            <span className="text-2xl font-bold text-white">Modus</span>
          </div>

          <span className="px-3 py-1 text-xs font-medium text-white bg-white bg-opacity-10 backdrop-blur-sm rounded-full mb-8 border border-white border-opacity-20">
            Enterprise Resource Planning
          </span>

          <h1 className="md:text-6xl max-w-4xl leading-tight text-4xl font-medium tracking-tighter">
            Modern ERP for the digital age
          </h1>

          <p className="md:text-xl max-w-2xl text-lg text-neutral-300 mt-6">
            Streamline your business operations with our comprehensive ERP solution.
            Manage customers, projects, invoices, and employees all in one elegant platform.
          </p>

          <div className="mt-12 flex flex-col sm:flex-row gap-4">
            <Link href="/auth/register" className="px-8 py-3 bg-white text-black font-medium rounded-full hover:bg-gray-200 transition-all duration-300 shadow-lg hover:shadow-xl">
              Start Free Trial
            </Link>
            <Link href="/demo" className="px-8 py-3 bg-white bg-opacity-10 backdrop-blur-sm text-white font-medium rounded-full hover:bg-opacity-20 transition-all duration-300 border border-white border-opacity-20">
              View Demo
            </Link>
          </div>

          <div className="mt-20 flex justify-center">
            <div className="w-[768px] h-[400px] bg-black bg-opacity-40 backdrop-blur-md rounded-lg shadow-2xl border border-white border-opacity-10 overflow-hidden">
              <div className="h-8 border-b border-white border-opacity-10 flex items-center px-4">
                <div className="flex space-x-2">
                  <div className="w-3 h-3 rounded-full bg-white bg-opacity-30" />
                  <div className="w-3 h-3 rounded-full bg-white bg-opacity-30" />
                  <div className="w-3 h-3 rounded-full bg-white bg-opacity-30" />
                </div>
              </div>
              <div className="p-4 opacity-70">
                {/* ERP Dashboard Preview */}
                <div className="flex space-x-4">
                  <div className="w-48 h-full bg-white bg-opacity-5 backdrop-blur-sm rounded p-3 border border-white border-opacity-5">
                    <div className="w-full h-4 bg-white bg-opacity-20 rounded mb-3" />
                    <div className="w-3/4 h-3 bg-white bg-opacity-15 rounded mb-4" />
                    <div className="space-y-2">
                      <div className="w-full h-8 bg-white bg-opacity-10 rounded flex items-center px-2">
                        <Users className="w-3 h-3 mr-2 opacity-60" />
                        <span className="text-xs opacity-60">Customers</span>
                      </div>
                      <div className="w-full h-8 bg-white bg-opacity-10 rounded flex items-center px-2">
                        <FileText className="w-3 h-3 mr-2 opacity-60" />
                        <span className="text-xs opacity-60">Invoices</span>
                      </div>
                      <div className="w-full h-8 bg-white bg-opacity-10 rounded flex items-center px-2">
                        <BarChart3 className="w-3 h-3 mr-2 opacity-60" />
                        <span className="text-xs opacity-60">Projects</span>
                      </div>
                    </div>
                  </div>
                  <div className="flex-1 bg-white bg-opacity-5 backdrop-blur-sm rounded p-3 border border-white border-opacity-5">
                    <div className="w-1/3 h-4 bg-white bg-opacity-15 rounded mb-3" />
                    <div className="w-full h-[320px] bg-white bg-opacity-10 rounded flex items-center justify-center">
                      <div className="grid grid-cols-2 gap-4 w-full max-w-xs">
                        <div className="bg-white bg-opacity-10 rounded p-3 text-center">
                          <div className="text-lg font-bold opacity-80">247</div>
                          <div className="text-xs opacity-60">Customers</div>
                        </div>
                        <div className="bg-white bg-opacity-10 rounded p-3 text-center">
                          <div className="text-lg font-bold opacity-80">1,432</div>
                          <div className="text-xs opacity-60">Invoices</div>
                        </div>
                        <div className="bg-white bg-opacity-10 rounded p-3 text-center">
                          <div className="text-lg font-bold opacity-80">89</div>
                          <div className="text-xs opacity-60">Projects</div>
                        </div>
                        <div className="bg-white bg-opacity-10 rounded p-3 text-center">
                          <div className="text-lg font-bold opacity-80">24</div>
                          <div className="text-xs opacity-60">Employees</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>


      {/* Features Section */}
      <section id="features" className="relative z-10 py-20 px-6 bg-black bg-opacity-80 backdrop-blur-sm">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-16">
            <span className="px-3 py-1 text-xs font-medium text-white bg-white bg-opacity-10 backdrop-blur-sm rounded-full mb-8 border border-white border-opacity-20 inline-block">
              Features
            </span>
            <h2 className="text-3xl md:text-4xl font-medium text-white mb-6">
              Everything you need to run your business
            </h2>
            <p className="text-lg text-neutral-300 max-w-2xl mx-auto">
              Our comprehensive ERP solution provides all the tools you need to manage and grow your business efficiently.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="bg-white bg-opacity-5 backdrop-blur-sm rounded-lg p-6 border border-white border-opacity-10">
              <div className="w-12 h-12 bg-white bg-opacity-10 rounded-full flex items-center justify-center mb-5">
                <Users className="w-6 h-6 text-white" />
              </div>
              <h3 className="text-lg font-medium text-white mb-3">Customer Management</h3>
              <p className="text-neutral-300 text-sm">
                Keep track of all your customers, their contact information, and interaction history in one centralized location.
              </p>
            </div>

            <div className="bg-white bg-opacity-5 backdrop-blur-sm rounded-lg p-6 border border-white border-opacity-10">
              <div className="w-12 h-12 bg-white bg-opacity-10 rounded-full flex items-center justify-center mb-5">
                <FileText className="w-6 h-6 text-white" />
              </div>
              <h3 className="text-lg font-medium text-white mb-3">Invoice & Billing</h3>
              <p className="text-neutral-300 text-sm">
                Create professional invoices, track payments, and manage your billing process with ease.
              </p>
            </div>

            <div className="bg-white bg-opacity-5 backdrop-blur-sm rounded-lg p-6 border border-white border-opacity-10">
              <div className="w-12 h-12 bg-white bg-opacity-10 rounded-full flex items-center justify-center mb-5">
                <BarChart3 className="w-6 h-6 text-white" />
              </div>
              <h3 className="text-lg font-medium text-white mb-3">Project Tracking</h3>
              <p className="text-neutral-300 text-sm">
                Monitor project progress, deadlines, and resource allocation to ensure successful delivery.
              </p>
            </div>

            <div className="bg-white bg-opacity-5 backdrop-blur-sm rounded-lg p-6 border border-white border-opacity-10">
              <div className="w-12 h-12 bg-white bg-opacity-10 rounded-full flex items-center justify-center mb-5">
                <Settings className="w-6 h-6 text-white" />
              </div>
              <h3 className="text-lg font-medium text-white mb-3">Employee Management</h3>
              <p className="text-neutral-300 text-sm">
                Manage your team, track performance, and handle HR processes efficiently.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="relative z-10 py-20 px-6 bg-black bg-opacity-60 backdrop-blur-sm">
        <div className="max-w-4xl mx-auto text-center">
          <span className="px-3 py-1 text-xs font-medium text-white bg-white bg-opacity-10 backdrop-blur-sm rounded-full mb-8 border border-white border-opacity-20 inline-block">
            Testimonials
          </span>
          <h2 className="text-3xl md:text-4xl font-medium text-white mb-12">
            Trusted by businesses worldwide
          </h2>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="bg-white bg-opacity-5 backdrop-blur-sm rounded-lg p-6 border border-white border-opacity-10">
              <div className="flex items-center mb-4">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="w-4 h-4 text-yellow-400 fill-current" />
                ))}
              </div>
              <p className="text-neutral-300 text-sm mb-4">
                "Modus has transformed how we manage our business. The interface is intuitive and the features are exactly what we needed."
              </p>
              <div className="text-white font-medium text-sm">Sarah Johnson</div>
              <div className="text-neutral-400 text-xs">CEO, TechCorp</div>
            </div>

            <div className="bg-white bg-opacity-5 backdrop-blur-sm rounded-lg p-6 border border-white border-opacity-10">
              <div className="flex items-center mb-4">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="w-4 h-4 text-yellow-400 fill-current" />
                ))}
              </div>
              <p className="text-neutral-300 text-sm mb-4">
                "The invoice management system alone has saved us hours every week. Highly recommended for any growing business."
              </p>
              <div className="text-white font-medium text-sm">Michael Chen</div>
              <div className="text-neutral-400 text-xs">Founder, StartupXYZ</div>
            </div>

            <div className="bg-white bg-opacity-5 backdrop-blur-sm rounded-lg p-6 border border-white border-opacity-10">
              <div className="flex items-center mb-4">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="w-4 h-4 text-yellow-400 fill-current" />
                ))}
              </div>
              <p className="text-neutral-300 text-sm mb-4">
                "Clean, modern, and powerful. Modus gives us everything we need to run our operations smoothly."
              </p>
              <div className="text-white font-medium text-sm">Emma Davis</div>
              <div className="text-neutral-400 text-xs">Operations Manager, BuildCo</div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="relative z-10 py-20 px-6 bg-black bg-opacity-80 backdrop-blur-sm">
        <div className="max-w-2xl mx-auto text-center">
          <h2 className="text-3xl md:text-4xl font-medium text-white mb-6">
            Ready to get started?
          </h2>
          <p className="text-lg text-neutral-300 mb-8">
            Join thousands of businesses already using Modus to streamline their operations.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/auth/register" className="px-8 py-3 bg-white text-black font-medium rounded-full hover:bg-gray-200 transition-all duration-300 shadow-lg hover:shadow-xl">
              Start Free Trial
            </Link>
            <Link href="/contact" className="px-8 py-3 bg-white bg-opacity-10 backdrop-blur-sm text-white font-medium rounded-full hover:bg-opacity-20 transition-all duration-300 border border-white border-opacity-20">
              Contact Sales
            </Link>
          </div>
        </div>
      </section>


      {/* Footer */}
      <footer className="relative z-10 py-12 px-6 bg-black bg-opacity-90 backdrop-blur-sm border-t border-white border-opacity-10">
        <div className="max-w-6xl mx-auto">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="mb-6 md:mb-0">
              <div className="flex items-center mb-4">
                <ModusLogo size="md" variant="light" className="mr-2" />
                <span className="text-lg font-medium text-white">Modus</span>
              </div>
              <p className="text-neutral-400 text-sm">© 2024 Modus ERP. All rights reserved.</p>
            </div>

            <div className="flex flex-wrap justify-center md:justify-end gap-6">
              <Link href="#features" className="text-sm text-neutral-300 hover:text-white transition-colors">Features</Link>
              <Link href="#pricing" className="text-sm text-neutral-300 hover:text-white transition-colors">Pricing</Link>
              <Link href="#about" className="text-sm text-neutral-300 hover:text-white transition-colors">About</Link>
              <Link href="#contact" className="text-sm text-neutral-300 hover:text-white transition-colors">Contact</Link>
              <Link href="/privacy" className="text-sm text-neutral-300 hover:text-white transition-colors">Privacy</Link>
              <Link href="/terms" className="text-sm text-neutral-300 hover:text-white transition-colors">Terms</Link>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
