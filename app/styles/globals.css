/* Add styles for Google Layout */

.google-page {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #fff;
}

.google-header {
  display: flex;
  align-items: center;
  padding: 0.75rem 1.5rem;
  border-bottom: 1px solid #e5e7eb;
}

.google-logo {
  display: flex;
  align-items: center;
  margin-right: 1.5rem;
}

.google-search-bar {
  flex: 1;
  max-width: 720px;
  display: flex;
  align-items: center;
  height: 46px;
  background-color: #f1f3f4;
  border-radius: 8px;
  padding: 0 16px;
}

.google-search-bar:focus-within {
  background-color: #fff;
  box-shadow: 0 1px 6px rgba(32, 33, 36, 0.28);
}

.google-search-icon {
  color: #5f6368;
  margin-right: 10px;
}

.google-voice-icon {
  color: #5f6368;
  margin-left: 10px;
}

.google-search-bar input {
  flex: 1;
  border: none;
  background: transparent;
  font-size: 1rem;
  color: #202124;
  outline: none;
}

.google-tabs {
  display: flex;
  border-bottom: 1px solid #e5e7eb;
  padding: 0 1.5rem;
}

.google-tab {
  display: flex;
  align-items: center;
  padding: 0.75rem 1.25rem;
  font-size: 0.875rem;
  color: #5f6368;
  cursor: pointer;
  border-bottom: 3px solid transparent;
  transition: color 0.2s, border-color 0.2s;
}

.google-tab.active {
  color: #1a73e8;
  border-bottom-color: #1a73e8;
}

.google-tab:hover:not(.active) {
  color: #202124;
  background-color: rgba(0, 0, 0, 0.03);
}

.google-tab-icon {
  margin-right: 8px;
}

.google-content {
  flex: 1;
  padding: 1.5rem;
  background-color: #f8f9fa;
}

/* For Google UI Components */
.google-card {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);
  margin-bottom: 1rem;
  overflow: hidden;
}

.google-card-header {
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #e5e7eb;
}

.google-card-title {
  font-size: 1rem;
  font-weight: 500;
  color: #202124;
}

.google-card-content {
  padding: 1.5rem;
}

.google-actions {
  display: flex;
  gap: 0.5rem;
}

.google-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  font-size: 0.875rem;
  font-weight: 500;
  padding: 0.5rem 1rem;
  transition: all 0.2s;
}

.google-button-primary {
  background-color: #1a73e8;
  color: white;
}

.google-button-primary:hover {
  background-color: #1765cc;
  box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3);
}

.google-button-outline {
  border: 1px solid #dadce0;
  color: #1a73e8;
  background-color: white;
}

.google-button-outline:hover {
  background-color: rgba(26, 115, 232, 0.04);
  box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3);
}

.google-chip {
  display: inline-flex;
  align-items: center;
  height: 24px;
  border-radius: 12px;
  padding: 0 10px;
  font-size: 0.75rem;
  color: #5f6368;
  background-color: #f1f3f4;
}

.google-badge {
  display: inline-flex;
  align-items: center;
  border-radius: 4px;
  padding: 2px 8px;
  font-size: 0.75rem;
  font-weight: 500;
}

.google-badge-success {
  color: #137333;
  background-color: #e6f4ea;
}

.google-badge-info {
  color: #1967d2;
  background-color: #e8f0fe;
}

.google-badge-warning {
  color: #ea8600;
  background-color: #fef7e0;
}

.google-badge-error {
  color: #d93025;
  background-color: #fce8e6;
}

.google-text-blue {
  color: #1a73e8;
}

/* Google Progress Bar */
.google-progress {
  height: 4px;
  border-radius: 2px;
  background-color: #e6e6e6;
  overflow: hidden;
}

.google-progress-bar {
  height: 100%;
  background-color: #1a73e8;
  transition: width 0.2s;
}

/* Google Divider */
.google-divider {
  height: 1px;
  background-color: #e5e7eb;
  margin: 1rem 0;
} 