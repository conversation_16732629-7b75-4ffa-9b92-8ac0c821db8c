"use client";

export default function TestEnv() {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

  return (
    <div style={{ padding: '20px' }}>
      <h1>Environment Variables Test</h1>
      <p>Supabase URL: {supabaseUrl ? 'Set' : 'Not set'}</p>
      <p>Supabase Key: {supabaseKey ? 'Set' : 'Not set'}</p>
      <p>URL: {supabaseUrl}</p>
      <p>Key: {supabaseKey ? `${supabaseKey.substring(0, 20)}...` : 'None'}</p>
    </div>
  );
} 