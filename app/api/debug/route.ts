import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase/supabase';

export async function GET(request: NextRequest) {
  try {
    console.log('Debug: Testing Supabase connection...');
    
    // Test basic connection
    const { data, error } = await supabase
      .from('customers')
      .select('id, name')
      .limit(3);

    console.log('Debug: Supabase response:', { data, error });

    if (error) {
      console.error('Debug: Supabase error:', error);
      return NextResponse.json({ 
        error: error.message,
        details: error,
        env: {
          url: process.env.NEXT_PUBLIC_SUPABASE_URL ? 'SET' : 'NOT SET',
          key: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY ? 'SET' : 'NOT SET'
        }
      }, { status: 500 });
    }

    return NextResponse.json({ 
      success: true,
      data,
      env: {
        url: process.env.NEXT_PUBLIC_SUPABASE_URL ? 'SET' : 'NOT SET',
        key: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY ? 'SET' : 'NOT SET'
      }
    });

  } catch (error) {
    console.error('Debug: Catch error:', error);
    return NextResponse.json({ 
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error',
      env: {
        url: process.env.NEXT_PUBLIC_SUPABASE_URL ? 'SET' : 'NOT SET',
        key: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY ? 'SET' : 'NOT SET'
      }
    }, { status: 500 });
  }
} 