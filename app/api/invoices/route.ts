import { NextRequest, NextResponse } from 'next/server';
import { createServerSupabaseClient } from '@/lib/supabase/supabase-server';

export async function GET(request: NextRequest) {
  try {
    console.log('Fetching invoices from API...');
    
    const supabase = await createServerSupabaseClient();
    
    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      console.error('Auth error:', authError);
      return NextResponse.json({ 
        error: 'Authentication required' 
      }, { status: 401 });
    }
    
    console.log('User authenticated:', user.id);
    
    // Fetch invoices for the authenticated user
    const { data: invoices, error: invoicesError } = await supabase
      .from('invoices')
      .select(`
        id,
        invoice_number,
        customer_name,
        customer_email,
        customer_phone,
        customer_address,
        project_name,
        description,
        line_items,
        subtotal,
        vat_amount,
        total_amount,
        vat_rate,
        currency,
        status,
        due_date,
        issue_date,
        payment_date,
        payment_terms,
        notes,
        created_at,
        updated_at
      `)
      .eq('user_id', user.id)
      .order('created_at', { ascending: false });
    
    if (invoicesError) {
      console.error('Invoices query error:', invoicesError);
      return NextResponse.json({ 
        error: 'Failed to fetch invoices', 
        details: invoicesError.message 
      }, { status: 500 });
    }
    
    console.log('Invoices fetched successfully:', invoices?.length || 0, 'records');
    
    return NextResponse.json({
      success: true,
      data: invoices || []
    });
    
  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json({ 
      error: 'Internal server error', 
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    console.log('Creating new invoice...');
    
    const supabase = await createServerSupabaseClient();
    
    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      console.error('Auth error:', authError);
      return NextResponse.json({ 
        error: 'Authentication required' 
      }, { status: 401 });
    }
    
    const body = await request.json();
    
    // Add user_id to the invoice data
    const invoiceData = {
      ...body,
      user_id: user.id
    };
    
    const { data: newInvoice, error: createError } = await supabase
      .from('invoices')
      .insert(invoiceData)
      .select()
      .single();
    
    if (createError) {
      console.error('Create invoice error:', createError);
      return NextResponse.json({ 
        error: 'Failed to create invoice', 
        details: createError.message 
      }, { status: 500 });
    }
    
    console.log('Invoice created successfully:', newInvoice.id);
    
    return NextResponse.json({
      success: true,
      data: newInvoice
    });
    
  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json({ 
      error: 'Internal server error', 
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
