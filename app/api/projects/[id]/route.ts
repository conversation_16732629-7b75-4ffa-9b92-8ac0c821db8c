import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
    );
    
    const { status, ...updateData } = await request.json();
    const resolvedParams = await params;
    const projectId = resolvedParams.id;

    console.log('Updating project:', projectId, 'to status:', status);

    // Update the project status
    const { data: projects, error: updateError } = await supabase
      .from('projects')
      .update({ status, ...updateData })
      .eq('id', projectId)
      .select('*');

    if (updateError) {
      console.error('Error updating project:', updateError);
      return NextResponse.json({ error: updateError.message }, { status: 500 });
    }

    console.log('Update result:', projects);

    if (!projects || projects.length === 0) {
      console.log('No projects returned from update');
      return NextResponse.json({ error: 'Project not found' }, { status: 404 });
    }

    const project = projects[0];

    // If project is marked as completed, create an invoice
    if (status === 'completed') {
      console.log('Creating invoice for completed project');
      
      // Generate invoice number
      const { data: lastInvoice } = await supabase
        .from('invoices')
        .select('invoice_number')
        .order('created_at', { ascending: false })
        .limit(1);

      let invoiceNumber = 'INV-2025-001';
      if (lastInvoice && lastInvoice.length > 0) {
        const lastNumber = parseInt(lastInvoice[0].invoice_number.split('-')[2]);
        invoiceNumber = `INV-2025-${String(lastNumber + 1).padStart(3, '0')}`;
      }

      // Calculate invoice amount (use project budget or estimated value)
      const invoiceAmount = project.budget || project.estimated_hours * 1000 || 50000; // Default fallback

      // Create invoice
      const { data: invoice, error: invoiceError } = await supabase
        .from('invoices')
        .insert({
          invoice_number: invoiceNumber,
          customer_id: project.customer_id,
          project_id: projectId,
          status: 'draft',
          issue_date: new Date().toISOString().split('T')[0],
          due_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 30 days from now
          total_amount: invoiceAmount,
          notes: `Faktura för avslutat projekt: ${project.name}`
        })
        .select()
        .single();

      if (invoiceError) {
        console.error('Error creating invoice:', invoiceError);
        // Don't fail the project update if invoice creation fails
        return NextResponse.json({ 
          project, 
          warning: 'Project updated but invoice creation failed: ' + invoiceError.message 
        });
      }

      console.log('Invoice created:', invoice);

      return NextResponse.json({ 
        project, 
        invoice,
        message: 'Project marked as completed and invoice created successfully' 
      });
    }

    return NextResponse.json({ project });

  } catch (error: any) {
    console.error('Error in project update API:', error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
    );
    const resolvedParams = await params;
    const projectId = resolvedParams.id;

    const { data: project, error } = await supabase
      .from('projects')
      .select('*')
      .eq('id', projectId)
      .single();

    if (error) {
      console.error('Error fetching project:', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    return NextResponse.json({ project });

  } catch (error: any) {
    console.error('Error in project fetch API:', error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
} 