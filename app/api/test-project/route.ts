import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

export async function GET() {
  try {
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
    );

    console.log('Testing Supabase connection...');
    console.log('URL:', process.env.NEXT_PUBLIC_SUPABASE_URL);
    console.log('Key exists:', !!process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY);

    // Test basic query
    const { data: projects, error } = await supabase
      .from('projects')
      .select('id, name, status')
      .limit(5);

    if (error) {
      console.error('Supabase error:', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    console.log('Projects found:', projects);

    return NextResponse.json({ 
      message: 'Supabase connection successful',
      projects: projects
    });

  } catch (error: any) {
    console.error('Error in test API:', error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}

export async function PUT() {
  try {
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
    );

    const projectId = 'e8d3c533-c515-4502-a303-9ccfb5ea4964';
    
    console.log('Testing project update for:', projectId);

    // Test update
    const { data: projects, error } = await supabase
      .from('projects')
      .update({ status: 'completed' })
      .eq('id', projectId)
      .select('*');

    if (error) {
      console.error('Update error:', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    console.log('Update result:', projects);

    return NextResponse.json({ 
      message: 'Update test successful',
      projects: projects
    });

  } catch (error: any) {
    console.error('Error in test update:', error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
} 