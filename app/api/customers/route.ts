import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase/supabase';

export async function GET(request: NextRequest) {
  try {
    const { data: customers, error } = await supabase
      .from('customers')
      .select('id, name, company_name, email, phone')
      .order('name', { ascending: true });

    if (error) {
      console.error('Error fetching customers:', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    // Transform data to include display name
    const transformedCustomers = customers?.map(customer => ({
      ...customer,
      display_name: customer.company_name ? 
        `${customer.name} (${customer.company_name})` : 
        customer.name
    })) || [];

    return NextResponse.json({ customers: transformedCustomers });

  } catch (error) {
    console.error('Error in customers API:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
} 