import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabaseClient } from '@/lib/supabase/supabase-server'

export async function GET(request: NextRequest) {
  try {
    const supabase = await createServerSupabaseClient()

    // Parallel data fetching for better performance
    const [
      projectsResult,
      estimatesResult,
      customersResult,
      invoicesResult
    ] = await Promise.all([
      supabase
        .from('projects')
        .select('id, name, status, created_at, updated_at, end_date, description')
        .order('created_at', { ascending: false })
        .limit(10),
      
      supabase
        .from('estimates')
        .select('id, project_name, status, total_amount, created_at')
        .order('created_at', { ascending: false })
        .limit(10),
      
      supabase
        .from('customers')
        .select('id, name, created_at', { count: 'exact' }),
      
      supabase
        .from('invoices')
        .select('id, total_amount, status, created_at')
        .order('created_at', { ascending: false })
        .limit(5)
    ])

    // Check for errors
    if (projectsResult.error) throw projectsResult.error
    if (estimatesResult.error) throw estimatesResult.error
    if (customersResult.error) throw customersResult.error
    if (invoicesResult.error) throw invoicesResult.error

    const projects = projectsResult.data || []
    const estimates = estimatesResult.data || []
    const customers = customersResult.data || []
    const invoices = invoicesResult.data || []

    // Calculate project stats
    const projectStats = {
      total: projects.length,
      active: projects.filter(p => p.status === 'active').length,
      completed: projects.filter(p => p.status === 'completed').length,
      planning: projects.filter(p => p.status === 'planning').length,
      recent: projects.slice(0, 3)
    }

    // Calculate estimate stats
    const estimateStats = {
      total: estimates.length,
      pending: estimates.filter(e => e.status === 'pending').length,
      accepted: estimates.filter(e => e.status === 'accepted').length,
      draft: estimates.filter(e => e.status === 'draft').length,
      totalValue: estimates.reduce((sum, e) => sum + (e.total_amount || 0), 0),
      recent: estimates.slice(0, 2)
    }

    // Calculate invoice stats
    const invoiceStats = {
      total: invoices.length,
      totalAmount: invoices.reduce((sum, inv) => sum + (parseFloat(inv.total_amount?.toString() || '0')), 0),
      paid: invoices.filter(inv => inv.status === 'paid').length,
      pending: invoices.filter(inv => inv.status === 'sent').length
    }

    // Create recent activity
    const recentActivity = []
    
    // Add recent estimates
    estimates.slice(0, 2).forEach(estimate => {
      recentActivity.push({
        id: estimate.id,
        type: 'estimate',
        title: `Ny offert: ${estimate.project_name}`,
        description: `Värde: ${(estimate.total_amount / 1000).toFixed(1)} KSEK`,
        time: estimate.created_at,
        status: estimate.status,
        icon: 'FileText'
      })
    })

    // Add recent projects
    projects.slice(0, 2).forEach(project => {
      recentActivity.push({
        id: project.id,
        type: 'project',
        title: project.status === 'completed' ? `Projekt avslutat: ${project.name}` : `Projekt uppdaterat: ${project.name}`,
        description: project.description || 'Ingen beskrivning',
        time: project.updated_at || project.created_at,
        status: project.status,
        icon: project.status === 'completed' ? 'CheckCircle' : 'Edit'
      })
    })

    // Sort activity by time
    recentActivity.sort((a, b) => new Date(b.time).getTime() - new Date(a.time).getTime())

    // Mock staff allocation (replace with real data when available)
    const staffAllocation = {
      thisWeek: Math.floor(Math.random() * 20) + 75,
      nextWeek: Math.floor(Math.random() * 20) + 70,
      twoWeeksOut: Math.floor(Math.random() * 20) + 60,
      overtimeWarnings: Math.random() > 0.7 ? ['Anna Andersson'] : []
    }

    const dashboardData = {
      projects: projectStats,
      estimates: estimateStats,
      invoices: invoiceStats,
      customers: {
        total: customersResult.count || 0
      },
      recentActivity: recentActivity.slice(0, 4),
      staffAllocation,
      lastUpdated: new Date().toISOString()
    }

    return NextResponse.json(dashboardData)

  } catch (error) {
    console.error('Dashboard API Error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch dashboard data' },
      { status: 500 }
    )
  }
}
