import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase/supabase';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status');
    const search = searchParams.get('search');
    const dateFrom = searchParams.get('dateFrom');
    const dateTo = searchParams.get('dateTo');

    // Start building the query
    let query = supabase
      .from('estimates')
      .select(`
        id,
        estimate_number,
        customer_id,
        project_name,
        description,
        date,
        valid_until,
        status,
        total_amount,
        currency,
        created_at,
        updated_at,
        customers:customer_id (
          id,
          name,
          company_name
        )
      `)
      .order('created_at', { ascending: false });

    // Apply filters
    if (status && status !== 'all') {
      query = query.eq('status', status);
    }

    if (search) {
      query = query.or(`project_name.ilike.%${search}%,estimate_number.ilike.%${search}%,customers.name.ilike.%${search}%`);
    }

    if (dateFrom) {
      query = query.gte('date', dateFrom);
    }

    if (dateTo) {
      query = query.lte('date', dateTo);
    }

    const { data: estimates, error } = await query;

    if (error) {
      console.error('Supabase query error:', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    // Transform the data to match the frontend expectations
    const transformedEstimates = estimates?.map(estimate => {
      // Handle customer data - it should be a single object, not an array
      const customer = Array.isArray(estimate.customers) ? estimate.customers[0] : estimate.customers;
      
      return {
        id: estimate.id, // Use the UUID as the primary ID for API calls
        estimate_number: estimate.estimate_number, // Display this as the user-friendly number
        customer_name: customer?.name || customer?.company_name || 'Unknown Customer',
        project_name: estimate.project_name,
        created_at: estimate.created_at,
        date: estimate.date, // Include the estimate date for waiting days calculation
        total_amount: estimate.total_amount || 0,
        status: estimate.status,
        valid_until: estimate.valid_until,
        updated_at: estimate.updated_at
      };
    }) || [];

    return NextResponse.json(transformedEstimates);

  } catch (error) {
    console.error('Unexpected error in estimates API:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    console.log('=== ESTIMATE CREATION DEBUG ===');
    console.log('Request body:', body);
    console.log('Line items from request:', body.line_items);
    
    // Use provided estimate number or generate one
    let estimateNumber = body.estimate_number;
    if (!estimateNumber) {
      // Generate estimate number
      const { data: latestEstimate } = await supabase
        .from('estimates')
        .select('estimate_number')
        .order('created_at', { ascending: false })
        .limit(1);

      let nextNumber = 1;
      if (latestEstimate && latestEstimate.length > 0) {
        const lastNumber = latestEstimate[0].estimate_number.split('-').pop();
        nextNumber = parseInt(lastNumber) + 1;
      }

      estimateNumber = `EST-${new Date().getFullYear()}-${nextNumber.toString().padStart(3, '0')}`;
    }

    const estimateData = {
      estimate_number: estimateNumber,
      customer_id: body.customer_id,
      project_name: body.project_name,
      description: body.description,
      date: body.date || new Date().toISOString().split('T')[0],
      valid_until: body.valid_until,
      payment_terms: body.payment_terms || '30 dagar',
      status: body.status || 'draft',
      tax_type: body.tax_type || '25%',
      tax_amount: body.tax_amount || 0,
      subtotal: body.subtotal || 0,
      total_amount: body.total_amount || 0,
      rot_avdrag: body.rot_avdrag || false,
      rot_personnummer: body.rot_personnummer,
      intro_text: body.intro_text,
      closing_text: body.closing_text,
      currency: body.currency || 'SEK',
      created_by: body.created_by
    };

    console.log('Creating estimate with data:', estimateData);

    const { data: estimate, error } = await supabase
      .from('estimates')
      .insert([estimateData])
      .select()
      .single();

    if (error) {
      console.error('Error creating estimate:', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    console.log('Estimate created successfully:', estimate);

    // Now handle line items if they exist
    if (body.line_items && Array.isArray(body.line_items) && body.line_items.length > 0) {
      console.log('Processing line items:', body.line_items);
      
      const lineItemsData = body.line_items.map((item: any, index: number) => ({
        estimate_id: estimate.id,
        article: item.article_number || item.article || '', // Handle both field names
        description: item.description,
        unit: item.unit || 'st',
        quantity: parseFloat(item.quantity) || 1,
        unit_price: parseFloat(item.unit_price) || 0,
        discount: parseFloat(item.discount) || 0,
        rot_eligible: Boolean(item.rot_eligible || item.rotEligible), // Handle both field names
        sort_order: index
      }));

      console.log('Line items to insert:', lineItemsData);

      const { data: createdLineItems, error: lineItemsError } = await supabase
        .from('estimate_line_items')
        .insert(lineItemsData)
        .select();

      if (lineItemsError) {
        console.error('Error creating line items:', lineItemsError);
        // Don't fail the entire request, but log the error
        console.warn('Estimate created but line items failed:', lineItemsError.message);
      } else {
        console.log('Line items created successfully:', createdLineItems);
      }
    } else {
      console.log('No line items to process');
    }

    return NextResponse.json(estimate, { status: 201 });

  } catch (error) {
    console.error('Error in estimates POST API:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
} 