import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase/supabase';

export async function GET(request: NextRequest) {
  try {
    // Get total estimates count
    const { count: totalCount, error: totalError } = await supabase
      .from('estimates')
      .select('*', { count: 'exact', head: true });

    if (totalError) {
      console.error('Error fetching total estimates count:', totalError);
      return NextResponse.json({ error: totalError.message }, { status: 500 });
    }

    // Get accepted estimates count
    const { count: acceptedCount, error: acceptedError } = await supabase
      .from('estimates')
      .select('*', { count: 'exact', head: true })
      .eq('status', 'accepted');

    if (acceptedError) {
      console.error('Error fetching accepted estimates count:', acceptedError);
      return NextResponse.json({ error: acceptedError.message }, { status: 500 });
    }

    // Get pending estimates count
    const { count: pendingCount, error: pendingError } = await supabase
      .from('estimates')
      .select('*', { count: 'exact', head: true })
      .eq('status', 'pending');

    if (pendingError) {
      console.error('Error fetching pending estimates count:', pendingError);
      return NextResponse.json({ error: pendingError.message }, { status: 500 });
    }

    // Get draft estimates count
    const { count: draftCount, error: draftError } = await supabase
      .from('estimates')
      .select('*', { count: 'exact', head: true })
      .eq('status', 'draft');

    if (draftError) {
      console.error('Error fetching draft estimates count:', draftError);
      return NextResponse.json({ error: draftError.message }, { status: 500 });
    }

    // Get rejected estimates count
    const { count: rejectedCount, error: rejectedError } = await supabase
      .from('estimates')
      .select('*', { count: 'exact', head: true })
      .eq('status', 'rejected');

    if (rejectedError) {
      console.error('Error fetching rejected estimates count:', rejectedError);
      return NextResponse.json({ error: rejectedError.message }, { status: 500 });
    }

    // Get previous period data for comparison (30 days ago)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    const formattedDate = thirtyDaysAgo.toISOString().split('T')[0];

    // Get counts from 30 days ago
    const { count: previousTotalCount } = await supabase
      .from('estimates')
      .select('*', { count: 'exact', head: true })
      .lte('created_at', formattedDate);

    const { count: previousAcceptedCount } = await supabase
      .from('estimates')
      .select('*', { count: 'exact', head: true })
      .eq('status', 'accepted')
      .lte('created_at', formattedDate);

    const { count: previousPendingCount } = await supabase
      .from('estimates')
      .select('*', { count: 'exact', head: true })
      .eq('status', 'pending')
      .lte('created_at', formattedDate);

    const { count: previousDraftCount } = await supabase
      .from('estimates')
      .select('*', { count: 'exact', head: true })
      .eq('status', 'draft')
      .lte('created_at', formattedDate);

    const { count: previousRejectedCount } = await supabase
      .from('estimates')
      .select('*', { count: 'exact', head: true })
      .eq('status', 'rejected')
      .lte('created_at', formattedDate);

    // Calculate percentage changes
    const calculateChange = (current: number, previous: number) => {
      if (previous === 0) return current > 0 ? 100 : 0;
      return Math.round(((current - previous) / previous) * 100);
    };

    const stats = {
      total: {
        count: totalCount || 0,
        change: calculateChange(totalCount || 0, previousTotalCount || 0)
      },
      accepted: {
        count: acceptedCount || 0,
        change: calculateChange(acceptedCount || 0, previousAcceptedCount || 0)
      },
      pending: {
        count: pendingCount || 0,
        change: calculateChange(pendingCount || 0, previousPendingCount || 0)
      },
      draft: {
        count: draftCount || 0,
        change: calculateChange(draftCount || 0, previousDraftCount || 0)
      },
      rejected: {
        count: rejectedCount || 0,
        change: calculateChange(rejectedCount || 0, previousRejectedCount || 0)
      }
    };

    return NextResponse.json(stats);

  } catch (error) {
    console.error('Error in estimates stats API:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
} 