import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabaseClient } from '@/lib/supabase/supabase-server'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params; // estimate_id
    
    // Use the same API endpoint as the detail page
    const estimateResponse = await fetch(`http://localhost:3000/api/estimates/${id}`)
    
    if (!estimateResponse.ok) {
      console.error('Error fetching estimate from API')
      return NextResponse.json(
        { error: 'Offert hittades inte' },
        { status: 404 }
      )
    }
    
    const estimateData = await estimateResponse.json()
    const estimate = estimateData.estimate
    
    if (!estimate) {
      console.error('No estimate data found')
      return NextResponse.json(
        { error: 'Offert hittades inte' },
        { status: 404 }
      )
    }

    // Line items are included in the estimate data
    const lineItems = estimate.estimate_line_items || []

    // Generate HTML for PDF
    const html = generatePDFHTML(estimate, lineItems)

    // For now, return HTML content (later we can integrate with a PDF library)
    return new NextResponse(html, {
      headers: {
        'Content-Type': 'text/html; charset=utf-8',
        'Content-Disposition': `inline; filename="offert-${estimate.estimate_number}.html"`
      }
    })

  } catch (error) {
    console.error('PDF Generation error:', error)
    return NextResponse.json(
      { error: 'Kunde inte generera PDF' },
      { status: 500 }
    )
  }
}

function generatePDFHTML(estimate: any, lineItems: any[]) {
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('sv-SE', {
      style: 'currency',
      currency: 'SEK'
    }).format(amount)
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString('sv-SE')
  }

  const getStatusDisplayName = (status: string) => {
    switch (status) {
      case 'accepted': return 'Godkänd'
      case 'pending': return 'Väntande'
      case 'draft': return 'Utkast'
      case 'rejected': return 'Avvisad'
      default: return status
    }
  }

  const customerName = estimate.customers?.name || estimate.customers?.company_name || 'Okänd kund'

  return `
<!DOCTYPE html>
<html lang="sv">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Offert ${estimate.estimate_number}</title>
    <style>
        body {
            font-family: 'Helvetica', 'Arial', sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 40px 20px;
            background: #fff;
        }
        
        .header {
            border-bottom: 3px solid #2563eb;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        
        .company-info {
            text-align: right;
            color: #666;
            font-size: 14px;
        }
        
        .estimate-title {
            font-size: 28px;
            font-weight: bold;
            color: #2563eb;
            margin: 20px 0 10px 0;
        }
        
        .estimate-number {
            font-size: 18px;
            color: #666;
            margin-bottom: 20px;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin: 30px 0;
        }
        
        .info-section h3 {
            font-size: 16px;
            font-weight: bold;
            color: #2563eb;
            margin-bottom: 10px;
            border-bottom: 1px solid #e5e7eb;
            padding-bottom: 5px;
        }
        
        .info-section p {
            margin: 5px 0;
            font-size: 14px;
        }
        
        .status {
            display: inline-block;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
        }
        
        .status.pending {
            background-color: #fef3c7;
            color: #92400e;
        }
        
        .status.accepted {
            background-color: #d1fae5;
            color: #065f46;
        }
        
        .status.draft {
            background-color: #dbeafe;
            color: #1e40af;
        }
        
        .status.rejected {
            background-color: #fee2e2;
            color: #991b1b;
        }
        
        .description {
            background-color: #f9fafb;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        
        .description h3 {
            margin-top: 0;
            color: #2563eb;
        }
        
        .line-items {
            margin: 30px 0;
        }
        
        .line-items h3 {
            font-size: 18px;
            color: #2563eb;
            margin-bottom: 15px;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #e5e7eb;
        }
        
        th {
            background-color: #f9fafb;
            font-weight: bold;
            color: #374151;
            font-size: 14px;
        }
        
        td {
            font-size: 14px;
        }
        
        .quantity, .unit-price, .total {
            text-align: right;
        }
        
        .rot-eligible {
            display: inline-block;
            background-color: #d1fae5;
            color: #065f46;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 11px;
            font-weight: bold;
            margin-top: 4px;
        }
        
        .summary {
            margin-top: 30px;
            border-top: 2px solid #e5e7eb;
            padding-top: 20px;
        }
        
        .summary-table {
            width: 300px;
            margin-left: auto;
        }
        
        .summary-table td {
            border: none;
            padding: 8px 12px;
        }
        
        .summary-total {
            font-weight: bold;
            font-size: 18px;
            border-top: 2px solid #2563eb;
            color: #2563eb;
        }
        
        .terms {
            background-color: #f9fafb;
            padding: 20px;
            border-radius: 8px;
            margin-top: 30px;
            font-size: 14px;
        }
        
        .terms h4 {
            margin-top: 0;
            color: #2563eb;
        }
        
        @media print {
            body {
                padding: 20px;
            }
            
            .header {
                border-bottom: 2px solid #000;
            }
            
            .estimate-title {
                color: #000;
            }
            
            .info-section h3 {
                color: #000;
            }
            
            .status {
                border: 1px solid #666;
                background-color: #fff !important;
                color: #000 !important;
            }
            
            .summary-total {
                color: #000;
                border-top: 2px solid #000;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="company-info">
            <strong>Ditt Företag AB</strong><br>
            Företagsgatan 123<br>
            123 45 Stockholm<br>
            Tel: 08-123 456 78<br>
            <EMAIL>
        </div>
        
        <div class="estimate-title">OFFERT</div>
        <div class="estimate-number">${estimate.estimate_number}</div>
        <span class="status ${estimate.status}">${getStatusDisplayName(estimate.status)}</span>
    </div>

    <div class="info-grid">
        <div class="info-section">
            <h3>Kund</h3>
            <p><strong>${customerName}</strong></p>
            ${estimate.customers?.email ? `<p>📧 ${estimate.customers.email}</p>` : ''}
            ${estimate.customers?.phone ? `<p>📞 ${estimate.customers.phone}</p>` : ''}
            ${estimate.customers?.address ? `<p>📍 ${estimate.customers.address}</p>` : ''}
        </div>
        
        <div class="info-section">
            <h3>Offertinformation</h3>
            <p><strong>Projekt:</strong> ${estimate.project_name}</p>
            <p><strong>Datum:</strong> ${formatDate(estimate.date || estimate.created_at)}</p>
            <p><strong>Giltig till:</strong> ${formatDate(estimate.valid_until)}</p>
            <p><strong>Betalningsvillkor:</strong> ${estimate.payment_terms}</p>
        </div>
    </div>

    ${estimate.intro_text ? `
    <div class="description">
        <h3>Inledning</h3>
        <p>${estimate.intro_text.replace(/\n/g, '<br>')}</p>
    </div>
    ` : ''}

    ${estimate.description ? `
    <div class="description">
        <h3>Projektbeskrivning</h3>
        <p>${estimate.description.replace(/\n/g, '<br>')}</p>
    </div>
    ` : ''}

    ${lineItems.length > 0 ? `
    <div class="line-items">
        <h3>Specificerade poster</h3>
        <table>
            <thead>
                <tr>
                    <th>Beskrivning</th>
                    <th class="quantity">Antal</th>
                    <th class="unit-price">Pris/enhet</th>
                    <th class="total">Summa</th>
                </tr>
            </thead>
            <tbody>
                ${lineItems.map(item => `
                <tr>
                    <td>
                        <strong>${item.description}</strong>
                        ${item.article ? `<br><small style="color: #666;">${item.article}</small>` : ''}
                        ${item.rot_eligible ? '<br><span class="rot-eligible">ROT-berättigad</span>' : ''}
                    </td>
                    <td class="quantity">${item.quantity} ${item.unit}</td>
                    <td class="unit-price">${formatCurrency(item.unit_price)}</td>
                    <td class="total">${formatCurrency(item.quantity * item.unit_price * (1 - item.discount / 100))}</td>
                </tr>
                `).join('')}
            </tbody>
        </table>
    </div>
    ` : ''}

    <div class="summary">
        <table class="summary-table">
            <tr>
                <td>Subtotal:</td>
                <td style="text-align: right;">${formatCurrency(estimate.subtotal)}</td>
            </tr>
            <tr>
                <td>Moms (25%):</td>
                <td style="text-align: right;">${formatCurrency(estimate.tax_amount)}</td>
            </tr>
            ${estimate.rot_avdrag ? `
            <tr>
                <td>ROT-avdrag:</td>
                <td style="text-align: right; color: #059669;">Tillämpligt</td>
            </tr>
            ` : ''}
            <tr class="summary-total">
                <td><strong>TOTALT:</strong></td>
                <td style="text-align: right;"><strong>${formatCurrency(estimate.total_amount)}</strong></td>
            </tr>
        </table>
    </div>

    ${estimate.closing_text ? `
    <div class="terms">
        <h4>Avslutande information</h4>
        <p>${estimate.closing_text.replace(/\n/g, '<br>')}</p>
    </div>
    ` : ''}

    <div class="terms">
        <h4>Allmänna villkor</h4>
        <p>Denna offert är giltig till ${formatDate(estimate.valid_until)}. Betalning sker enligt överenskomna betalningsvillkor: ${estimate.payment_terms}.</p>
        <p>Vi reserverar oss för eventuella tryckfel och prisändringar.</p>
        <br>
        <p><em>Tack för ert förtroende!</em></p>
    </div>

    <script>
        // Auto-print when opened in new window
        if (window.location.search.includes('print=true')) {
            window.print();
        }
    </script>
</body>
</html>
  `
} 