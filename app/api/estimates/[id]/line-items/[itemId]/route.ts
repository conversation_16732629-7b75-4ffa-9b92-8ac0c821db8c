import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase/supabase';

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string; itemId: string }> }
) {
  try {
    const { id: estimateId, itemId } = await params;
    const body = await request.json();

    const updateData: any = {
      updated_at: new Date().toISOString()
    };

    // Only include fields that are being updated
    if (body.article !== undefined) updateData.article = body.article;
    if (body.description !== undefined) updateData.description = body.description;
    if (body.unit !== undefined) updateData.unit = body.unit;
    if (body.quantity !== undefined) updateData.quantity = body.quantity;
    if (body.unit_price !== undefined) updateData.unit_price = body.unit_price;
    if (body.discount !== undefined) updateData.discount = body.discount;
    if (body.rot_eligible !== undefined) updateData.rot_eligible = body.rot_eligible;
    if (body.visible !== undefined) updateData.visible = body.visible;
    if (body.sort_order !== undefined) updateData.sort_order = body.sort_order;

    const { data: lineItem, error } = await supabase
      .from('estimate_line_items')
      .update(updateData)
      .eq('id', itemId)
      .eq('estimate_id', estimateId)
      .select()
      .single();

    if (error) {
      console.error('Error updating estimate line item:', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    if (!lineItem) {
      return NextResponse.json({ error: 'Line item not found' }, { status: 404 });
    }

    // Update estimate totals
    await updateEstimateTotals(estimateId);

    return NextResponse.json({ lineItem });

  } catch (error) {
    console.error('Error in estimate line item PUT API:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string; itemId: string }> }
) {
  try {
    const { id: estimateId, itemId } = await params;

    const { data: lineItem, error } = await supabase
      .from('estimate_line_items')
      .delete()
      .eq('id', itemId)
      .eq('estimate_id', estimateId)
      .select()
      .single();

    if (error) {
      console.error('Error deleting estimate line item:', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    if (!lineItem) {
      return NextResponse.json({ error: 'Line item not found' }, { status: 404 });
    }

    // Update estimate totals
    await updateEstimateTotals(estimateId);

    return NextResponse.json({ message: 'Line item deleted successfully' });

  } catch (error) {
    console.error('Error in estimate line item DELETE API:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// Helper function to recalculate estimate totals
async function updateEstimateTotals(estimateId: string) {
  try {
    // Get all line items for this estimate
    const { data: lineItems } = await supabase
      .from('estimate_line_items')
      .select('*')
      .eq('estimate_id', estimateId)
      .eq('visible', true);

    if (!lineItems) return;

    // Calculate subtotal
    const subtotal = lineItems.reduce((total, item) => {
      const quantity = parseFloat(item.quantity) || 0;
      const unitPrice = parseFloat(item.unit_price) || 0;
      const discount = parseFloat(item.discount) || 0;
      const lineTotal = quantity * unitPrice * (1 - discount / 100);
      return total + lineTotal;
    }, 0);

    // Get estimate to check tax type
    const { data: estimate } = await supabase
      .from('estimates')
      .select('tax_type')
      .eq('id', estimateId)
      .single();

    // Calculate tax (assuming Swedish VAT rates)
    let taxRate = 0.25; // Default 25%
    if (estimate?.tax_type === '12%') taxRate = 0.12;
    else if (estimate?.tax_type === '6%') taxRate = 0.06;
    else if (estimate?.tax_type === '0%') taxRate = 0;

    const taxAmount = subtotal * taxRate;
    const totalAmount = subtotal + taxAmount;

    // Update the estimate
    await supabase
      .from('estimates')
      .update({
        subtotal: subtotal.toString(),
        tax_amount: taxAmount.toString(),
        total_amount: totalAmount.toString(),
        updated_at: new Date().toISOString()
      })
      .eq('id', estimateId);

  } catch (error) {
    console.error('Error updating estimate totals:', error);
  }
} 