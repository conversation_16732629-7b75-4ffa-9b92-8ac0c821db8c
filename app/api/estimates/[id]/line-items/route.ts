import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase/supabase';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params; // estimate_id

    const { data: lineItems, error } = await supabase
      .from('estimate_line_items')
      .select('*')
      .eq('estimate_id', id)
      .order('sort_order', { ascending: true });

    if (error) {
      console.error('Error fetching estimate line items:', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    return NextResponse.json({ lineItems: lineItems || [] });

  } catch (error) {
    console.error('Error in estimate line items GET API:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params; // estimate_id
    const body = await request.json();

    // Get the next sort order
    const { data: maxSortOrder } = await supabase
      .from('estimate_line_items')
      .select('sort_order')
      .eq('estimate_id', id)
      .order('sort_order', { ascending: false })
      .limit(1);

    const nextSortOrder = maxSortOrder && maxSortOrder.length > 0 
      ? maxSortOrder[0].sort_order + 1 
      : 0;

    const lineItemData = {
      estimate_id: id,
      article: body.article || '',
      description: body.description,
      unit: body.unit || 'st',
      quantity: body.quantity || '1',
      unit_price: body.unit_price || '0',
      discount: body.discount || '0',
      rot_eligible: body.rot_eligible || false,
      sort_order: nextSortOrder,
      visible: body.visible !== false // default to true
    };

    const { data: lineItem, error } = await supabase
      .from('estimate_line_items')
      .insert([lineItemData])
      .select()
      .single();

    if (error) {
      console.error('Error creating estimate line item:', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    // Update estimate totals
    await updateEstimateTotals(id);

    return NextResponse.json({ lineItem }, { status: 201 });

  } catch (error) {
    console.error('Error in estimate line items POST API:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params; // estimate_id
    const url = new URL(request.url);
    const itemId = url.searchParams.get('itemId');

    if (itemId) {
      // Delete specific line item
      const { error } = await supabase
        .from('estimate_line_items')
        .delete()
        .eq('id', itemId)
        .eq('estimate_id', id);

      if (error) {
        console.error('Error deleting specific line item:', error);
        return NextResponse.json({ error: error.message }, { status: 500 });
      }
    } else {
      // Delete all line items for this estimate
      const { error } = await supabase
        .from('estimate_line_items')
        .delete()
        .eq('estimate_id', id);

      if (error) {
        console.error('Error deleting estimate line items:', error);
        return NextResponse.json({ error: error.message }, { status: 500 });
      }
    }

    // Update estimate totals after deletion
    await updateEstimateTotals(id);

    return NextResponse.json({ message: 'Line items deleted successfully' });

  } catch (error) {
    console.error('Error in estimate line items DELETE API:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// Helper function to recalculate estimate totals
async function updateEstimateTotals(estimateId: string) {
  try {
    // Get all line items for this estimate
    const { data: lineItems } = await supabase
      .from('estimate_line_items')
      .select('*')
      .eq('estimate_id', estimateId)
      .eq('visible', true);

    if (!lineItems) return;

    // Calculate subtotal
    const subtotal = lineItems.reduce((total, item) => {
      const quantity = parseFloat(item.quantity) || 0;
      const unitPrice = parseFloat(item.unit_price) || 0;
      const discount = parseFloat(item.discount) || 0;
      const lineTotal = quantity * unitPrice * (1 - discount / 100);
      return total + lineTotal;
    }, 0);

    // Get estimate to check tax type
    const { data: estimate } = await supabase
      .from('estimates')
      .select('tax_type')
      .eq('id', estimateId)
      .single();

    // Calculate tax (assuming Swedish VAT rates)
    let taxRate = 0.25; // Default 25%
    if (estimate?.tax_type === '12%') taxRate = 0.12;
    else if (estimate?.tax_type === '6%') taxRate = 0.06;
    else if (estimate?.tax_type === '0%') taxRate = 0;

    const taxAmount = subtotal * taxRate;
    const totalAmount = subtotal + taxAmount;

    // Update the estimate
    await supabase
      .from('estimates')
      .update({
        subtotal: subtotal.toString(),
        tax_amount: taxAmount.toString(),
        total_amount: totalAmount.toString(),
        updated_at: new Date().toISOString()
      })
      .eq('id', estimateId);

  } catch (error) {
    console.error('Error updating estimate totals:', error);
  }
} 