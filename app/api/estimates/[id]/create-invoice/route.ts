import { NextRequest, NextResponse } from 'next/server'
import { supabase } from '@/lib/supabase/supabase'

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: estimateId } = await params

    // Fetch the estimate details
    const { data: estimate, error: estimateError } = await supabase
      .from('estimates')
      .select('*')
      .eq('id', estimateId)
      .single()

    if (estimateError || !estimate) {
      return NextResponse.json({ error: 'Estimate not found' }, { status: 404 })
    }

    // Check if estimate is accepted
    if (estimate.status !== 'accepted') {
      return NextResponse.json({ 
        error: 'Only accepted estimates can be converted to invoices' 
      }, { status: 400 })
    }

    // Get customer details
    const { data: customer } = await supabase
      .from('customers')
      .select('*')
      .eq('id', estimate.customer_id)
      .single()

    // Generate invoice number
    const { data: existingInvoices } = await supabase
      .from('invoices')
      .select('invoice_number')
      .order('created_at', { ascending: false })
      .limit(1)

    let invoiceNumber = 'INV-001'
    if (existingInvoices && existingInvoices.length > 0) {
      const lastNumber = existingInvoices[0].invoice_number
      const numberMatch = lastNumber.match(/INV-(\d+)/)
      if (numberMatch) {
        const nextNumber = parseInt(numberMatch[1]) + 1
        invoiceNumber = `INV-${nextNumber.toString().padStart(3, '0')}`
      }
    }

    // Get estimate line items
    const { data: lineItems } = await supabase
      .from('estimate_line_items')
      .select('*')
      .eq('estimate_id', estimateId)
      .order('sort_order')

    // Create the invoice
    const invoiceData = {
      invoice_number: invoiceNumber,
      customer_id: estimate.customer_id,
      customer_name: customer?.name || 'Unknown Customer',
      customer_email: customer?.email || '',
      customer_phone: customer?.phone || '',
      customer_address: customer?.address || '',
      project_name: estimate.project_name,
      description: estimate.description,
      line_items: lineItems || [],
      subtotal: estimate.subtotal || 0,
      vat_amount: estimate.tax_amount || 0,
      total_amount: estimate.total_amount || 0,
      vat_rate: parseFloat(estimate.tax_type?.replace('%', '') || '25'),
      currency: estimate.currency || 'SEK',
      status: 'draft',
      issue_date: new Date().toISOString().split('T')[0],
      due_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 30 days from now
      estimate_id: estimateId,
      related_quote_id: estimateId,
      notes: estimate.closing_text || '',
      payment_terms: estimate.payment_terms || '30 dagar netto',
      payment_details: {}
    }

    const { data: invoice, error: invoiceError } = await supabase
      .from('invoices')
      .insert(invoiceData)
      .select()
      .single()

    if (invoiceError) {
      console.error('Error creating invoice:', invoiceError)
      return NextResponse.json({ 
        error: 'Failed to create invoice' 
      }, { status: 500 })
    }

    return NextResponse.json({ 
      invoiceId: invoice.id,
      invoiceNumber: invoice.invoice_number,
      message: 'Invoice created successfully' 
    })

  } catch (error) {
    console.error('Error in create-invoice API:', error)
    return NextResponse.json({ 
      error: 'Internal server error' 
    }, { status: 500 })
  }
} 