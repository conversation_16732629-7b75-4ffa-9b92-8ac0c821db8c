import { NextRequest, NextResponse } from 'next/server'
import { createAppRouterSupabaseClient } from '@/lib/supabase/supabase-server'

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const supabase = await createAppRouterSupabaseClient()
    const { id: estimateId } = await params

    // First, get the estimate data
    const { data: estimate, error: estimateError } = await supabase
      .from('estimates')
      .select(`
        *,
        customers:customer_id (
          id,
          name,
          company_name
        )
      `)
      .eq('id', estimateId)
      .single()

    if (estimateError || !estimate) {
      return NextResponse.json(
        { error: 'Estimate not found' },
        { status: 404 }
      )
    }

    // Check if estimate is accepted
    if (estimate.status !== 'accepted') {
      return NextResponse.json(
        { error: 'Only accepted estimates can be converted to projects' },
        { status: 400 }
      )
    }

    // Check if a project already exists for this estimate
    const { data: existingProject } = await supabase
      .from('projects')
      .select('id')
      .eq('estimate_id', estimateId)
      .single()

    if (existingProject) {
      return NextResponse.json(
        { error: 'A project already exists for this estimate', projectId: existingProject.id },
        { status: 400 }
      )
    }

    // Create the project from estimate data
    const projectData = {
      name: estimate.project_name || `Projekt från offert ${estimate.estimate_number}`,
      description: estimate.description || `Projekt skapat från offert ${estimate.estimate_number}`,
      customer_id: estimate.customer_id,
      estimate_id: estimateId,
      status: 'planning',
      budget: estimate.total_amount,
      start_date: new Date().toISOString().split('T')[0], // Today
      estimated_hours: estimate.estimated_hours || null,
      priority: 'medium',
      progress: 0
    }

    const { data: project, error: projectError } = await supabase
      .from('projects')
      .insert(projectData)
      .select()
      .single()

    if (projectError) {
      console.error('Error creating project:', projectError)
      return NextResponse.json(
        { error: 'Failed to create project' },
        { status: 500 }
      )
    }

    // Update the estimate to link it to the project
    await supabase
      .from('estimates')
      .update({ project_id: project.id })
      .eq('id', estimateId)

    return NextResponse.json({
      success: true,
      projectId: project.id,
      message: 'Project created successfully from estimate'
    })

  } catch (error) {
    console.error('Error in create-project API:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
} 