import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase/supabase';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;

    // Check if the id is a UUID or an estimate number
    const isUuid = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(id);
    const column = isUuid ? 'id' : 'estimate_number';

    const { data: estimate, error } = await supabase
      .from('estimates')
      .select(`
        *,
        customers:customer_id (
          id,
          name,
          company_name,
          email,
          phone
        ),
        estimate_line_items (
          id,
          article,
          description,
          unit,
          quantity,
          unit_price,
          discount,
          rot_eligible,
          sort_order
        )
      `)
      .eq(column, id)
      .single();

    if (error) {
      console.error('Error fetching estimate:', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    if (!estimate) {
      return NextResponse.json({ error: 'Estimate not found' }, { status: 404 });
    }

    return NextResponse.json({ estimate });

  } catch (error) {
    console.error('Error in estimate GET API:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const body = await request.json();

    // Check if the id is a UUID or an estimate number
    const isUuid = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(id);
    const column = isUuid ? 'id' : 'estimate_number';

    // First, get the actual UUID if we received an estimate number
    let actualId = id;
    if (!isUuid) {
      const { data: estimate } = await supabase
        .from('estimates')
        .select('id')
        .eq('estimate_number', id)
        .single();
      
      if (!estimate) {
        return NextResponse.json({ error: 'Estimate not found' }, { status: 404 });
      }
      actualId = estimate.id;
    } else {
      actualId = id;
    }

    const updateData: any = {
      updated_at: new Date().toISOString()
    };

    // Only include fields that are being updated
    if (body.status !== undefined) updateData.status = body.status;
    if (body.project_name !== undefined) updateData.project_name = body.project_name;
    if (body.description !== undefined) updateData.description = body.description;
    if (body.customer_id !== undefined) updateData.customer_id = body.customer_id;
    if (body.date !== undefined) updateData.date = body.date;
    if (body.valid_until !== undefined) updateData.valid_until = body.valid_until;
    if (body.payment_terms !== undefined) updateData.payment_terms = body.payment_terms;
    if (body.tax_type !== undefined) updateData.tax_type = body.tax_type;
    if (body.rot_avdrag !== undefined) updateData.rot_avdrag = body.rot_avdrag;
    if (body.intro_text !== undefined) updateData.intro_text = body.intro_text;
    if (body.closing_text !== undefined) updateData.closing_text = body.closing_text;
    if (body.subtotal !== undefined) updateData.subtotal = body.subtotal;
    if (body.tax_amount !== undefined) updateData.tax_amount = body.tax_amount;
    if (body.total_amount !== undefined) updateData.total_amount = body.total_amount;

    const { data: estimates, error } = await supabase
      .from('estimates')
      .update(updateData)
      .eq(column, id)
      .select();

    if (error) {
      console.error('Error updating estimate:', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    if (!estimates || estimates.length === 0) {
      return NextResponse.json({ error: 'Estimate not found' }, { status: 404 });
    }

    const estimate = estimates[0];

    // Handle line items update if provided
    if (body.line_items && Array.isArray(body.line_items)) {
      // Delete existing line items
      const { error: deleteError } = await supabase
        .from('estimate_line_items')
        .delete()
        .eq('estimate_id', actualId);

      if (deleteError) {
        console.error('Error deleting existing line items:', deleteError);
        return NextResponse.json({ error: deleteError.message }, { status: 500 });
      }

      // Insert new line items (only if there are any)
      if (body.line_items.length > 0) {
        const lineItemsToInsert = body.line_items.map((item: any, index: number) => ({
          estimate_id: actualId,
          article: item.article || '',
          description: item.description || '',
          unit: item.unit || (item.article ? 'st' : 'tim'),
          quantity: parseFloat(item.quantity) || 0,
          unit_price: parseFloat(item.unit_price) || 0,
          discount: parseFloat(item.discount) || 0,
          rot_eligible: Boolean(item.rot_eligible),
          sort_order: index
        }));

        const { data: lineItems, error: lineItemsError } = await supabase
          .from('estimate_line_items')
          .insert(lineItemsToInsert)
          .select();

        if (lineItemsError) {
          console.error('Error creating line items:', lineItemsError);
          return NextResponse.json({ error: lineItemsError.message }, { status: 500 });
        }
      }
    }

    return NextResponse.json({ estimate });

  } catch (error) {
    console.error('Error in estimate PUT API:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;

    // Check if the id is a UUID or an estimate number
    const isUuid = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(id);
    const column = isUuid ? 'id' : 'estimate_number';

    // First, get the actual UUID if we received an estimate number
    let actualId = id;
    if (!isUuid) {
      const { data: estimate } = await supabase
        .from('estimates')
        .select('id')
        .eq('estimate_number', id)
        .single();
      
      if (!estimate) {
        return NextResponse.json({ error: 'Estimate not found' }, { status: 404 });
      }
      actualId = estimate.id;
    }

    // Delete estimate line items first (due to foreign key constraint)
    const { error: lineItemsError } = await supabase
      .from('estimate_line_items')
      .delete()
      .eq('estimate_id', actualId);

    if (lineItemsError) {
      console.error('Error deleting estimate line items:', lineItemsError);
      return NextResponse.json({ error: lineItemsError.message }, { status: 500 });
    }

    // Delete the estimate
    const { data: estimates, error } = await supabase
      .from('estimates')
      .delete()
      .eq(column, id)
      .select();

    if (error) {
      console.error('Error deleting estimate:', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    if (!estimates || estimates.length === 0) {
      return NextResponse.json({ error: 'Estimate not found' }, { status: 404 });
    }

    return NextResponse.json({ message: 'Estimate deleted successfully' });

  } catch (error) {
    console.error('Error in estimate DELETE API:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
} 