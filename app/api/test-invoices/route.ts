import { NextRequest, NextResponse } from 'next/server';
import { createServerSupabaseClient } from '@/lib/supabase/supabase-server';

export async function GET(request: NextRequest) {
  try {
    console.log('Testing invoice service...');
    
    const supabase = await createServerSupabaseClient();
    
    // Test 1: Check if we can connect to Supabase
    console.log('Test 1: Testing Supabase connection...');
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError) {
      console.error('Auth error:', authError);
      return NextResponse.json({ 
        error: 'Authentication failed', 
        details: authError.message 
      }, { status: 401 });
    }
    
    if (!user) {
      return NextResponse.json({ 
        error: 'No authenticated user' 
      }, { status: 401 });
    }
    
    console.log('User authenticated:', user.id);
    
    // Test 2: Check if invoices table exists and is accessible
    console.log('Test 2: Testing invoices table access...');
    const { data: invoices, error: invoicesError } = await supabase
      .from('invoices')
      .select('*')
      .limit(5);
    
    if (invoicesError) {
      console.error('Invoices query error:', invoicesError);
      return NextResponse.json({ 
        error: 'Failed to query invoices table', 
        details: invoicesError.message,
        hint: invoicesError.hint,
        code: invoicesError.code
      }, { status: 500 });
    }
    
    console.log('Invoices found:', invoices?.length || 0);
    
    // Test 3: Try to create a sample invoice if none exist
    if (!invoices || invoices.length === 0) {
      console.log('Test 3: Creating sample invoice...');
      
      const sampleInvoice = {
        invoice_number: `TEST-${Date.now()}`,
        user_id: user.id,
        customer_name: 'Test Customer',
        customer_email: '<EMAIL>',
        project_name: 'Test Project',
        description: 'Test invoice for debugging',
        line_items: [
          {
            id: '1',
            description: 'Test Service',
            quantity: 1,
            unit_price: 1000,
            total: 1000,
            vat_rate: 25
          }
        ],
        subtotal: 1000,
        vat_amount: 250,
        total_amount: 1250,
        vat_rate: 25,
        currency: 'SEK',
        status: 'draft',
        due_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        issue_date: new Date().toISOString().split('T')[0],
        payment_terms: '30 dagar netto',
        notes: 'Test invoice created by debug endpoint'
      };
      
      const { data: newInvoice, error: createError } = await supabase
        .from('invoices')
        .insert(sampleInvoice)
        .select()
        .single();
      
      if (createError) {
        console.error('Create invoice error:', createError);
        return NextResponse.json({ 
          error: 'Failed to create test invoice', 
          details: createError.message,
          hint: createError.hint,
          code: createError.code
        }, { status: 500 });
      }
      
      console.log('Sample invoice created:', newInvoice.id);
      
      return NextResponse.json({
        success: true,
        message: 'Test invoice created successfully',
        user_id: user.id,
        invoice_count: 1,
        sample_invoice: newInvoice
      });
    }
    
    return NextResponse.json({
      success: true,
      message: 'Invoice service test completed',
      user_id: user.id,
      invoice_count: invoices.length,
      invoices: invoices
    });
    
  } catch (error) {
    console.error('Test endpoint error:', error);
    return NextResponse.json({ 
      error: 'Internal server error', 
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
