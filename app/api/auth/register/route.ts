import { NextRequest, NextResponse } from 'next/server';
import { createAppRouterSupabaseClient } from '@/lib/supabase/supabase-server';

export async function POST(request: NextRequest) {
  const requestUrl = new URL(request.url);
  
  try {
    const { email, password, firstName, lastName, role = 'employee' } = await request.json();
    
    // Validera indata
    if (!email || !password) {
      return NextResponse.json(
        { error: 'E-post och lösenord krävs' },
        { status: 400 }
      );
    }
    
    // Kontrollera att role är giltig
    if (!['admin', 'project_manager', 'employee'].includes(role)) {
      return NextResponse.json(
        { error: 'Ogiltig roll' },
        { status: 400 }
      );
    }
    
    // Skapa Supabase-klient på servern
    const supabase = await createAppRouterSupabaseClient();
    
    // Skapa användare
    const { data: authData, error: authError } = await supabase.auth.signUp({
      email,
      password,
      options: {
        emailRedirectTo: `${requestUrl.origin}/auth/callback`,
        data: {
          first_name: firstName,
          last_name: lastName,
          role
        }
      },
    });
    
    if (authError) {
      return NextResponse.json(
        { error: authError.message },
        { status: 400 }
      );
    }
    
    // Vänta en kort stund och kontrollera sedan om profilen skapades
    if (authData.user) {
      // Vi försöker först upsert profilen för att säkerställa att den finns
      const { error: upsertError } = await supabase
        .from('profiles')
        .upsert({
          id: authData.user.id,
          first_name: firstName,
          last_name: lastName,
          role,
        }, { onConflict: 'id' });
      
      if (upsertError) {
        console.error('Failed to upsert profile:', upsertError);
        // Vi försöker uppdatera istället om upsert misslyckades
        const { error: updateError } = await supabase
          .from('profiles')
          .update({
            first_name: firstName,
            last_name: lastName,
            role,
          })
          .eq('id', authData.user.id);
        
        if (updateError) {
          console.error('Failed to update profile after upsert failure:', updateError);
        }
      }
    }
    
    return NextResponse.json(
      { message: 'Användare skapad framgångsrikt', user: authData.user },
      { status: 201 }
    );
  } catch (error) {
    console.error('Registration error:', error);
    return NextResponse.json(
      { error: 'Ett fel uppstod vid registrering' },
      { status: 500 }
    );
  }
} 