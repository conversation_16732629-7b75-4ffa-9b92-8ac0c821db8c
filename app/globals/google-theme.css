/* Google Theme CSS for Project Details Page */

:root {
  --google-blue: #4285F4;
  --google-red: #EA4335;
  --google-yellow: #FBBC05;
  --google-green: #34A853;
  --google-gray-light: #f8f9fa;
  --google-gray-border: #dadce0;
  --google-text-primary: #202124;
  --google-text-secondary: #5f6368;
  --google-font: 'Google Sans', 'Roboto', Arial, sans-serif;
}

.google-page {
  font-family: var(--google-font);
  color: var(--google-text-primary);
  background-color: #fff;
  max-width: 1140px;
  margin: 0 auto;
}

.google-header {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  height: 68px;
  position: sticky;
  top: 0;
  z-index: 100;
}

.google-logo {
  margin-right: 30px;
  display: flex;
  align-items: center;
}

.google-search-bar {
  flex: 1;
  max-width: 690px;
  height: 40px;
  background: rgba(0, 0, 0, 0.04);
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 20px;
  padding: 0 16px;
  display: flex;
  align-items: center;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.google-search-bar:focus-within {
  background: rgba(255, 255, 255, 0.9);
  border-color: rgba(66, 133, 244, 0.3);
  box-shadow: 0 0 0 3px rgba(66, 133, 244, 0.1);
}

.google-search-bar input {
  flex: 1;
  border: none;
  font-size: 16px;
  outline: none;
  font-family: var(--google-font);
  color: var(--google-text-primary);
}

.google-search-icon {
  color: var(--google-blue);
  margin-right: 10px;
}

.google-voice-icon {
  color: var(--google-blue);
  margin-left: 10px;
}

.google-tabs {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  gap: 4px;
}

.google-tab {
  display: inline-flex;
  align-items: center;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 500;
  color: var(--google-text-secondary);
  background: transparent;
  border: none;
  border-radius: 20px;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  white-space: nowrap;
  position: relative;
  overflow: hidden;
}

.google-tab:hover {
  background: rgba(0, 0, 0, 0.04);
  color: var(--google-text-primary);
}

.google-tab.active {
  background: rgba(66, 133, 244, 0.1);
  color: var(--google-blue);
  font-weight: 600;
}

.google-tab.active::before {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg, rgba(66, 133, 244, 0.1), rgba(66, 133, 244, 0.05));
  border-radius: 20px;
  z-index: -1;
}

.google-tab-icon {
  margin-right: 6px;
  display: inline-flex;
  align-items: center;
  transition: transform 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.google-tab.active .google-tab-icon {
  transform: scale(1.1);
}

.google-content {
  padding: 24px 20px;
  background: linear-gradient(to bottom, rgba(248, 249, 250, 0.5), rgba(255, 255, 255, 0.8));
  min-height: calc(100vh - 136px);
}

.google-card {
  border: 1px solid var(--google-gray-border);
  border-radius: 8px;
  margin-bottom: 16px;
  overflow: hidden;
  box-shadow: 0 1px 2px rgba(60, 64, 67, 0.1);
}

.google-card-header {
  padding: 16px;
  border-bottom: 1px solid var(--google-gray-border);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.google-card-title {
  font-size: 18px;
  font-weight: 500;
  color: var(--google-text-primary);
}

.google-card-content {
  padding: 16px;
}

.google-badge {
  display: inline-flex;
  align-items: center;
  padding: 3px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  margin-right: 8px;
}

.google-badge-primary {
  background-color: #e8f0fe;
  color: var(--google-blue);
}

.google-badge-success {
  background-color: #e6f4ea;
  color: var(--google-green);
}

.google-badge-warning {
  background-color: #fef7e0;
  color: #f29900;
}

.google-badge-danger {
  background-color: #fce8e6;
  color: var(--google-red);
}

.google-badge-secondary {
  background-color: #f1f3f4;
  color: var(--google-text-secondary);
}

.google-chip {
  display: inline-flex;
  align-items: center;
  background-color: #f1f3f4;
  color: var(--google-text-secondary);
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 14px;
  margin-right: 8px;
  margin-bottom: 8px;
}

.google-chip-icon {
  margin-right: 6px;
  display: inline-flex;
}

.google-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0 16px;
  height: 36px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  border: 1px solid transparent;
}

.google-button-primary {
  background-color: var(--google-blue);
  color: white;
}

.google-button-primary:hover {
  background-color: #3b78e7;
  box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3);
}

.google-button-outline {
  border: 1px solid var(--google-gray-border);
  color: var(--google-blue);
  background-color: white;
}

.google-button-outline:hover {
  background-color: rgba(66, 133, 244, 0.04);
}

.google-avatar {
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #e8f0fe;
  color: var(--google-blue);
  font-weight: 500;
  overflow: hidden;
}

.google-progress-container {
  height: 4px;
  background-color: #e8eaed;
  border-radius: 2px;
  overflow: hidden;
}

.google-progress-bar {
  height: 100%;
  background-color: var(--google-blue);
}

.google-table {
  width: 100%;
  border-collapse: collapse;
}

.google-table th,
.google-table td {
  padding: 12px 16px;
  text-align: left;
  border-bottom: 1px solid var(--google-gray-border);
}

.google-table th {
  font-weight: 500;
  color: var(--google-text-secondary);
  font-size: 14px;
}

.google-section {
  margin-bottom: 24px;
}

.google-section-title {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 8px;
  color: var(--google-text-primary);
}

.google-metadata {
  display: flex;
  align-items: center;
  color: var(--google-text-secondary);
  font-size: 14px;
  margin-bottom: 4px;
}

.google-metadata-icon {
  margin-right: 8px;
  display: inline-flex;
}

.google-info-box {
  background-color: #f8f9fa;
  padding: 16px;
  border-radius: 8px;
  border: 1px solid var(--google-gray-border);
}

.google-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 16px;
}

.google-flex-row {
  display: flex;
  align-items: center;
}

.google-flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.google-actions {
  display: flex;
  gap: 8px;
}

.google-divider {
  height: 1px;
  background-color: var(--google-gray-border);
  margin: 16px 0;
}

.google-text-primary {
  color: var(--google-text-primary);
}

.google-text-secondary {
  color: var(--google-text-secondary);
}

.google-text-blue {
  color: var(--google-blue);
}

.google-text-red {
  color: var(--google-red);
}

.google-text-green {
  color: var(--google-green);
}

.google-text-yellow {
  color: var(--google-yellow);
} 