@tailwind base;
@tailwind components;
@tailwind utilities;

/* Additional CSS Variables for consistent theming */
:root {
  /* Primary colors */
  --primary: 222.2 84% 4.9%;
  --primary-foreground: 210 40% 98%;

  /* Secondary colors */
  --secondary: 210 40% 96%;
  --secondary-foreground: 222.2 84% 4.9%;

  /* Accent colors */
  --accent: 210 40% 96%;
  --accent-foreground: 222.2 84% 4.9%;

  /* Card colors */
  --card: 0 0% 100%;
  --card-foreground: 222.2 84% 4.9%;

  /* Border and input colors */
  --border: 214.3 31.8% 91.4%;
  --input: 214.3 31.8% 91.4%;
  --ring: 222.2 84% 4.9%;

  /* Status colors */
  --success: 142.1 76.2% 36.3%;
  --warning: 47.9 95.8% 53.1%;
  --error: 0 84.2% 60.2%;
  --info: 221.2 83.2% 53.3%;
}

.dark {
  --primary: 210 40% 98%;
  --primary-foreground: 222.2 84% 4.9%;

  --secondary: 222.2 84% 4.9%;
  --secondary-foreground: 210 40% 98%;

  --accent: 217.2 32.6% 17.5%;
  --accent-foreground: 210 40% 98%;

  --background: 222.2 84% 4.9%;
  --foreground: 210 40% 98%;

  --card: 222.2 84% 4.9%;
  --card-foreground: 210 40% 98%;

  --border: 217.2 32.6% 17.5%;
  --input: 217.2 32.6% 17.5%;
  --ring: 212.7 26.8% 83.9%;
}

:root {
  --background: #ffffff;
  --foreground: #171717;
  --font-sans: var(--font-inter);
  --font-mono: var(--font-jetbrains-mono);

  /* Toast notification variables */
  --toast-background: #ffffff;
  --toast-foreground: #0f172a;
  --toast-border: #e2e8f0;
  --toast-success-bg: #f0fdf4;
  --toast-success-border: #16a34a;
  --toast-success-text: #15803d;
  --toast-error-bg: #fef2f2;
  --toast-error-border: #dc2626;
  --toast-error-text: #dc2626;
  --toast-warning-bg: #fffbeb;
  --toast-warning-border: #d97706;
  --toast-warning-text: #d97706;
  --toast-info-bg: #eff6ff;
  --toast-info-border: #2563eb;
  --toast-info-text: #2563eb;
  --toast-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --toast-backdrop-blur: blur(16px);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;

    /* Dark mode toast variables */
    --toast-background: #1e293b;
    --toast-foreground: #f1f5f9;
    --toast-border: #334155;
    --toast-success-bg: #0f2419;
    --toast-success-border: #16a34a;
    --toast-success-text: #4ade80;
    --toast-error-bg: #2d1b1b;
    --toast-error-border: #dc2626;
    --toast-error-text: #f87171;
    --toast-warning-bg: #2d2416;
    --toast-warning-border: #d97706;
    --toast-warning-text: #fbbf24;
    --toast-info-bg: #1e2a3a;
    --toast-info-border: #2563eb;
    --toast-info-text: #60a5fa;
    --toast-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

/* Google-style linear progress animation */
@keyframes linearProgress {
  0% {
    width: 0%;
    left: 0;
  }
  50% {
    width: 40%;
    left: 30%;
  }
  100% {
    width: 0%;
    left: 100%;
  }
}

.animate-linearProgress {
  animation: linearProgress 2s ease-in-out infinite;
}

/* Apple-style Tab Navigation */
.google-tabs {
  display: flex;
  border-bottom: 1px solid #e5e7eb;
  padding: 0 2rem;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  gap: 0;
  overflow-x: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
  position: relative;
  box-shadow: 0 1px 0 0 rgba(0, 0, 0, 0.05);
}

.dark .google-tabs {
  border-bottom-color: #374151;
  background: rgba(17, 24, 39, 0.95);
  box-shadow: 0 1px 0 0 rgba(255, 255, 255, 0.05);
}

.google-tabs::-webkit-scrollbar {
  display: none;
}

.google-tab {
  display: flex;
  align-items: center;
  padding: 1rem 1.5rem;
  font-size: 0.9rem;
  font-weight: 500;
  color: #6b7280;
  cursor: pointer;
  border-bottom: 2px solid transparent;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  white-space: nowrap;
  position: relative;
  min-width: fit-content;
  background: transparent;
  border-radius: 0;
  letter-spacing: -0.01em;
}

.dark .google-tab {
  color: #9ca3af;
}

.google-tab.active {
  color: #111827;
  border-bottom-color: #3b82f6;
  font-weight: 600;
  background: transparent;
  position: relative;
}

.google-tab.active::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, #3b82f6, #2563eb);
  border-radius: 1px;
  animation: slideIn 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

@keyframes slideIn {
  from {
    transform: scaleX(0);
    opacity: 0;
  }
  to {
    transform: scaleX(1);
    opacity: 1;
  }
}

.dark .google-tab.active {
  color: #f9fafb;
  border-bottom-color: #60a5fa;
}

.dark .google-tab.active::after {
  background: linear-gradient(90deg, #60a5fa, #3b82f6);
}

.google-tab:hover:not(.active) {
  color: #374151;
  background-color: rgba(0, 0, 0, 0.02);
}

.dark .google-tab:hover:not(.active) {
  color: #d1d5db;
  background-color: rgba(255, 255, 255, 0.03);
}

.google-tab-icon {
  margin-right: 0.5rem;
  display: inline-flex;
  align-items: center;
  flex-shrink: 0;
  opacity: 0.7;
  transition: opacity 0.3s ease;
}

.google-tab.active .google-tab-icon {
  opacity: 1;
}

/* Mobile responsive improvements */
@media (max-width: 768px) {
  .google-tabs {
    padding: 0 1rem;
    gap: 0;
  }
  
  .google-tab {
    padding: 0.875rem 1rem;
    font-size: 0.85rem;
  }
  
  .google-tab-icon {
    margin-right: 0.375rem;
  }
}

@media (max-width: 480px) {
  .google-tabs {
    padding: 0 0.75rem;
  }
  
  .google-tab {
    padding: 0.75rem 0.75rem;
    font-size: 0.8rem;
  }
  
  .google-tab-icon {
    margin-right: 0.25rem;
  }
}

.google-content {
  flex: 1;
  padding: 2rem;
  background-color: #f9fafb;
  min-height: calc(100vh - 120px);
}

.dark .google-content {
  background-color: #111827;
}

/* Modern Toast Notification Styles */
[data-sonner-toaster] {
  --width: 356px;
  --font-family: var(--font-sans);
  --toast-gap: 8px;
  --toast-close-button-start: calc(var(--toast-gap) * -1 - 4px);
  --toast-close-button-end: var(--toast-gap);
  --toast-close-button-size: 20px;
  --toast-button-margin-start: auto;
  --toast-button-margin-end: var(--toast-gap);
  --toast-svg-margin-start: var(--toast-gap);
  --toast-svg-margin-end: calc(var(--toast-gap) / 2 * -1);
  --toast-loading-icon-color: hsl(var(--muted-foreground));
  --toast-icon-margin-start: var(--toast-gap);
  --toast-icon-margin-end: calc(var(--toast-gap) / 2 * -1);
  --toast-success-icon-color: var(--toast-success-text);
  --toast-error-icon-color: var(--toast-error-text);
  --toast-warning-icon-color: var(--toast-warning-text);
  --toast-info-icon-color: var(--toast-info-text);
  font-family: var(--font-family);
  --toaster-z-index: 999999;
  --toast-z-index: 999999;
  /* Remove default backdrop blur */
  --normal-bg: transparent;
  --normal-border: transparent;
  --normal-text: transparent;
}

[data-sonner-toaster][data-theme='light'] {
  --normal-bg: var(--toast-background);
  --normal-border: var(--toast-border);
  --normal-text: var(--toast-foreground);
  --success-bg: var(--toast-success-bg);
  --success-border: var(--toast-success-border);
  --success-text: var(--toast-success-text);
  --error-bg: var(--toast-error-bg);
  --error-border: var(--toast-error-border);
  --error-text: var(--toast-error-text);
  --warning-bg: var(--toast-warning-bg);
  --warning-border: var(--toast-warning-border);
  --warning-text: var(--toast-warning-text);
  --info-bg: var(--toast-info-bg);
  --info-border: var(--toast-info-border);
  --info-text: var(--toast-info-text);
}

[data-sonner-toaster][data-theme='dark'] {
  --normal-bg: var(--toast-background);
  --normal-border: var(--toast-border);
  --normal-text: var(--toast-foreground);
  --success-bg: var(--toast-success-bg);
  --success-border: var(--toast-success-border);
  --success-text: var(--toast-success-text);
  --error-bg: var(--toast-error-bg);
  --error-border: var(--toast-error-border);
  --error-text: var(--toast-error-text);
  --warning-bg: var(--toast-warning-bg);
  --warning-border: var(--toast-warning-border);
  --warning-text: var(--toast-warning-text);
  --info-bg: var(--toast-info-bg);
  --info-border: var(--toast-info-border);
  --info-text: var(--toast-info-text);
}

[data-sonner-toast] {
  background: var(--normal-bg) !important;
  border: 1px solid var(--normal-border) !important;
  color: var(--normal-text) !important;
  border-radius: 12px !important;
  box-shadow: var(--toast-shadow) !important;
  backdrop-filter: var(--toast-backdrop-blur) !important;
  -webkit-backdrop-filter: var(--toast-backdrop-blur) !important;
  padding: 16px !important;
  gap: 12px !important;
  font-weight: 500 !important;
  font-size: 14px !important;
  line-height: 1.4 !important;
  min-height: 48px !important;
  transition: all 0.3s cubic-bezier(0.32, 0.72, 0, 1) !important;
  transform-origin: center !important;
  position: relative !important;
  overflow: hidden !important;
}

[data-sonner-toast]:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: inherit;
  backdrop-filter: var(--toast-backdrop-blur);
  -webkit-backdrop-filter: var(--toast-backdrop-blur);
  z-index: -1;
  border-radius: inherit;
}

[data-sonner-toast][data-type='success'] {
  background: var(--success-bg) !important;
  border-color: var(--success-border) !important;
  color: var(--success-text) !important;
}

[data-sonner-toast][data-type='error'] {
  background: var(--error-bg) !important;
  border-color: var(--error-border) !important;
  color: var(--error-text) !important;
}

[data-sonner-toast][data-type='warning'] {
  background: var(--warning-bg) !important;
  border-color: var(--warning-border) !important;
  color: var(--warning-text) !important;
}

[data-sonner-toast][data-type='info'] {
  background: var(--info-bg) !important;
  border-color: var(--info-border) !important;
  color: var(--info-text) !important;
}

[data-sonner-toast] [data-title] {
  font-weight: 600 !important;
  font-size: 14px !important;
  line-height: 1.3 !important;
  margin: 0 !important;
  color: inherit !important;
}

[data-sonner-toast] [data-description] {
  font-weight: 400 !important;
  font-size: 13px !important;
  line-height: 1.4 !important;
  margin: 4px 0 0 0 !important;
  opacity: 0.8 !important;
  color: inherit !important;
}

[data-sonner-toast] [data-button] {
  border-radius: 6px !important;
  padding: 6px 12px !important;
  font-size: 12px !important;
  font-weight: 500 !important;
  transition: all 0.2s ease !important;
  border: 1px solid transparent !important;
  cursor: pointer !important;
}

[data-sonner-toast] [data-button]:hover {
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.12) !important;
}

[data-sonner-toast] [data-cancel] {
  background: rgba(0, 0, 0, 0.08) !important;
  color: var(--normal-text) !important;
}

[data-sonner-toast] [data-cancel]:hover {
  background: rgba(0, 0, 0, 0.12) !important;
}

/* Toast entrance and exit animations */
[data-sonner-toast][data-mounted='true'] {
  animation: toast-slide-in 0.3s cubic-bezier(0.32, 0.72, 0, 1) !important;
}

[data-sonner-toast][data-removed='true'] {
  animation: toast-slide-out 0.2s cubic-bezier(0.32, 0.72, 0, 1) forwards !important;
}

@keyframes toast-slide-in {
  from {
    transform: translateX(100%) scale(0.95);
    opacity: 0;
  }
  to {
    transform: translateX(0) scale(1);
    opacity: 1;
  }
}

@keyframes toast-slide-out {
  from {
    transform: translateX(0) scale(1);
    opacity: 1;
  }
  to {
    transform: translateX(100%) scale(0.95);
    opacity: 0;
  }
}
