"use client";

import { useState } from 'react';
import { supabaseMinimal } from '@/lib/supabase-minimal';

export default function TestMinimalSupabase() {
  const [message, setMessage] = useState('Minimal Supabase imported!');

  const testConnection = async () => {
    try {
      const { data, error } = await supabaseMinimal.auth.getSession();
      setMessage(`Connection test: ${error ? 'Error' : 'Success'}`);
    } catch (err) {
      setMessage(`Error: ${err}`);
    }
  };

  return (
    <div style={{ padding: '20px' }}>
      <h1>Test Minimal Supabase</h1>
      <p>{message}</p>
      <button 
        onClick={testConnection}
        style={{ padding: '10px', backgroundColor: '#0070f3', color: 'white', border: 'none' }}
      >
        Test Connection
      </button>
    </div>
  );
} 