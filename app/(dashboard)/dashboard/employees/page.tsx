'use client';

import React, { useEffect, useState } from 'react';
import { supabase } from '@/lib/supabase/supabase';
import Link from 'next/link';
import { Users, Edit, Search, Plus, Trash2, AlertTriangle, FileText, Briefcase, DollarSign, Calendar, 
Mail, Phone, MapPin, Clock, TrendingUp, UserCheck, UserX } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { toast } from 'sonner';
import GoogleLayout from '@/components/ui/google-layout';
import { ExpandableList } from '@/components/ui/expandable-list';
import { StatsCards } from '@/components/ui/stats-cards';

interface Employee {
  id: string;
  name: string;
  email: string;
  phone?: string;
  position: string;
  department: string;
  status: 'active' | 'sick_leave' | 'vacation' | 'inactive';
  hourly_rate: number;
  utilization_percentage: number;
  location: string;
  hire_date: string;
  avatar_url?: string;
  vacation_days_entitled: number;
  vacation_days_used: number;
  vacation_days_pending: number;
  vacation_days_available: number;
  created_at: string;
  updated_at: string;
}

interface VacationRequest {
  id: string;
  employee_id: string;
  type: 'vacation' | 'sick_leave' | 'personal' | 'parental';
  start_date: string;
  end_date: string;
  days_count: number;
  status: 'pending' | 'approved' | 'rejected';
  notes?: string;
  requested_at: string;
  reviewed_at?: string;
  reviewed_by?: string;
}

interface EmployeeContract {
  id: string;
  employee_id: string;
  contract_type: 'employment' | 'option' | 'nda' | 'other';
  title: string;
  file_url: string;
  signed_date?: string;
  expiry_date?: string;
  status: 'active' | 'expired' | 'terminated';
  created_at: string;
  updated_at: string;
}

interface RelatedRecords {
  vacationRequests: number;
  contracts: number;
  timesheets: number;
}

export default function EmployeesPage() {
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [employeeToDelete, setEmployeeToDelete] = useState<Employee | null>(null);
  const [relatedRecords, setRelatedRecords] = useState<RelatedRecords | null>(null);
  const [isCheckingRelated, setIsCheckingRelated] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [activeTab, setActiveTab] = useState('overview');
  const [employeeStats, setEmployeeStats] = useState({
    totalEmployees: 0,
    activeEmployees: 0,
    onVacation: 0,
    averageHourlyRate: 0,
    averageUtilization: 0,
    pendingRequests: 0
  });

  useEffect(() => {
    async function fetchEmployees() {
      try {
        setLoading(true);
        const { data, error } = await supabase
          .from('employees')
          .select('*')
          .order('name');

        if (error) {
          throw error;
        }

        const employeesData = data || [];
        setEmployees(employeesData);

        // Calculate statistics
        const totalEmployees = employeesData.length;
        const activeEmployees = employeesData.filter(e => e.status === 'active').length;
        const onVacation = employeesData.filter(e => e.status === 'vacation').length;
        const averageHourlyRate = totalEmployees > 0 
          ? Math.round(employeesData.reduce((sum, e) => sum + e.hourly_rate, 0) / totalEmployees)
          : 0;
        const averageUtilization = totalEmployees > 0 
          ? Math.round(employeesData.reduce((sum, e) => sum + e.utilization_percentage, 0) / totalEmployees)
          : 0;

        // Fetch pending vacation requests
        const { data: pendingRequests } = await supabase
          .from('vacation_requests')
          .select('id')
          .eq('status', 'pending');

        setEmployeeStats({
          totalEmployees,
          activeEmployees,
          onVacation,
          averageHourlyRate,
          averageUtilization,
          pendingRequests: pendingRequests?.length || 0
        });
      } catch (error: any) {
        console.error('Error fetching employees:', error.message);
        toast.error('Kunde inte hämta anställda');
      } finally {
        setLoading(false);
      }
    }
    
    fetchEmployees();
  }, []);

  const filteredEmployees = employees.filter(employee => 
    employee.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    employee.position.toLowerCase().includes(searchQuery.toLowerCase()) ||
    employee.department.toLowerCase().includes(searchQuery.toLowerCase()) ||
    (employee.email && employee.email.toLowerCase().includes(searchQuery.toLowerCase()))
  );

  const confirmDelete = async (id: string) => {
    const employee = employees.find(e => e.id === id);
    if (!employee) return;
    
    setEmployeeToDelete(employee);
    setIsCheckingRelated(true);
    setIsDeleteModalOpen(true);
    
    try {
      // Check for related records
      const [vacationResult, contractsResult, timesheetsResult] = await Promise.all([
        supabase.from('vacation_requests').select('id', { count: 'exact' }).eq('employee_id', id),
        supabase.from('employee_contracts').select('id', { count: 'exact' }).eq('employee_id', id),
        supabase.from('timesheets').select('id', { count: 'exact' }).eq('user_id', id)
      ]);
      
      setRelatedRecords({
        vacationRequests: vacationResult.count || 0,
        contracts: contractsResult.count || 0,
        timesheets: timesheetsResult.count || 0
      });
    } catch (error: any) {
      console.error('Error checking related records:', error.message);
      toast.error('Error checking related records');
      setIsDeleteModalOpen(false);
    } finally {
      setIsCheckingRelated(false);
    }
  };

  const handleDelete = async () => {
    if (!employeeToDelete) return;
    
    try {
      setIsDeleting(true);
      
      // If there are related records, we need to delete them first (cascade delete)
      if (relatedRecords) {
        const deletePromises = [];
        
        // Delete in the correct order to avoid foreign key violations
        if (relatedRecords.timesheets > 0) {
          deletePromises.push(
            supabase.from('timesheets').delete().eq('user_id', employeeToDelete.id)
          );
        }
        
        if (relatedRecords.vacationRequests > 0) {
          deletePromises.push(
            supabase.from('vacation_requests').delete().eq('employee_id', employeeToDelete.id)
          );
        }
        
        if (relatedRecords.contracts > 0) {
          deletePromises.push(
            supabase.from('employee_contracts').delete().eq('employee_id', employeeToDelete.id)
          );
        }
        
        // Execute all deletions
        const results = await Promise.all(deletePromises);
        
        // Check for errors in any of the deletions
        for (const result of results) {
          if (result.error) throw result.error;
        }
      }
      
      // Finally delete the employee
      const { error } = await supabase
        .from('employees')
        .delete()
        .eq('id', employeeToDelete.id);
      
      if (error) throw error;
      
      // Remove the deleted employee from state
      setEmployees(employees.filter(e => e.id !== employeeToDelete.id));
      setIsDeleteModalOpen(false);
      setEmployeeToDelete(null);
      setRelatedRecords(null);
      
      // Show success notification
      toast.success('Employee deleted successfully', {
        description: 'Employee and all related records have been removed.'
      });
      
    } catch (error: any) {
      console.error('Error deleting employee:', error.message);
      toast.error('Error deleting employee', {
        description: error.message
      });
    } finally {
      setIsDeleting(false);
    }
  };

  const handleTabChange = (tab: string) => {
    setActiveTab(tab);
  };

  const renderTabContent = () => {
    switch (activeTab) {
      case 'overview':
        return renderEmployeesContent();
      case 'resources':
        return <div className="p-6">Employee Scheduling content coming soon...</div>;
      case 'timeline':
        return <div className="p-6">Employee Timeline content coming soon...</div>;
      default:
        return renderEmployeesContent();
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-700 dark:bg-green-900/20 dark:text-green-400';
      case 'vacation':
        return 'bg-blue-100 text-blue-700 dark:bg-blue-900/20 dark:text-blue-400';
      case 'sick_leave':
        return 'bg-yellow-100 text-yellow-700 dark:bg-yellow-900/20 dark:text-yellow-400';
      case 'inactive':
        return 'bg-gray-100 text-gray-700 dark:bg-gray-900/20 dark:text-gray-400';
      default:
        return 'bg-gray-100 text-gray-700 dark:bg-gray-900/20 dark:text-gray-400';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'active':
        return 'Aktiv';
      case 'vacation':
        return 'Semester';
      case 'sick_leave':
        return 'Sjukskriven';
      case 'inactive':
        return 'Inaktiv';
      default:
        return status;
    }
  };

  const employeeMetrics = [
    {
      label: "Totalt anställda",
      value: employeeStats.totalEmployees,
      icon: <Users className="w-5 h-5 text-blue-600 dark:text-blue-400" />,
      change: "+2",
      changeType: "positive" as const,
      changeLabel: "senaste månaden"
    },
    {
      label: "Aktiva anställda",
      value: employeeStats.activeEmployees,
      icon: <UserCheck className="w-5 h-5 text-green-600 dark:text-green-400" />,
      change: "+1",
      changeType: "positive" as const,
      changeLabel: "senaste månaden"
    },
    {
      label: "På semester",
      value: employeeStats.onVacation,
      icon: <Calendar className="w-5 h-5 text-amber-600 dark:text-amber-400" />,
      change: "0",
      changeType: "neutral" as const,
      changeLabel: "denna vecka"
    },
    {
      label: "Snitt timkostnad",
      value: `${employeeStats.averageHourlyRate} kr`,
      icon: <DollarSign className="w-5 h-5 text-emerald-600 dark:text-emerald-400" />,
      change: "+5%",
      changeType: "positive" as const,
      changeLabel: "senaste året"
    },
    {
      label: "Snitt utnyttjande",
      value: `${employeeStats.averageUtilization}%`,
      icon: <TrendingUp className="w-5 h-5 text-indigo-600 dark:text-indigo-400" />,
      change: "+3%",
      changeType: "positive" as const,
      changeLabel: "senaste månaden"
    },
    {
      label: "Väntande ansökningar",
      value: employeeStats.pendingRequests,
      icon: <Clock className="w-5 h-5 text-orange-600 dark:text-orange-400" />,
      change: "-1",
      changeType: "positive" as const,
      changeLabel: "senaste veckan"
    }
  ];

  const employeeColumns = [
    { key: 'name', label: 'Namn' },
    { key: 'position', label: 'Position' },
    { key: 'department', label: 'Avdelning' },
    { key: 'status', label: 'Status' },
    { key: 'utilization', label: 'Utnyttjande' },
    { key: 'actions', label: 'Åtgärder', className: 'text-right' }
  ];

  const renderEmployeeCell = (employee: Employee, columnKey: string) => {
    switch (columnKey) {
      case 'name':
        return (
          <div className="flex items-center">
            <div className="h-10 w-10 rounded-full bg-blue-500 flex items-center justify-center text-white font-medium text-sm mr-3">
              {employee.name.split(' ').map(n => n[0]).join('')}
            </div>
            <div>
              <Link href={`/dashboard/employees/${employee.id}`} className="text-blue-600 dark:text-blue-400 hover:underline font-medium">
                {employee.name}
              </Link>
              <p className="text-sm text-gray-500 dark:text-gray-400">{employee.email}</p>
            </div>
          </div>
        );
      case 'position':
        return (
          <span className="text-gray-700 dark:text-gray-300">
            {employee.position}
          </span>
        );
      case 'department':
        return (
          <span className="text-gray-700 dark:text-gray-300">
            {employee.department}
          </span>
        );
      case 'status':
        return (
          <span className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(employee.status)}`}>
            {getStatusText(employee.status)}
          </span>
        );
      case 'utilization':
        return (
          <div className="flex items-center">
            <div className="w-16 bg-gray-200 dark:bg-gray-700 rounded-full h-2 mr-2">
              <div 
                className="bg-blue-600 h-2 rounded-full" 
                style={{ width: `${employee.utilization_percentage}%` }}
              ></div>
            </div>
            <span className="text-sm text-gray-600 dark:text-gray-400">
              {employee.utilization_percentage}%
            </span>
          </div>
        );
      case 'actions':
        return (
          <div className="flex justify-end space-x-2" onClick={(e) => e.stopPropagation()}>
            <Link href={`/dashboard/employees/${employee.id}/edit`} className="text-indigo-600 dark:text-indigo-400 hover:text-indigo-900 dark:hover:text-indigo-200">
              <Edit className="h-5 w-5" />
            </Link>
            <button
              onClick={() => confirmDelete(employee.id)}
              className="text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-200"
            >
              <Trash2 className="h-5 w-5" />
            </button>
          </div>
        );
      default:
        return null;
    }
  };

  const renderEmployeeExpandedContent = (employee: Employee) => (
    <div className="flex flex-col md:flex-row gap-8">
      <div className="flex-1 space-y-3">
        <div className="text-lg font-medium text-gray-900 dark:text-white mb-4">Anställningsdetaljer</div>
        <div><span className="font-medium text-gray-700 dark:text-gray-300">Namn:</span> <span className="text-gray-900 dark:text-white">{employee.name}</span></div>
        <div><span className="font-medium text-gray-700 dark:text-gray-300">Position:</span> <span className="text-gray-900 dark:text-white">{employee.position}</span></div>
        <div><span className="font-medium text-gray-700 dark:text-gray-300">Avdelning:</span> <span className="text-gray-900 dark:text-white">{employee.department}</span></div>
        <div><span className="font-medium text-gray-700 dark:text-gray-300">Plats:</span> <span className="text-gray-900 dark:text-white">{employee.location}</span></div>
        <div><span className="font-medium text-gray-700 dark:text-gray-300">Anställningsdatum:</span> <span className="text-gray-900 dark:text-white">{new Date(employee.hire_date).toLocaleDateString('sv-SE')}</span></div>
        <div><span className="font-medium text-gray-700 dark:text-gray-300">Timkostnad:</span> <span className="text-gray-900 dark:text-white">{employee.hourly_rate} kr/h</span></div>
        <div><span className="font-medium text-gray-700 dark:text-gray-300">Utnyttjande:</span> <span className="text-gray-900 dark:text-white">{employee.utilization_percentage}%</span></div>
        {employee.email && (
          <div className="flex items-center"><Mail className="w-4 h-4 mr-2 text-gray-500" /><span className="font-medium text-gray-700 dark:text-gray-300">E-post:</span> <span className="text-gray-900 dark:text-white ml-2">{employee.email}</span></div>
        )}
        {employee.phone && (
          <div className="flex items-center"><Phone className="w-4 h-4 mr-2 text-gray-500" /><span className="font-medium text-gray-700 dark:text-gray-300">Telefon:</span> <span className="text-gray-900 dark:text-white ml-2">{employee.phone}</span></div>
        )}
      </div>
      <div className="flex-1 space-y-3">
        <div className="text-lg font-medium text-gray-900 dark:text-white mb-4">Semesterinformation</div>
        <div><span className="font-medium text-gray-700 dark:text-gray-300">Berättigade dagar:</span> <span className="text-gray-900 dark:text-white">{employee.vacation_days_entitled} dagar</span></div>
        <div><span className="font-medium text-gray-700 dark:text-gray-300">Använda dagar:</span> <span className="text-gray-900 dark:text-white">{employee.vacation_days_used} dagar</span></div>
        <div><span className="font-medium text-gray-700 dark:text-gray-300">Väntande dagar:</span> <span className="text-gray-900 dark:text-white">{employee.vacation_days_pending} dagar</span></div>
        <div><span className="font-medium text-gray-700 dark:text-gray-300">Tillgängliga dagar:</span> <span className="text-gray-900 dark:text-white">{employee.vacation_days_available} dagar</span></div>
        
        <div className="text-lg font-medium text-gray-900 dark:text-white mb-4 mt-6">Snabbåtgärder</div>
        <div className="space-y-3">
          <Link href={`/dashboard/employees/${employee.id}`} className="block">
            <Button variant="outline" className="w-full justify-start">
              <FileText className="w-4 h-4 mr-2" />
              Visa detaljer
            </Button>
          </Link>
          <Link href={`/dashboard/employees/${employee.id}/edit`} className="block">
            <Button variant="outline" className="w-full justify-start">
              <Edit className="w-4 h-4 mr-2" />
              Redigera anställd
            </Button>
          </Link>
          <Link href={`/dashboard/employees/${employee.id}/vacation`} className="block">
            <Button variant="outline" className="w-full justify-start">
              <Calendar className="w-4 h-4 mr-2" />
              Hantera semester
            </Button>
          </Link>
        </div>
      </div>
    </div>
  );

  const renderEmployeesContent = () => (
    <div className="flex-1 p-4 md:p-6 lg:p-8 space-y-6 max-w-full overflow-hidden">
      <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
        <div>
          <h2 className="text-2xl md:text-3xl font-bold tracking-tight text-gray-900 dark:text-white">Anställda</h2>
          <p className="text-gray-500 dark:text-gray-400 mt-1">Hantera och spåra personalresurser</p>
        </div>
        <div className="flex flex-wrap items-center gap-2">
          <Link href="/dashboard/employees/new">
            <Button className="bg-[#5D5FEF] hover:bg-[#4B4AEF]">
              <Plus className="h-4 w-4 mr-2" />
              Lägg till anställd
            </Button>
          </Link>
        </div>
      </div>

      {/* Statistics Cards */}
      <StatsCards metrics={employeeMetrics} />
      
      <div className="flex flex-col gap-4 md:flex-row">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400 dark:text-gray-500" />
          <Input type="search" placeholder="Sök anställda..." className="w-full pl-10 border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-800" value={searchQuery} onChange={(e) => setSearchQuery(e.target.value)} />
        </div>
      </div>
      
      {loading ? (
        <div className="flex justify-center p-8">
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      ) : filteredEmployees.length > 0 ? (
        <ExpandableList
          data={filteredEmployees}
          columns={employeeColumns}
          renderCell={renderEmployeeCell}
          renderExpandedContent={renderEmployeeExpandedContent}
          getRowKey={(employee) => employee.id}
        />
      ) : (
        <div className="text-center py-12 bg-white dark:bg-gray-800 rounded-lg shadow">
          {searchQuery ? (
            <div>
              <Users className="h-12 w-12 mx-auto text-gray-400 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">Inga matchande anställda</h3>
              <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
                Försök justera din sökning för att hitta det du letar efter.
              </p>
              <Button className="mt-4" onClick={() => setSearchQuery('')}>
                Rensa sökning
              </Button>
            </div>
          ) : (
            <div>
              <Users className="h-12 w-12 mx-auto text-gray-400 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">Inga anställda ännu</h3>
              <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
                Kom igång genom att lägga till din första anställd.
              </p>
              <Link href="/dashboard/employees/new">
                <Button className="mt-4">
                  <Plus className="h-4 w-4 mr-2" />
                  Lägg till anställd
                </Button>
              </Link>
            </div>
          )}
        </div>
      )}
      
      {/* Delete Confirmation Modal */}
      {isDeleteModalOpen && employeeToDelete && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
          <div className="bg-white dark:bg-gray-800 rounded-lg max-w-lg w-full p-6 shadow-xl">
            <div className="flex items-center mb-4">
              <div className="flex-shrink-0">
                <AlertTriangle className="h-6 w-6 text-red-500" />
              </div>
              <div className="ml-3">
                <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
                  Delete Employee
                </h3>
              </div>
            </div>
            
            <div className="mb-4">
              <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                You're about to delete <strong>{employeeToDelete.name}</strong> ({employeeToDelete.position}).
              </p>
              
              {isCheckingRelated ? (
                <div className="flex items-center justify-center py-4">
                  <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-blue-500"></div>
                  <span className="ml-2 text-sm text-gray-600 dark:text-gray-400">
                    Checking related records...
                  </span>
                </div>
              ) : relatedRecords ? (
                <div className="space-y-3">
                  {(relatedRecords.vacationRequests > 0 || relatedRecords.contracts > 0 || 
                    relatedRecords.timesheets > 0) ? (
                    <>
                      <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-3">
                        <p className="text-sm font-medium text-red-800 dark:text-red-200 mb-2">
                          ⚠️ This will also delete the following related records:
                        </p>
                        <div className="space-y-1">
                          {relatedRecords.vacationRequests > 0 && (
                            <div className="flex items-center text-sm text-red-700 dark:text-red-300">
                              <Calendar className="h-4 w-4 mr-2" />
                              {relatedRecords.vacationRequests} vacation request{relatedRecords.vacationRequests !== 1 ? 's' : ''}
                            </div>
                          )}
                          {relatedRecords.contracts > 0 && (
                            <div className="flex items-center text-sm text-red-700 dark:text-red-300">
                              <FileText className="h-4 w-4 mr-2" />
                              {relatedRecords.contracts} contract{relatedRecords.contracts !== 1 ? 's' : ''}
                            </div>
                          )}
                          {relatedRecords.timesheets > 0 && (
                            <div className="flex items-center text-sm text-red-700 dark:text-red-300">
                              <Clock className="h-4 w-4 mr-2" />
                              {relatedRecords.timesheets} timesheet{relatedRecords.timesheets !== 1 ? 's' : ''}
                            </div>
                          )}
                        </div>
                      </div>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        <strong>This action cannot be undone.</strong> All data associated with this employee will be permanently removed.
                      </p>
                    </>
                  ) : (
                    <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-3">
                      <p className="text-sm text-green-800 dark:text-green-200">
                        ✅ No related records found. This employee can be safely deleted.
                      </p>
                    </div>
                  )}
                </div>
              ) : null}
            </div>
            
            <div className="flex justify-end space-x-3">
              <Button 
                variant="outline" 
                onClick={() => {
                  setIsDeleteModalOpen(false);
                  setEmployeeToDelete(null);
                  setRelatedRecords(null);
                }}
                disabled={isDeleting}
              >
                Cancel
              </Button>
              <Button 
                variant="destructive" 
                onClick={handleDelete}
                disabled={isCheckingRelated || isDeleting}
                className="min-w-[100px]"
              >
                {isDeleting ? (
                  <div className="flex items-center">
                    <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2"></div>
                    Deleting...
                  </div>
                ) : (
                  'Delete'
                )}
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );

  return (
    <GoogleLayout 
      projectName="Employees" 
      activeTab={activeTab}
      onTabChange={handleTabChange}
      showTabs={{
        overview: true,
        tasks: false,
        resources: true,
        timeline: true,
        settings: false
      }}
    >
      {renderTabContent()}
    </GoogleLayout>
  );
} 