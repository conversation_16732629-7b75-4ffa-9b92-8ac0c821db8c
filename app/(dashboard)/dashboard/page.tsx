'use client'
import { <PERSON>, Card<PERSON><PERSON><PERSON>, Card<PERSON><PERSON>le, CardContent } from '@/components/ui/card'
import { Progress } from '@/components/ui/progress'

import { 
  AlertCircle, 
  Clock, 
  CheckCircle, 
  TrendingUp, 
  TrendingDown, 
  ArrowUpRight, 
  Calendar, 
  CircleCheck, 
  Briefcase, 
  Users, 
  Search, 
  Bell, 
  HelpCircle, 
  Menu,
  Settings,
  MessageCircle,
  Star,
  ChevronRight,
  FileText,
  Edit,
  UserPlus
} from 'lucide-react'
import { getGreeting, getCurrentDate } from '@/lib/utils'
import { useState, useEffect, useMemo, useCallback } from 'react'
import { supabase } from '@/lib/supabase/supabase'
import { PageSkeleton } from '@/components/ui/loading-skeleton'

export default function Dashboard() {
  const [searchFocused, setSearchFocused] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  
  // State for real data
  const [dashboardData, setDashboardData] = useState({
    projects: {
      total: 0,
      active: 0,
      completed: 0,
      planning: 0,
      recentProjects: [] as any[]
    },
    estimates: {
      total: 0,
      pending: 0,
      accepted: 0,
      draft: 0,
      totalValue: 0,
      recentEstimates: [] as any[]
    },
    customers: {
      total: 0
    },
    recentActivity: [] as any[],
    staffAllocation: {
      thisWeek: 0,
      nextWeek: 0,
      twoWeeksOut: 0,
      overtimeWarnings: [] as string[]
    }
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Optimized data fetching with single API call
  const fetchDashboardData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch('/api/dashboard');
      if (!response.ok) {
        throw new Error('Failed to fetch dashboard data');
      }

      const data = await response.json();
      setDashboardData({
        projects: {
          total: data.projects.total,
          active: data.projects.active,
          completed: data.projects.completed,
          planning: data.projects.planning,
          recentProjects: data.projects.recent
        },
        estimates: {
          total: data.estimates.total,
          pending: data.estimates.pending,
          accepted: data.estimates.accepted,
          draft: data.estimates.draft,
          totalValue: data.estimates.totalValue,
          recentEstimates: data.estimates.recent
        },
        customers: {
          total: data.customers.total
        },
        recentActivity: data.recentActivity,
        staffAllocation: data.staffAllocation
      });

    } catch (err: any) {
      console.error('Error fetching dashboard data:', err);
      setError(err.message || 'Failed to load dashboard data');
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchDashboardData();
  }, [fetchDashboardData]);

  const handleSearch = (query: string) => {
    setSearchQuery(query);
    // Here you can implement search functionality
    console.log('Searching for:', query);
  };

  // Helper för att renderera projektets status-indikator
  const getPhaseIndicator = (status: string) => {
    switch(status) {
      case 'active':
        return <span className="text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20 px-2 py-1 rounded-full text-xs font-medium">Aktiv</span>
      case 'planning':
        return <span className="text-amber-600 dark:text-amber-400 bg-amber-50 dark:bg-amber-900/20 px-2 py-1 rounded-full text-xs font-medium">Planering</span>
      case 'completed':
        return <span className="text-green-600 dark:text-green-400 bg-green-50 dark:bg-green-900/20 px-2 py-1 rounded-full text-xs font-medium">Slutförd</span>
      case 'on_hold':
        return <span className="text-gray-600 dark:text-gray-400 bg-gray-50 dark:bg-gray-900/20 px-2 py-1 rounded-full text-xs font-medium">Pausad</span>
      default:
        return <span className="text-gray-600 dark:text-gray-400 bg-gray-50 dark:bg-gray-900/20 px-2 py-1 rounded-full text-xs font-medium">{status}</span>;
    }
  }

  return (
    <>
      {/* Välkomsthälsning - Updated with Google Material Design */}
      <div className="px-6 pt-8 pb-4">
        <h1 className="text-2xl font-normal text-gray-800 dark:text-white">{getGreeting()}, Youssef</h1>
        <p className="text-gray-500 dark:text-gray-400 mt-1 text-sm font-normal">Här är din översikt för {getCurrentDate()}</p>
      </div>

      <div className="space-y-8 font-sans px-6 pb-8">
        {/* Loading State */}
        {loading && <PageSkeleton />}

        {/* Error State */}
        {error && (
          <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
            <div className="flex items-center">
              <AlertCircle className="w-5 h-5 text-red-500 mr-2" />
              <span className="text-red-700 dark:text-red-400">Fel vid laddning av data: {error}</span>
            </div>
          </div>
        )}

        {/* Dashboard Content */}
        {!loading && !error && (
          <>
            {/* Projektöversikt - Updated with real data */}
            <section>
              <div className="flex justify-between items-center mb-5">
                <div className="flex items-center">
                  <Briefcase className="w-5 h-5 text-blue-600 dark:text-blue-400 mr-2" />
                  <h2 className="text-xl font-medium text-gray-800 dark:text-white">Projektöversikt</h2>
                </div>
                <button className="text-blue-600 dark:text-blue-400 text-sm font-medium flex items-center hover:bg-blue-50 dark:hover:bg-blue-900/10 px-3 py-1 rounded-full transition-colors">
                  Visa alla <ChevronRight className="ml-0.5 w-4 h-4" />
                </button>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                <Card className="shadow-sm border-0 bg-white dark:bg-gray-800 rounded-xl overflow-hidden hover:shadow-md transition-shadow duration-300">
                  <CardContent className="p-0">
                    <div className="p-5">
                      <div className="flex flex-col">
                        <p className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">Totalt projekt</p>
                        <span className="text-2xl font-normal text-gray-900 dark:text-white">
                          {dashboardData.projects.total}
                        </span>
                        <span className="text-sm text-blue-600 dark:text-blue-400 mt-2 flex items-center">
                          <Briefcase className="w-4 h-4 mr-1" /> Alla projekt
                        </span>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card className="shadow-sm border-0 bg-white dark:bg-gray-800 rounded-xl overflow-hidden hover:shadow-md transition-shadow duration-300">
                  <CardContent className="p-0">
                    <div className="p-5">
                      <div className="flex flex-col">
                        <p className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">Aktiva projekt</p>
                        <span className="text-2xl font-normal text-gray-900 dark:text-white">
                          {dashboardData.projects.active}
                        </span>
                        <span className="text-sm text-green-600 dark:text-green-400 mt-2 flex items-center">
                          <TrendingUp className="w-4 h-4 mr-1" /> Pågående arbete
                        </span>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card className="shadow-sm border-0 bg-white dark:bg-gray-800 rounded-xl overflow-hidden hover:shadow-md transition-shadow duration-300">
                  <CardContent className="p-0">
                    <div className="p-5">
                      <div className="flex flex-col">
                        <p className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">Slutförda projekt</p>
                        <span className="text-2xl font-normal text-gray-900 dark:text-white">
                          {dashboardData.projects.completed}
                        </span>
                        <span className="text-sm text-green-600 dark:text-green-400 mt-2 flex items-center">
                          <CheckCircle className="w-4 h-4 mr-1" /> Avslutade
                        </span>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card className="shadow-sm border-0 bg-white dark:bg-gray-800 rounded-xl overflow-hidden hover:shadow-md transition-shadow duration-300">
                  <CardContent className="p-0">
                    <div className="p-5">
                      <div className="flex flex-col">
                        <p className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">Kunder</p>
                        <span className="text-2xl font-normal text-gray-900 dark:text-white">
                          {dashboardData.customers.total}
                        </span>
                        <span className="text-sm text-blue-600 dark:text-blue-400 mt-2 flex items-center">
                          <Users className="w-4 h-4 mr-1" /> Registrerade kunder
                        </span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </section>
          </>
        )}

            {/* Kundrelationer / försäljning - Updated with real data */}
            <section>
              <div className="flex justify-between items-center mb-5">
                <div className="flex items-center">
                  <Users className="w-5 h-5 text-blue-600 dark:text-blue-400 mr-2" />
                  <h2 className="text-xl font-medium text-gray-800 dark:text-white">Offerter & försäljning</h2>
                </div>
                <button className="text-blue-600 dark:text-blue-400 text-sm font-medium flex items-center hover:bg-blue-50 dark:hover:bg-blue-900/10 px-3 py-1 rounded-full transition-colors">
                  Visa alla <ChevronRight className="ml-0.5 w-4 h-4" />
                </button>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <Card className="shadow-sm border-0 bg-white dark:bg-gray-800 rounded-xl overflow-hidden hover:shadow-md transition-shadow duration-300">
                  <CardContent className="p-0">
                    <div className="p-5 border-b border-gray-100 dark:border-gray-700">
                      <h3 className="text-base font-medium text-gray-800 dark:text-white">Offertstatus</h3>
                    </div>
                    
                    <div className="px-5 py-4 bg-blue-50 dark:bg-blue-900/10">
                      <div className="flex justify-between mb-1">
                        <span className="font-medium text-gray-700 dark:text-gray-300">Total offertvärde</span>
                        <span className="font-medium text-blue-700 dark:text-blue-400">
                          {new Intl.NumberFormat('sv-SE', { style: 'currency', currency: 'SEK' }).format(dashboardData.estimates.totalValue)}
                        </span>
                      </div>
                      <div className="flex justify-between text-sm text-gray-500 dark:text-gray-400">
                        <span className="flex items-center"><Calendar className="w-4 h-4 mr-1" /> {dashboardData.estimates.total} totala offerter</span>
                        <span className="flex items-center"><TrendingUp className="w-4 h-4 mr-1" /> {dashboardData.estimates.pending} väntande</span>
                      </div>
                    </div>
                
                    <div className="divide-y divide-gray-100 dark:divide-gray-700">
                      {dashboardData.estimates.recentEstimates.length > 0 ? (
                        dashboardData.estimates.recentEstimates.map((estimate: any) => (
                          <div key={estimate.id} className="flex items-center justify-between p-5 hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors cursor-pointer">
                            <div className="flex items-center">
                              <div className={`w-1.5 h-10 rounded-full mr-3 flex-shrink-0 ${
                                estimate.status === 'pending' ? 'bg-amber-500' : 
                                estimate.status === 'accepted' ? 'bg-green-500' : 
                                estimate.status === 'draft' ? 'bg-gray-400' : 'bg-blue-500'
                              }`}></div>
                              <div>
                                <div className="font-medium text-gray-900 dark:text-white">{estimate.project_name}</div>
                                <div className="text-xs text-gray-500 dark:text-gray-400 mt-0.5 flex items-center">
                                  <Calendar className="w-3 h-3 mr-1 text-gray-400 dark:text-gray-500" /> 
                                  Status: {estimate.status === 'pending' ? 'Väntande' : 
                                          estimate.status === 'accepted' ? 'Accepterad' : 
                                          estimate.status === 'draft' ? 'Utkast' : estimate.status}
                                </div>
                              </div>
                            </div>
                            <div className="flex items-center">
                              <span className="font-medium text-gray-900 dark:text-white text-right">
                                {new Intl.NumberFormat('sv-SE', { style: 'currency', currency: 'SEK' }).format(estimate.total_amount || 0)}
                              </span>
                              <ChevronRight className="w-5 h-5 ml-2 text-gray-400 dark:text-gray-500" />
                            </div>
                          </div>
                        ))
                      ) : (
                        <div className="p-5 text-center text-gray-500 dark:text-gray-400">
                          <FileText className="w-8 h-8 mx-auto mb-2 text-gray-300 dark:text-gray-600" />
                          <p>Inga offerter att visa</p>
                        </div>
                      )}
                    </div>
                
                <div className="p-3">
                  <button className="w-full py-2.5 text-sm font-medium text-blue-600 dark:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/10 rounded-lg transition-colors text-center">
                    Visa alla offerter
                  </button>
                </div>
              </CardContent>
            </Card>
            
            <Card className="shadow-sm border-0 bg-white dark:bg-gray-800 rounded-xl overflow-hidden hover:shadow-md transition-shadow duration-300">
              <CardContent className="p-0">
                <div className="p-5 border-b border-gray-100 dark:border-gray-700">
                  <h3 className="text-base font-medium text-gray-800 dark:text-white">Konverteringsstatistik</h3>
                </div>
                
                <div className="p-5">
                  <div className="space-y-6">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <div className="w-10 h-10 rounded-full bg-green-50 dark:bg-green-900/10 flex items-center justify-center text-green-600 dark:text-green-400 mr-3 flex-shrink-0">
                          <TrendingUp className="w-5 h-5" />
                        </div>
                        <div>
                          <div className="text-sm font-normal text-gray-500 dark:text-gray-400">Vunna offerter</div>
                          <div className="text-2xl font-normal text-gray-900 dark:text-white">68%</div>
                        </div>
                      </div>
                      <div className="flex flex-col items-end">
                        <div className="text-xs text-gray-400 dark:text-gray-500">senaste 3 mån</div>
                        <div className="mt-1 text-sm text-green-600 dark:text-green-400 flex items-center">
                          <TrendingUp className="w-4 h-4 mr-0.5" /> 5%
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <div className="w-10 h-10 rounded-full bg-blue-50 dark:bg-blue-900/10 flex items-center justify-center text-blue-600 dark:text-blue-400 mr-3 flex-shrink-0">
                          <MessageCircle className="w-5 h-5" />
                        </div>
                        <div>
                          <div className="text-sm font-normal text-gray-500 dark:text-gray-400">Förfrågningar</div>
                          <div className="text-2xl font-normal text-gray-900 dark:text-white">12</div>
                        </div>
                      </div>
                      <div className="flex flex-col items-end">
                        <div className="text-xs text-gray-400 dark:text-gray-500">senaste veckan</div>
                        <div className="mt-1 text-sm text-green-600 dark:text-green-400 flex items-center">
                          <TrendingUp className="w-4 h-4 mr-0.5" /> 2
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <div className="w-10 h-10 rounded-full bg-amber-50 dark:bg-amber-900/10 flex items-center justify-center text-amber-600 dark:text-amber-400 mr-3 flex-shrink-0">
                          <Star className="w-5 h-5" />
                        </div>
                        <div>
                          <div className="text-sm font-normal text-gray-500 dark:text-gray-400">Kundnöjdhet</div>
                          <div className="text-2xl font-normal text-gray-900 dark:text-white">4.6/5</div>
                        </div>
                      </div>
                      <div className="flex flex-col items-end">
                        <div className="text-xs text-gray-400 dark:text-gray-500">senaste månaden</div>
                        <div className="mt-1 text-sm text-amber-600 dark:text-amber-400 flex items-center">
                          <TrendingDown className="w-4 h-4 mr-0.5" /> 0.2
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div className="border-t border-gray-100 dark:border-gray-700 mt-1 p-3">
                  <button className="w-full py-2.5 text-sm font-medium text-blue-600 dark:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/10 rounded-lg transition-colors text-center">
                    Se detaljerad statistik
                  </button>
                </div>
              </CardContent>
            </Card>
          </div>
        </section>

        {/* Projektstatus - Updated with Google Material Design */}
        <section>
          <div className="flex justify-between items-center mb-5">
            <div className="flex items-center">
              <Calendar className="w-5 h-5 text-blue-600 dark:text-blue-400 mr-2" />
              <h2 className="text-xl font-medium text-gray-800 dark:text-white">Projektstatus</h2>
            </div>
            <button className="text-blue-600 dark:text-blue-400 text-sm font-medium flex items-center hover:bg-blue-50 dark:hover:bg-blue-900/10 px-3 py-1 rounded-full transition-colors">
              Visa alla projekt <ChevronRight className="ml-0.5 w-4 h-4" />
            </button>
          </div>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {dashboardData.projects.recentProjects.length > 0 ? (
                  dashboardData.projects.recentProjects.map((project: any) => (
                    <Card key={project.id} className="shadow-sm border-0 bg-white dark:bg-gray-800 rounded-xl overflow-hidden hover:shadow-md transition-shadow duration-300">
                      <CardContent className="p-0">
                        <div className="p-5">
                          <div className="flex justify-between items-start mb-4">
                            <div>
                              <h3 className="text-base font-medium text-gray-800 dark:text-white">{project.name}</h3>
                              <div className="flex items-center gap-2 mt-1.5">
                                {getPhaseIndicator(project.status)}
                                <span className="text-xs text-gray-500 dark:text-gray-400 flex items-center">
                                  <Calendar className="w-3 h-3 mr-1" /> 
                                  {project.end_date ? new Date(project.end_date).toLocaleDateString('sv-SE') : 'Inget slutdatum'}
                                </span>
                              </div>
                            </div>
                            {project.status === 'completed' && (
                              <div className="bg-green-50 dark:bg-green-900/20 h-8 w-8 rounded-full flex items-center justify-center">
                                <CircleCheck className="h-5 w-5 text-green-600 dark:text-green-400" />
                              </div>
                            )}
                          </div>
                          
                          <div className="space-y-4">
                            <div className="flex justify-between items-center">
                              <div className="flex flex-col">
                                <span className="text-xs text-gray-500 dark:text-gray-400">Budget</span>
                                <span className="text-sm font-medium text-gray-900 dark:text-white mt-0.5">
                                  {project.budget ? new Intl.NumberFormat('sv-SE', { style: 'currency', currency: 'SEK' }).format(project.budget) : 'Ej angiven'}
                                </span>
                              </div>
                              <div className="flex flex-col items-end">
                                <span className="text-xs text-gray-500 dark:text-gray-400">Status</span>
                                <span className="text-sm font-medium text-gray-900 dark:text-white mt-0.5">
                                  {project.status === 'active' ? 'Aktiv' : 
                                   project.status === 'completed' ? 'Slutförd' : 
                                   project.status === 'planning' ? 'Planering' : project.status}
                                </span>
                              </div>
                            </div>
                            
                            {project.description && (
                              <div>
                                <span className="text-xs text-gray-500 dark:text-gray-400">Beskrivning</span>
                                <p className="text-sm text-gray-700 dark:text-gray-300 mt-1 line-clamp-2">
                                  {project.description}
                                </p>
                              </div>
                            )}
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))
                ) : (
                  <div className="col-span-3 text-center py-8">
                    <Briefcase className="w-12 h-12 mx-auto mb-4 text-gray-300 dark:text-gray-600" />
                    <p className="text-gray-500 dark:text-gray-400">Inga projekt att visa</p>
                  </div>
                )}
          </div>
        </section>

        {/* Beläggning och personal - Updated with Google Material Design */}
        <section>
          <div className="flex justify-between items-center mb-5">
            <div className="flex items-center">
              <Users className="w-5 h-5 text-blue-600 dark:text-blue-400 mr-2" />
              <h2 className="text-xl font-medium text-gray-800 dark:text-white">Beläggning och personal</h2>
            </div>
            <button className="text-blue-600 dark:text-blue-400 text-sm font-medium flex items-center hover:bg-blue-50 dark:hover:bg-blue-900/10 px-3 py-1 rounded-full transition-colors">
              Hantera schema <ChevronRight className="ml-0.5 w-4 h-4" />
            </button>
          </div>
          
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="lg:col-span-2">
              <Card className="shadow-sm border-0 bg-white dark:bg-gray-800 rounded-xl overflow-hidden hover:shadow-md transition-shadow duration-300">
                <CardContent className="p-0">
                  {/* Staff data placeholder - will be replaced with real employee data later */}
                  {[].map((person: any, index: number) => (
                    <div key={person.name} className={`hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors border-b border-gray-100 dark:border-gray-700`}>
                      <div className="p-4 flex items-center justify-between">
                        <div className="flex items-center">
                          <div className="h-10 w-10 flex-shrink-0 rounded-full bg-gradient-to-r from-blue-500 to-indigo-600 flex items-center justify-center text-white font-medium mr-4">
                            {person.name.split(' ').map((n: string) => n[0]).join('')}
                          </div>
                          <div>
                            <div className="font-medium text-gray-900 dark:text-white">{person.name}</div>
                            <div className="text-xs text-gray-500 dark:text-gray-400 mt-0.5">{person.role}</div>
                          </div>
                        </div>

                        <div className="flex items-center gap-6">
                          <div className="w-32">
                            <div className="flex justify-between mb-1 text-xs">
                              <span className="text-gray-500 dark:text-gray-400">Beläggning</span>
                              <span className={`font-medium ${
                                person.utilization > 0.8 
                                  ? 'text-red-600 dark:text-red-400' 
                                  : person.utilization > 0.6 
                                    ? 'text-amber-600 dark:text-amber-400'
                                    : 'text-green-600 dark:text-green-400'
                              }`}>
                                {(person.utilization * 100).toFixed(0)}%
                              </span>
                            </div>
                            <div className="w-full bg-gray-100 dark:bg-gray-700 rounded-full h-2">
                              <div 
                                className={`h-2 rounded-full transition-all duration-500 ${
                                  person.utilization > 0.8 
                                    ? 'bg-red-500' 
                                    : person.utilization > 0.6 
                                      ? 'bg-amber-500'
                                      : 'bg-green-500'
                                }`}
                                style={{ width: `${person.utilization * 100}%` }}
                              ></div>
                            </div>
                          </div>

                          <div className="w-24 flex flex-col items-end">
                            <div className="flex flex-wrap gap-1 justify-end">
                              {person.certifications.map((cert: string) => (
                                <span key={cert} className="text-xs bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 px-2 py-0.5 rounded-full">
                                  {cert}
                                </span>
                              ))}
                            </div>
                            {person.overtimeRisk && (
                              <div className="mt-1 text-xs text-red-600 dark:text-red-400 flex items-center">
                                <AlertCircle className="w-3 h-3 mr-1" /> Övertidsrisk
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </CardContent>
              </Card>
            </div>
            
            <div>
              <Card className="shadow-sm border-0 bg-white dark:bg-gray-800 rounded-xl overflow-hidden hover:shadow-md transition-shadow duration-300">
                <CardContent className="p-0">
                  <div className="p-5 border-b border-gray-100 dark:border-gray-700">
                    <h3 className="text-base font-medium text-gray-800 dark:text-white">Beläggningsöversikt</h3>
                  </div>
                  
                  <div className="px-5 py-4">
                    <div className="grid grid-cols-3 gap-2 mb-6">
                      <div className="bg-blue-50 dark:bg-blue-900/10 rounded-lg p-4 text-center">
                        <div className="text-2xl font-normal text-gray-900 dark:text-white">
                          {dashboardData.staffAllocation.thisWeek}%
                        </div>
                        <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">Denna vecka</div>
                      </div>
                      <div className="bg-blue-50 dark:bg-blue-900/10 rounded-lg p-4 text-center">
                        <div className="text-2xl font-normal text-gray-900 dark:text-white">
                          {dashboardData.staffAllocation.nextWeek}%
                        </div>
                        <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">Nästa vecka</div>
                      </div>
                      <div className="bg-blue-50 dark:bg-blue-900/10 rounded-lg p-4 text-center">
                        <div className="text-2xl font-normal text-gray-900 dark:text-white">
                          {dashboardData.staffAllocation.twoWeeksOut}%
                        </div>
                        <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">Om 2 veckor</div>
                      </div>
                    </div>
                    
                    <div className="space-y-5">
                      <div>
                        <div className="flex justify-between items-center mb-1.5">
                          <div className="flex items-center">
                            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Denna vecka</span>
                            {/* Google-style info tooltip */}
                            <div className="ml-1 w-4 h-4 rounded-full bg-gray-100 dark:bg-gray-700 flex items-center justify-center cursor-help">
                              <span className="text-xs text-gray-500 dark:text-gray-400">?</span>
                            </div>
                          </div>
                          <div className="flex items-center">
                            <span className="font-medium text-gray-900 dark:text-white mr-2">83%</span>
                            <span className="text-xs text-green-600 dark:text-green-400 flex items-center">
                              <TrendingUp className="w-3 h-3 mr-0.5" /> 2%
                            </span>
                          </div>
                        </div>
                        <div className="relative w-full h-2 bg-gray-100 dark:bg-gray-700 rounded-full overflow-hidden">
                          <div className="absolute top-0 left-0 h-full bg-blue-600 dark:bg-blue-500 rounded-full transition-all duration-500" style={{ width: '83%' }}></div>
                        </div>
                      </div>
                      
                      <div>
                        <div className="flex justify-between items-center mb-1.5">
                          <div className="flex items-center">
                            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Nästa vecka</span>
                          </div>
                          <div className="flex items-center">
                            <span className="font-medium text-gray-900 dark:text-white mr-2">76%</span>
                            <span className="text-xs text-red-600 dark:text-red-400 flex items-center">
                              <TrendingDown className="w-3 h-3 mr-0.5" /> 7%
                            </span>
                          </div>
                        </div>
                        <div className="relative w-full h-2 bg-gray-100 dark:bg-gray-700 rounded-full overflow-hidden">
                          <div className="absolute top-0 left-0 h-full bg-blue-600 dark:bg-blue-500 rounded-full transition-all duration-500" style={{ width: '76%' }}></div>
                        </div>
                      </div>
                      
                      <div>
                        <div className="flex justify-between items-center mb-1.5">
                          <div className="flex items-center">
                            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Om två veckor</span>
                          </div>
                          <div className="flex items-center">
                            <span className="font-medium text-gray-900 dark:text-white mr-2">62%</span>
                            <span className="text-xs text-amber-600 dark:text-amber-400 flex items-center">
                              <TrendingDown className="w-3 h-3 mr-0.5" /> 14%
                            </span>
                          </div>
                        </div>
                        <div className="relative w-full h-2 bg-gray-100 dark:bg-gray-700 rounded-full overflow-hidden">
                          <div className="absolute top-0 left-0 h-full bg-blue-600 dark:bg-blue-500 rounded-full transition-all duration-500" style={{ width: '62%' }}></div>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <div className="border-t border-gray-100 dark:border-gray-700 p-5">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center">
                        <AlertCircle className="w-4 h-4 text-red-500 dark:text-red-400 mr-2" />
                        <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Övertidsvarningar</span>
                      </div>
                      <div className="flex items-center">
                        <span className={`text-xs font-medium px-2 py-0.5 rounded-full ${dashboardData.staffAllocation.overtimeWarnings.length > 0 ? 'bg-red-50 text-red-600 dark:bg-red-900/20 dark:text-red-400' : 'bg-green-50 text-green-600 dark:bg-green-900/20 dark:text-green-400'}`}>
                          {dashboardData.staffAllocation.overtimeWarnings.length}
                        </span>
                      </div>
                    </div>
                    
                    {dashboardData.staffAllocation.overtimeWarnings.length > 0 && (
                      <div className="text-xs text-gray-500 dark:text-gray-400 mt-1 flex items-start">
                        <span className="bg-red-50 dark:bg-red-900/10 text-red-600 dark:text-red-400 w-5 h-5 rounded-full flex items-center justify-center flex-shrink-0 mr-2 mt-0.5">!</span>
                        <span>Anna Andersson har jobbat mer än 40 timmar denna vecka och riskerar övertid enligt arbetstidslagen.</span>
                      </div>
                    )}
                  </div>
                  
                  <div className="border-t border-gray-100 dark:border-gray-700 p-3">
                    <button className="w-full py-2.5 text-sm font-medium text-blue-600 dark:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/10 rounded-lg transition-colors text-center">
                      Visa detaljerad beläggning
                    </button>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>

        {/* Tidslinje / händelselog */}
        <section className="mt-6">
          <div className="flex justify-between items-center mb-5">
            <div className="flex items-center">
              <Clock className="w-5 h-5 text-blue-600 dark:text-blue-400 mr-2" />
              <h2 className="text-xl font-medium text-gray-800 dark:text-white">Senaste händelser</h2>
            </div>
            <button className="text-blue-600 dark:text-blue-400 text-sm font-medium flex items-center hover:bg-blue-50 dark:hover:bg-blue-900/10 px-3 py-1 rounded-full transition-colors">
              Visa alla <ChevronRight className="ml-0.5 w-4 h-4" />
            </button>
          </div>
          <Card className="shadow-sm border-0 bg-white dark:bg-gray-800 rounded-xl overflow-hidden hover:shadow-md transition-shadow duration-300">
            <CardContent className="p-0">
              <div className="divide-y divide-gray-100 dark:divide-gray-700">
                <div className="p-5 hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors cursor-pointer">
                  <div className="flex">
                    <div className="mr-4 flex-shrink-0">
                      <div className="w-10 h-10 rounded-full bg-green-50 dark:bg-green-900/10 flex items-center justify-center text-green-600 dark:text-green-400">
                        <CheckCircle className="w-5 h-5" />
                      </div>
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between mb-1">
                        <h3 className="text-base font-medium text-gray-900 dark:text-white truncate">Projekt avslutat: Kontorsrenovering</h3>
                        <span className="text-xs text-gray-500 dark:text-gray-400 ml-4">Idag 09:45</span>
                      </div>
                      <p className="text-sm text-gray-500 dark:text-gray-400">Slutfördes inom budget (3% under budgeterad kostnad)</p>
                      <div className="mt-2">
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-50 text-green-700 dark:bg-green-900/20 dark:text-green-400">
                          Slutförd
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div className="p-5 hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors cursor-pointer">
                  <div className="flex">
                    <div className="mr-4 flex-shrink-0">
                      <div className="w-10 h-10 rounded-full bg-blue-50 dark:bg-blue-900/10 flex items-center justify-center text-blue-600 dark:text-blue-400">
                        <FileText className="w-5 h-5" />
                      </div>
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between mb-1">
                        <h3 className="text-base font-medium text-gray-900 dark:text-white truncate">Ny offert skickad: Nybyggnation Nacka</h3>
                        <span className="text-xs text-gray-500 dark:text-gray-400 ml-4">Igår 16:32</span>
                      </div>
                      <p className="text-sm text-gray-500 dark:text-gray-400">Värde: 2,8 MSEK, svarstid 14 dagar</p>
                      <div className="mt-2">
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-50 text-blue-700 dark:bg-blue-900/20 dark:text-blue-400">
                          Offert
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div className="p-5 hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors cursor-pointer">
                  <div className="flex">
                    <div className="mr-4 flex-shrink-0">
                      <div className="w-10 h-10 rounded-full bg-amber-50 dark:bg-amber-900/10 flex items-center justify-center text-amber-600 dark:text-amber-400">
                        <Edit className="w-5 h-5" />
                      </div>
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between mb-1">
                        <h3 className="text-base font-medium text-gray-900 dark:text-white truncate">Ändring i kontrakt: Villa Solvik</h3>
                        <span className="text-xs text-gray-500 dark:text-gray-400 ml-4">2 dagar sedan 11:20</span>
                      </div>
                      <p className="text-sm text-gray-500 dark:text-gray-400">Tilläggsarbeten godkända, +320 000 SEK</p>
                      <div className="mt-2">
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-amber-50 text-amber-700 dark:bg-amber-900/20 dark:text-amber-400">
                          Ändring
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div className="p-5 hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors cursor-pointer">
                  <div className="flex">
                    <div className="mr-4 flex-shrink-0">
                      <div className="w-10 h-10 rounded-full bg-purple-50 dark:bg-purple-900/10 flex items-center justify-center text-purple-600 dark:text-purple-400">
                        <UserPlus className="w-5 h-5" />
                      </div>
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between mb-1">
                        <h3 className="text-base font-medium text-gray-900 dark:text-white truncate">Ny anställd påbörjade tjänst</h3>
                        <span className="text-xs text-gray-500 dark:text-gray-400 ml-4">5 dagar sedan 08:00</span>
                      </div>
                      <p className="text-sm text-gray-500 dark:text-gray-400">Björn Bengtsson, Designer</p>
                      <div className="mt-2">
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-50 text-purple-700 dark:bg-purple-900/20 dark:text-purple-400">
                          Personal
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="border-t border-gray-100 dark:border-gray-700 p-3">
                <button className="w-full py-2.5 text-sm font-medium text-blue-600 dark:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/10 rounded-lg transition-colors text-center">
                  Visa alla händelser
                </button>
              </div>
            </CardContent>
          </Card>
        </section>
      </div>
    </>
  )
} 