'use client'

import React, { useState, useEffect, Suspense } from 'react'
import { useRouter } from 'next/navigation'
import { ArrowLeft, Save, Plus, Trash2, Calculator } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card'
import Link from 'next/link'
import { CreateInvoiceForm } from '@/components/invoices/create-invoice-form'
import { PageSkeleton } from '@/components/ui/loading-skeleton'
import { toast } from 'sonner'

export default function CreateInvoicePage() {
  const router = useRouter()
  const [loading, setLoading] = useState(false)

  const handleInvoiceCreated = (invoiceId: string) => {
    toast.success('Faktura skapad', {
      description: 'Fakturan har skapats framgångsrikt'
    })
    router.push(`/dashboard/fakturor/${invoiceId}`)
  }

  const handleCancel = () => {
    router.back()
  }

  return (
    <div className="p-4 md:p-6 lg:p-8 space-y-6 max-w-full overflow-hidden">
      {/* Header with Breadcrumbs */}
      <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
        <div className="flex items-center gap-4">
          <Link href="/dashboard/fakturor">
            <Button variant="outline" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Tillbaka
            </Button>
          </Link>
          <div>
            <nav className="flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400 mb-2">
              <Link href="/dashboard" className="hover:text-gray-700 dark:hover:text-gray-300">
                Dashboard
              </Link>
              <span>/</span>
              <Link href="/dashboard/fakturor" className="hover:text-gray-700 dark:hover:text-gray-300">
                Fakturor
              </Link>
              <span>/</span>
              <span className="text-gray-900 dark:text-white">Ny faktura</span>
            </nav>
            <h1 className="text-2xl md:text-3xl font-bold tracking-tight text-gray-900 dark:text-white">
              Skapa ny faktura
            </h1>
            <p className="text-gray-500 dark:text-gray-400 mt-1">
              Fyll i uppgifterna nedan för att skapa en ny faktura
            </p>
          </div>
        </div>
      </div>

      {/* Form Content */}
      <Suspense fallback={<PageSkeleton />}>
        <CreateInvoiceForm 
          onSuccess={handleInvoiceCreated}
          onCancel={handleCancel}
          loading={loading}
          setLoading={setLoading}
        />
      </Suspense>
    </div>
  )
}
