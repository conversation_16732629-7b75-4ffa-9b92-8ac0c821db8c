'use client'

import React, { useState, Suspense } from 'react'
import { Receipt, Plus, Search, Filter, LayoutGrid, Table } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import Link from 'next/link'
import { InvoicesList } from '@/components/invoices/invoices-list'
import { InvoicesSummary } from '@/components/invoices/invoices-summary'
import { StatsSkeleton, TableSkeleton } from '@/components/ui/loading-skeleton'

export default function FakturorPage() {
  const [view, setView] = useState<'table' | 'kanban'>('table')
  const [searchQuery, setSearchQuery] = useState('')

  return (
    <div className="p-4 md:p-6 lg:p-8 space-y-6 max-w-full overflow-hidden">
      <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
        <div>
          <h2 className="text-2xl md:text-3xl font-bold tracking-tight text-gray-900 dark:text-white">Fakturor</h2>
          <p className="text-gray-500 dark:text-gray-400 mt-1">Hantera och spåra fakturor</p>
        </div>
        <div className="flex flex-wrap items-center gap-2">
          <Link href="/dashboard/fakturor/create">
            <Button className="bg-[#5D5FEF] hover:bg-[#4B4AEF]">
              <Plus className="h-4 w-4 mr-2" />
              Ny faktura
            </Button>
          </Link>
        </div>
      </div>

      {/* Statistics Cards */}
      <Suspense fallback={<StatsSkeleton count={6} />}>
        <InvoicesSummary />
      </Suspense>

      <div className="flex flex-col gap-4 md:flex-row md:items-center justify-between">
        <div className="relative flex-1 max-w-md">
          <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400 dark:text-gray-500" />
          <Input
            type="search"
            placeholder="Sök fakturor..."
            className="w-full pl-10 border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-800"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>

        <div className="flex items-center space-x-2">
          <button
            onClick={() => setView('table')}
            className={`p-2 rounded-md ${view === 'table' ? 'bg-blue-100 text-blue-600 dark:bg-blue-900/20 dark:text-blue-400' : 'text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'}`}
          >
            <Table className="w-4 h-4" />
          </button>
          <button
            onClick={() => setView('kanban')}
            className={`p-2 rounded-md ${view === 'kanban' ? 'bg-blue-100 text-blue-600 dark:bg-blue-900/20 dark:text-blue-400' : 'text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'}`}
          >
            <LayoutGrid className="w-4 h-4" />
          </button>
        </div>
      </div>

      {/* Invoice List */}
      <Suspense fallback={<TableSkeleton rows={8} />}>
        <InvoicesList />
      </Suspense>
    </div>
  )
}