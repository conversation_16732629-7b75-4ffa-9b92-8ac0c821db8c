'use client'

import React, { useState, useEffect } from 'react'
import { useRouter, useParams } from 'next/navigation'
import { But<PERSON> } from '@/components/ui/button'
import { 
  ArrowLeft, Download, Send, Edit, Trash2, 
  CheckCircle2, User, Building, Phone, AtSign, MapPin, 
  CalendarIcon, CreditCardIcon, Eye, Receipt, Calculator,
  Clock, Check, X, AlertTriangle, Save, XCircle, FileText
} from 'lucide-react'
import Link from 'next/link'
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { invoiceService } from '@/services/invoiceService'
import { Invoice } from '@/lib/types'
import { formatCurrency, formatDate } from '@/lib/utils'
import { toast } from 'sonner'

export default function InvoiceDetailPage() {
  const router = useRouter()
  const params = useParams()
  const invoiceId = params.id as string

  const [invoice, setInvoice] = useState<Invoice | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    if (invoiceId) {
      fetchInvoice()
    }
  }, [invoiceId])

  const fetchInvoice = async () => {
    try {
      setLoading(true)
      const data = await invoiceService.getInvoiceById(invoiceId)
      setInvoice(data)
    } catch (error) {
      console.error('Error fetching invoice:', error)
      toast.error('Kunde inte hämta faktura')
    } finally {
      setLoading(false)
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'draft':
        return <FileText className="h-5 w-5" />
      case 'sent':
        return <Send className="h-5 w-5" />
      case 'paid':
        return <CheckCircle2 className="h-5 w-5" />
      case 'overdue':
        return <AlertTriangle className="h-5 w-5" />
      case 'cancelled':
        return <XCircle className="h-5 w-5" />
      default:
        return <Clock className="h-5 w-5" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'draft':
        return 'bg-gray-100 text-gray-700 dark:bg-gray-800 dark:text-gray-300'
      case 'sent':
        return 'bg-blue-100 text-blue-700 dark:bg-blue-900/20 dark:text-blue-400'
      case 'paid':
        return 'bg-green-100 text-green-700 dark:bg-green-900/20 dark:text-green-400'
      case 'overdue':
        return 'bg-red-100 text-red-700 dark:bg-red-900/20 dark:text-red-400'
      case 'cancelled':
        return 'bg-gray-100 text-gray-700 dark:bg-gray-800 dark:text-gray-300'
      default:
        return 'bg-gray-100 text-gray-700 dark:bg-gray-800 dark:text-gray-300'
    }
  }

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'draft':
        return 'Utkast'
      case 'sent':
        return 'Skickad'
      case 'paid':
        return 'Betald'
      case 'overdue':
        return 'Förfallen'
      case 'cancelled':
        return 'Avbruten'
      default:
        return status
    }
  }

  if (loading) {
    return (
      <div className="p-4 md:p-6 lg:p-8">
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-1/3"></div>
          <div className="h-64 bg-gray-200 dark:bg-gray-700 rounded"></div>
          <div className="h-32 bg-gray-200 dark:bg-gray-700 rounded"></div>
        </div>
      </div>
    )
  }

  if (!invoice) {
    return (
      <div className="p-4 md:p-6 lg:p-8">
        <div className="text-center py-12">
          <Receipt className="h-12 w-12 mx-auto text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">Faktura hittades inte</h3>
          <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
            Den begärda fakturan kunde inte hittas.
          </p>
          <Link href="/dashboard/fakturor">
            <Button className="mt-4">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Tillbaka till fakturor
            </Button>
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="p-4 md:p-6 lg:p-8 space-y-6">
      {/* Header */}
      <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
        <div className="flex items-center gap-4">
          <Link href="/dashboard/fakturor">
            <Button variant="outline" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Tillbaka
            </Button>
          </Link>
          <div>
            <h1 className="text-2xl md:text-3xl font-bold tracking-tight text-gray-900 dark:text-white">
              Faktura {invoice.invoice_number}
            </h1>
            <p className="text-gray-500 dark:text-gray-400 mt-1">
              {invoice.project_name && `${invoice.project_name} • `}
              Skapad {formatDate(invoice.created_at)}
            </p>
          </div>
        </div>
        <div className="flex flex-wrap items-center gap-2">
          <Badge className={`${getStatusColor(invoice.status)} flex items-center gap-1`}>
            {getStatusIcon(invoice.status)}
            {getStatusLabel(invoice.status)}
          </Badge>
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Ladda ner PDF
          </Button>
          <Link href={`/dashboard/fakturor/${invoice.id}/edit`}>
            <Button variant="outline" size="sm">
              <Edit className="h-4 w-4 mr-2" />
              Redigera
            </Button>
          </Link>
          {invoice.status === 'draft' && (
            <Button size="sm">
              <Send className="h-4 w-4 mr-2" />
              Skicka faktura
            </Button>
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Invoice Details */}
          <Card>
            <CardHeader>
              <CardTitle>Fakturauppgifter</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-500 dark:text-gray-400">Fakturanummer</label>
                  <p className="text-lg font-semibold text-gray-900 dark:text-white">{invoice.invoice_number}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500 dark:text-gray-400">Status</label>
                  <div className="mt-1">
                    <Badge className={getStatusColor(invoice.status)}>
                      {getStatusLabel(invoice.status)}
                    </Badge>
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500 dark:text-gray-400">Utfärdad</label>
                  <p className="text-gray-900 dark:text-white">{formatDate(invoice.issue_date)}</p>
                </div>
                {invoice.due_date && (
                  <div>
                    <label className="text-sm font-medium text-gray-500 dark:text-gray-400">Förfallodatum</label>
                    <p className="text-gray-900 dark:text-white">{formatDate(invoice.due_date)}</p>
                  </div>
                )}
                {invoice.payment_date && (
                  <div>
                    <label className="text-sm font-medium text-gray-500 dark:text-gray-400">Betald</label>
                    <p className="text-gray-900 dark:text-white">{formatDate(invoice.payment_date)}</p>
                  </div>
                )}
                <div>
                  <label className="text-sm font-medium text-gray-500 dark:text-gray-400">Betalningsvillkor</label>
                  <p className="text-gray-900 dark:text-white">{invoice.payment_terms}</p>
                </div>
              </div>
              {invoice.description && (
                <div>
                  <label className="text-sm font-medium text-gray-500 dark:text-gray-400">Beskrivning</label>
                  <p className="text-gray-900 dark:text-white">{invoice.description}</p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Line Items */}
          <Card>
            <CardHeader>
              <CardTitle>Fakturarader</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b border-gray-200 dark:border-gray-700">
                      <th className="text-left py-2 text-sm font-medium text-gray-500 dark:text-gray-400">Beskrivning</th>
                      <th className="text-right py-2 text-sm font-medium text-gray-500 dark:text-gray-400">Antal</th>
                      <th className="text-right py-2 text-sm font-medium text-gray-500 dark:text-gray-400">Pris</th>
                      <th className="text-right py-2 text-sm font-medium text-gray-500 dark:text-gray-400">Summa</th>
                    </tr>
                  </thead>
                  <tbody>
                    {invoice.line_items.map((item, index) => (
                      <tr key={index} className="border-b border-gray-100 dark:border-gray-800">
                        <td className="py-3 text-gray-900 dark:text-white">{item.description}</td>
                        <td className="py-3 text-right text-gray-900 dark:text-white">{item.quantity}</td>
                        <td className="py-3 text-right text-gray-900 dark:text-white">
                          {formatCurrency(item.unit_price, invoice.currency)}
                        </td>
                        <td className="py-3 text-right font-medium text-gray-900 dark:text-white">
                          {formatCurrency(item.amount, invoice.currency)}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                  <tfoot>
                    <tr className="border-t-2 border-gray-200 dark:border-gray-700">
                      <td colSpan={3} className="py-2 text-right font-medium text-gray-900 dark:text-white">Subtotal:</td>
                      <td className="py-2 text-right font-medium text-gray-900 dark:text-white">
                        {formatCurrency(invoice.subtotal, invoice.currency)}
                      </td>
                    </tr>
                    <tr>
                      <td colSpan={3} className="py-2 text-right font-medium text-gray-900 dark:text-white">
                        Moms ({invoice.vat_rate}%):
                      </td>
                      <td className="py-2 text-right font-medium text-gray-900 dark:text-white">
                        {formatCurrency(invoice.vat_amount, invoice.currency)}
                      </td>
                    </tr>
                    <tr className="border-t border-gray-200 dark:border-gray-700">
                      <td colSpan={3} className="py-2 text-right text-lg font-bold text-gray-900 dark:text-white">Total:</td>
                      <td className="py-2 text-right text-lg font-bold text-gray-900 dark:text-white">
                        {formatCurrency(invoice.total_amount, invoice.currency)}
                      </td>
                    </tr>
                  </tfoot>
                </table>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Customer Info */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5" />
                Kundinformation
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div>
                <p className="font-medium text-gray-900 dark:text-white">{invoice.customer_name}</p>
              </div>
              {invoice.customer_email && (
                <div className="flex items-center gap-2">
                  <AtSign className="h-4 w-4 text-gray-500" />
                  <span className="text-gray-700 dark:text-gray-300">{invoice.customer_email}</span>
                </div>
              )}
              {invoice.customer_phone && (
                <div className="flex items-center gap-2">
                  <Phone className="h-4 w-4 text-gray-500" />
                  <span className="text-gray-700 dark:text-gray-300">{invoice.customer_phone}</span>
                </div>
              )}
              {invoice.customer_address && (
                <div className="flex items-start gap-2">
                  <MapPin className="h-4 w-4 text-gray-500 mt-0.5" />
                  <span className="text-gray-700 dark:text-gray-300">{invoice.customer_address}</span>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Snabbåtgärder</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <Button variant="outline" className="w-full justify-start">
                <Download className="w-4 h-4 mr-2" />
                Ladda ner PDF
              </Button>
              <Button variant="outline" className="w-full justify-start">
                <Send className="w-4 h-4 mr-2" />
                Skicka via e-post
              </Button>
              <Link href={`/dashboard/fakturor/${invoice.id}/edit`} className="block">
                <Button variant="outline" className="w-full justify-start">
                  <Edit className="w-4 h-4 mr-2" />
                  Redigera faktura
                </Button>
              </Link>
              {invoice.status === 'sent' && (
                <Button variant="outline" className="w-full justify-start">
                  <CheckCircle2 className="w-4 h-4 mr-2" />
                  Markera som betald
                </Button>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
