"use client"

import { useEffect, useState } from "react"
import { toast } from "sonner"
import { Toaster } from "@/components/ui/sonner"
import { Button } from "@/components/ui/button"
import GoogleLayout from '@/components/ui/google-layout'

// Define the ChangelogItem type
interface ChangelogItem {
  id: string
  timestamp: string
  action: string
  user: string
  description: string
}

export default function ChangelogPage() {
  const [activeTab, setActiveTab] = useState('overview')
  // State to store the changelog items
  const [logs, setLogs] = useState<ChangelogItem[]>([])
  
  // Load the logs from localStorage on component mount
  useEffect(() => {
    const storedLogs = localStorage.getItem('changelog')
    if (storedLogs) {
      try {
        setLogs(JSON.parse(storedLogs))
      } catch (error) {
        console.error('Failed to parse changelog from localStorage', error)
      }
    }
  }, [])
  
  // Simulate adding a new log entry (for demo purposes only)
  const addDemoLog = () => {
    const newLog: ChangelogItem = {
      id: Date.now().toString(),
      timestamp: new Date().toISOString(),
      action: "Action Performed",
      user: "<PERSON>sse<PERSON>",
      description: "User performed a test action"
    }
    
    // Add the log to state
    const updatedLogs = [newLog, ...logs]
    setLogs(updatedLogs)
    
    // Save to localStorage
    localStorage.setItem('changelog', JSON.stringify(updatedLogs))
    
    // Show a toast notification
    toast.success("Action logged", {
      description: "The action has been recorded in the changelog"
    })
  }
  
  // Format the timestamp for display
  const formatDate = (isoDate: string) => {
    const date = new Date(isoDate)
    return new Intl.DateTimeFormat('en-US', {
      dateStyle: 'medium',
      timeStyle: 'short'
    }).format(date)
  }

  const handleTabChange = (tab: string) => {
    setActiveTab(tab)
  }

  const renderTabContent = () => {
    switch (activeTab) {
      case 'overview':
        return renderChangelogContent()
      case 'resources':
        return <div className="p-6">Project Staffing content coming soon...</div>
      case 'timeline':
        return <div className="p-6">Project Planning content coming soon...</div>
      default:
        return renderChangelogContent()
    }
  }

  const renderChangelogContent = () => (
    <div className="container mx-auto py-8">
      <Toaster />
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">Changelog</h1>
        <Button onClick={addDemoLog}>Add Test Log</Button>
      </div>
      
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead className="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Timestamp</th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">User</th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Action</th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Description</th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              {logs.length > 0 ? (
                logs.map((log) => (
                  <tr key={log.id}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">{formatDate(log.timestamp)}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">{log.user}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">{log.action}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">{log.description}</td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={4} className="px-6 py-4 text-center text-sm text-gray-500 dark:text-gray-300">
                    No actions have been recorded yet
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  )
  
  return (
    <GoogleLayout 
      projectName="Changelog" 
      activeTab={activeTab}
      onTabChange={handleTabChange}
      showTabs={{
        overview: true,
        tasks: false,
        resources: true,
        timeline: true,
        settings: false
      }}
    >
      {renderTabContent()}
    </GoogleLayout>
  )
} 