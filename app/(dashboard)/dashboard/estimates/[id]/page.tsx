"use client"

import React, { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, use<PERSON>ara<PERSON> } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { 
  FileText, ArrowLeft, Download, Send, Edit, Trash2, 
  CheckCircle2, User, Building, Phone, AtSign, MapPin, 
  CalendarIcon, CreditCardIcon, Eye, Hammer, Calculator,
  Clock, Check, X, AlertTriangle, Save, XCircle, Plus, MinusCircle, MoreHorizontal
} from 'lucide-react';
import Link from "next/link";
import { Card } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { 
  FloatingActionPanel, 
  FloatingActionItem, 
  FloatingActionSeparator 
} from "@/components/ui/floating-action-panel";

interface LineItem {
  id: string;
  article?: string;
  description: string;
  unit?: string;
  quantity: number;
  unit_price: number;
  discount?: number;
  rot_eligible?: boolean;
  sort_order?: number;
}

interface WorkItem {
  id: string;
  description: string;
  quantity: number;
  unitPrice: number;
  total: number;
  unit?: string;
  rotEligible?: boolean;
}

interface MaterialItem {
  id: string;
  articleNumber: string;
  description: string;
  quantity: number;
  unitPrice: number;
  total: number;
  unit?: string;
}

interface Customer {
  id: string;
  name: string;
  company_name?: string;
  email: string;
  phone?: string;
}

interface Estimate {
  id: string;
  estimate_number: string;
  project_name?: string;
  description?: string;
  date: string;
  valid_until?: string;
  status: string;
  customer_id: string;
  customers: Customer;
  payment_terms?: string;
  tax_type?: string;
  rot_avdrag?: number;
  intro_text?: string;
  closing_text?: string;
  subtotal?: number;
  tax_amount?: number;
  total_amount?: number;
  estimate_line_items: LineItem[];
  created_at: string;
  updated_at: string;
}

const FormSection = ({ title, children, className }: { title?: string, children: React.ReactNode, className?: string }) => (
  <div className={`bg-white dark:bg-gray-800 rounded-xl border border-gray-100 dark:border-gray-700 shadow-sm ${className}`}>
    {title && (
      <div className="px-6 py-4 border-b border-gray-100 dark:border-gray-700">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white">{title}</h3>
      </div>
    )}
    <div className="p-6">{children}</div>
  </div>
);

const StatusBadge = ({ status }: { status: string }) => {
  const getStatusStyles = () => {
    switch (status) {
      case 'draft':
        return 'bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300';
      case 'sent':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400';
      case 'accepted':
        return 'bg-green-100 text-green-700 dark:bg-green-900/20 dark:text-green-400';
      case 'rejected':
        return 'bg-red-100 text-red-700 dark:bg-red-900/20 dark:text-red-400';
      default:
        return 'bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300';
    }
  };

  const statusLabels = {
    'draft': 'Utkast',
    'sent': 'Skickad',
    'accepted': 'Accepterad',
    'rejected': 'Avvisad'
  };

  const statusIcons = {
    'draft': Clock,
    'sent': Send,
    'accepted': Check,
    'rejected': X
  };

  const Icon = statusIcons[status as keyof typeof statusIcons] || Clock;

  return (
    <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getStatusStyles()}`}>
      <Icon className="w-4 h-4 mr-1" />
      {statusLabels[status as keyof typeof statusLabels] || status}
    </span>
  );
};

const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('sv-SE', { style: 'currency', currency: 'SEK' }).format(amount);
};

const formatDate = (dateString: string) => {
  if (!dateString) return '';
  const date = new Date(dateString);
  return date.toLocaleDateString('sv-SE');
};

// PDF Preview Modal Component (exact copy from create page)
const PDFPreviewModal = ({ 
  isOpen, 
  onClose, 
  estimate,
  workItems,
  materialItems 
}: { 
  isOpen: boolean;
  onClose: () => void; 
  estimate: any;
  workItems: any[];
  materialItems: any[];
}) => {
  if (!isOpen) return null;

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('sv-SE', { style: 'currency', currency: 'SEK' }).format(amount);
  };

  const formatDate = (dateString: string) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString('sv-SE');
  };

  // Calculate totals - handle both editing mode arrays and original estimate data
  const workTotal = workItems.reduce((sum, item) => {
    // Handle both unitPrice (editing mode) and unit_price (original data)
    const price = item.unitPrice || item.unit_price || 0;
    return sum + (item.quantity * price);
  }, 0);
  
  const materialTotal = materialItems.reduce((sum, item) => {
    // Handle both unitPrice (editing mode) and unit_price (original data)
    const price = item.unitPrice || item.unit_price || 0;
    return sum + (item.quantity * price);
  }, 0);
  
  const subtotal = workTotal + materialTotal;
  const vatRate = 25; // 25% VAT
  const vatAmount = subtotal * (vatRate / 100);
  const totalAmount = subtotal + vatAmount;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-800 rounded-lg max-w-4xl w-full max-h-[90vh] overflow-hidden">
        <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">Förhandsgranska offert</h3>
          <Button variant="outline" size="sm" onClick={onClose}>
            <X className="w-4 h-4" />
          </Button>
        </div>
        
        <div className="overflow-y-auto max-h-[calc(90vh-120px)]">
          <div className="p-8 bg-white text-gray-900" style={{ fontFamily: 'Helvetica, Arial, sans-serif' }}>
            {/* Header */}
            <div className="border-b-4 border-blue-600 pb-6 mb-8">
              <div className="text-right text-gray-600 text-sm mb-4">
                <strong>Ditt Snickeriföretag AB</strong><br />
                Hantverkargatan 123<br />
                123 45 Stockholm<br />
                Tel: 08-123 456 78<br />
                <EMAIL>
              </div>
              
              <div className="text-3xl font-bold text-blue-600 mb-2">OFFERT</div>
              <div className="text-lg text-gray-600 mb-4">
                {estimate.estimate_number}
              </div>
            </div>

            {/* Customer and Estimate Info */}
            <div className="grid grid-cols-2 gap-8 mb-8">
              <div>
                <h3 className="text-lg font-bold text-blue-600 mb-3 border-b border-gray-200 pb-1">Kund</h3>
                <p><strong>{estimate.customers?.name || estimate.customers?.company_name}</strong></p>
                {estimate.customers?.email && <p>📧 {estimate.customers.email}</p>}
                {estimate.customers?.phone && <p>📞 {estimate.customers.phone}</p>}
              </div>
              
              <div>
                <h3 className="text-lg font-bold text-blue-600 mb-3 border-b border-gray-200 pb-1">Offertinformation</h3>
                {estimate.project_name && <p><strong>Projekt:</strong> {estimate.project_name}</p>}
                <p><strong>Datum:</strong> {formatDate(estimate.date)}</p>
                <p><strong>Giltigt till:</strong> {formatDate(estimate.valid_until)}</p>
                <p><strong>Betalningsvillkor:</strong> {estimate.payment_terms}</p>
              </div>
            </div>

            {/* Intro Text */}
            {estimate.intro_text && (
              <div className="bg-gray-50 p-6 rounded-lg mb-8">
                <h3 className="text-lg font-bold text-blue-600 mb-3">Inledning</h3>
                <p className="whitespace-pre-wrap">{estimate.intro_text}</p>
              </div>
            )}

            {/* Project Description */}
            {estimate.description && (
              <div className="bg-gray-50 p-6 rounded-lg mb-8">
                <h3 className="text-lg font-bold text-blue-600 mb-3">Projektbeskrivning</h3>
                <p className="whitespace-pre-wrap">{estimate.description}</p>
              </div>
            )}

            {/* Line Items */}
            <div className="mb-8">
              {/* Work Items */}
              {workItems.length > 0 && (
                <>
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center">
                      <Hammer className="w-5 h-5 text-blue-600 mr-2" />
                      <h4 className="text-lg font-medium text-gray-900">Arbetsmoment</h4>
                    </div>
                  </div>
                  
                  <div className="overflow-x-auto">
                    <table className="w-full border-collapse">
                      <thead>
                        <tr className="border-b border-gray-200">
                          <th className="text-left py-3 text-sm font-medium text-gray-700">Beskrivning</th>
                          <th className="text-right py-3 text-sm font-medium text-gray-700 w-24">Antal</th>
                          <th className="text-right py-3 text-sm font-medium text-gray-700 w-24">Enhet</th>
                          <th className="text-right py-3 text-sm font-medium text-gray-700 w-32">Pris</th>
                          <th className="text-center py-3 text-sm font-medium text-gray-700 w-16">ROT</th>
                          <th className="text-right py-3 text-sm font-medium text-gray-700 w-32">Totalt</th>
                        </tr>
                      </thead>
                      <tbody>
                        {workItems.map((item: any) => {
                          // Handle both unitPrice (editing mode) and unit_price (original data)
                          const price = item.unitPrice || item.unit_price || 0;
                          const total = item.quantity * price * (1 - (item.discount || 0) / 100);
                          // Handle both rotEligible (editing mode) and rot_eligible (original data)
                          const rotEligible = item.rotEligible !== undefined ? item.rotEligible : item.rot_eligible;
                          
                          return (
                            <tr key={item.id} className="border-b border-gray-100">
                              <td className="py-3 text-gray-900">
                                {item.description}
                              </td>
                              <td className="text-right py-3 text-gray-900">
                                {item.quantity}
                              </td>
                              <td className="text-right py-3 text-gray-600">
                                {item.unit || 'tim'}
                              </td>
                              <td className="text-right py-3 text-gray-900">
                                {formatCurrency(price)}
                              </td>
                              <td className="text-center py-3">
                                {rotEligible ? (
                                  <CheckCircle2 className="w-4 h-4 text-green-500 mx-auto" />
                                ) : (
                                  <XCircle className="w-4 h-4 text-red-500 mx-auto" />
                                )}
                              </td>
                              <td className="text-right py-3 text-gray-900">
                                {formatCurrency(total)}
                              </td>
                            </tr>
                          );
                        })}
                        
                        {workItems.length === 0 && (
                          <tr>
                            <td colSpan={6} className="py-8 text-center text-gray-500">
                              Inga arbetsmoment tillagda ännu.
                            </td>
                          </tr>
                        )}
                      </tbody>
                    </table>
                  </div>
                </>
              )}

              {/* Material Items */}
              {materialItems.length > 0 && (
                <>
                  <div className="flex items-center justify-between mb-4 mt-8">
                    <div className="flex items-center">
                      <Calculator className="w-5 h-5 text-blue-600 mr-2" />
                      <h4 className="text-lg font-medium text-gray-900">Material</h4>
                    </div>
                  </div>
                  
                  <div className="overflow-x-auto">
                    <table className="w-full border-collapse">
                      <thead>
                        <tr className="border-b border-gray-200">
                          <th className="text-left py-3 text-sm font-medium text-gray-700">Artikel</th>
                          <th className="text-left py-3 text-sm font-medium text-gray-700">Beskrivning</th>
                          <th className="text-right py-3 text-sm font-medium text-gray-700 w-24">Antal</th>
                          <th className="text-right py-3 text-sm font-medium text-gray-700 w-24">Enhet</th>
                          <th className="text-right py-3 text-sm font-medium text-gray-700 w-32">Pris</th>
                          <th className="text-right py-3 text-sm font-medium text-gray-700 w-32">Totalt</th>
                        </tr>
                      </thead>
                      <tbody>
                        {materialItems.map((item: any) => {
                          // Handle both unitPrice (editing mode) and unit_price (original data)
                          const price = item.unitPrice || item.unit_price || 0;
                          const total = item.quantity * price * (1 - (item.discount || 0) / 100);
                          // Handle both articleNumber (editing mode) and article (original data)
                          const articleNumber = item.articleNumber || item.article || '';
                          
                          return (
                            <tr key={item.id} className="border-b border-gray-100">
                              <td className="py-3 text-sm text-gray-600">{articleNumber}</td>
                              <td className="py-3 text-gray-900">{item.description}</td>
                              <td className="text-right py-3 text-gray-900">{item.quantity}</td>
                              <td className="text-right py-3 text-gray-600">{item.unit || 'st'}</td>
                              <td className="text-right py-3 text-gray-900">{formatCurrency(price)}</td>
                              <td className="text-right py-3 text-gray-900">{formatCurrency(total)}</td>
                            </tr>
                          );
                        })}
                        
                        {materialItems.length === 0 && (
                          <tr>
                            <td colSpan={6} className="py-8 text-center text-gray-500">
                              Inga material tillagda ännu.
                            </td>
                          </tr>
                        )}
                      </tbody>
                    </table>
                  </div>
                </>
              )}
            </div>

            {/* Summary */}
            <div className="border-t-2 border-gray-200 pt-6 mb-8">
              <table className="w-80 ml-auto">
                <tbody>
                  <tr>
                    <td className="py-2">Subtotal:</td>
                    <td className="text-right py-2">{formatCurrency(subtotal)}</td>
                  </tr>
                  <tr>
                    <td className="py-2">Moms ({vatRate}%):</td>
                    <td className="text-right py-2">{formatCurrency(vatAmount)}</td>
                  </tr>
                  <tr className="border-t-2 border-blue-600 font-bold text-lg text-blue-600">
                    <td className="py-3"><strong>TOTALT:</strong></td>
                    <td className="text-right py-3"><strong>{formatCurrency(totalAmount)}</strong></td>
                  </tr>
                </tbody>
              </table>
            </div>

            {/* Closing Text */}
            {estimate.closing_text && (
              <div className="bg-gray-50 p-6 rounded-lg mb-8">
                <h4 className="font-bold text-blue-600 mb-3">Avslutande information</h4>
                <p className="whitespace-pre-wrap">{estimate.closing_text}</p>
              </div>
            )}

            {/* Terms */}
            <div className="bg-gray-50 p-6 rounded-lg">
              <h4 className="font-bold text-blue-600 mb-3">Allmänna villkor</h4>
              <p>Denna offert är giltig till {formatDate(estimate.valid_until)}. Betalning sker enligt överenskomna betalningsvillkor: {estimate.payment_terms}.</p>
              <p className="mt-2">Vi reserverar oss för eventuella tryckfel och prisändringar.</p>
              <p className="mt-4 italic">Tack för ert förtroende!</p>
            </div>
          </div>
        </div>
        
        <div className="flex items-center justify-end gap-3 p-4 border-t border-gray-200 dark:border-gray-700">
          <Button variant="outline" onClick={() => window.print()}>
            <FileText className="w-4 h-4 mr-2" />
            Skriv ut
          </Button>
          <Button onClick={onClose}>
            Stäng
          </Button>
        </div>
      </div>
    </div>
  );
};

export default function EstimateViewPage() {
  const router = useRouter();
  const params = useParams();
  const id = params.id as string;
  
  const [loading, setLoading] = useState(true);
  const [estimate, setEstimate] = useState<Estimate | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [showPDFPreview, setShowPDFPreview] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  
  // Editing states - updated to match create page
  const [isEditing, setIsEditing] = useState(false);
  const [editForm, setEditForm] = useState({
    project_name: '',
    description: '',
    intro_text: '',
    closing_text: '',
    payment_terms: '',
    valid_until: ''
  });
  
  // Separate arrays for work items and materials to match create page
  const [workItems, setWorkItems] = useState<WorkItem[]>([]);
  const [materialItems, setMaterialItems] = useState<MaterialItem[]>([]);

  useEffect(() => {
    fetchEstimate();
  }, [id]);

  useEffect(() => {
    if (estimate) {
      // Always populate the edit form and arrays when estimate changes
      setEditForm({
        project_name: estimate.project_name || '',
        description: estimate.description || '',
        intro_text: estimate.intro_text || '',
        closing_text: estimate.closing_text || '',
        payment_terms: estimate.payment_terms || '',
        valid_until: estimate.valid_until || ''
      });
      
      // Always populate workItems and materialItems so PDF preview works
      setWorkItems(estimate.estimate_line_items.filter((item: LineItem) => !item.article).map((item: LineItem) => ({
        id: item.id,
        description: item.description,
        quantity: item.quantity,
        unitPrice: item.unit_price,
        total: item.quantity * item.unit_price * (1 - (item.discount || 0) / 100),
        unit: item.unit,
        rotEligible: item.rot_eligible
      })));
      
      setMaterialItems(estimate.estimate_line_items.filter((item: LineItem) => item.article).map((item: LineItem) => ({
        id: item.id,
        articleNumber: item.article || '',
        description: item.description,
        quantity: item.quantity,
        unitPrice: item.unit_price,
        total: item.quantity * item.unit_price * (1 - (item.discount || 0) / 100),
        unit: item.unit
      })));
    }
  }, [estimate]);

  const fetchEstimate = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/estimates/${id}`);
      
      if (!response.ok) {
        if (response.status === 404) {
          setError('Offerten hittades inte');
        } else {
          setError('Fel vid hämtning av offert');
        }
        return;
      }

      const data = await response.json();
      setEstimate(data.estimate);
    } catch (error) {
      console.error('Error fetching estimate:', error);
      setError('Fel vid hämtning av offert');
    } finally {
      setLoading(false);
    }
  };

  const handleSaveChanges = async () => {
    if (!estimate) return;

    try {
      // Combine work items and material items for saving
      const combinedLineItems = [
        ...workItems.map(item => ({
          id: item.id,
          description: item.description,
          quantity: item.quantity,
          unit_price: item.unitPrice,
          discount: 0,
          rot_eligible: item.rotEligible || false,
          unit: item.unit || 'tim'
        })),
        ...materialItems.map(item => ({
          id: item.id,
          article: item.articleNumber,
          description: item.description,
          quantity: item.quantity,
          unit_price: item.unitPrice,
          discount: 0,
          rot_eligible: false,
          unit: item.unit || 'st'
        }))
      ];

      const response = await fetch(`/api/estimates/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...editForm,
          line_items: combinedLineItems
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to update estimate');
      }

      const updatedEstimate = await response.json();
      setEstimate(estimate ? {
        ...estimate, 
        ...editForm,
        estimate_line_items: combinedLineItems 
      } : null);
      setIsEditing(false);
      alert('Ändringar sparade!');
    } catch (error) {
      console.error('Error updating estimate:', error);
      alert('Kunde inte spara ändringar');
    }
  };

  const handleStatusUpdate = async (newStatus: string) => {
    if (!estimate) return;

    try {
      const response = await fetch(`/api/estimates/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status: newStatus }),
      });

      if (response.ok) {
        setEstimate(prev => prev ? { ...prev, status: newStatus } : null);
      }
    } catch (error) {
      console.error('Error updating estimate status:', error);
    }
  };

  const handleDelete = async () => {
    if (!estimate || !confirm('Är du säker på att du vill ta bort denna offert?')) return;

    try {
      const response = await fetch(`/api/estimates/${id}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        router.push('/dashboard/estimates');
      }
    } catch (error) {
      console.error('Error deleting estimate:', error);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600 dark:text-gray-400">Laddar offert...</p>
        </div>
      </div>
    );
  }

  if (error || !estimate) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <AlertTriangle className="w-12 h-12 text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
            {error || 'Offerten hittades inte'}
          </h2>
          <Link href="/dashboard/estimates">
            <Button variant="outline">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Tillbaka till offerter
            </Button>
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center space-x-4">
            <Link href="/dashboard/estimates">
              <Button variant="outline" size="sm" className="border-gray-200 dark:border-gray-600">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Tillbaka
              </Button>
            </Link>
            <div>
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                Offert {estimate.estimate_number}
              </h1>
              <p className="text-gray-500 dark:text-gray-400">
                {estimate.project_name || 'Inget projektnamn'}
              </p>
            </div>
          </div>
          <div className="flex items-center space-x-3">
            <StatusBadge status={estimate.status} />
            <Button 
              onClick={() => setShowPDFPreview(true)}
              className="bg-gray-100 hover:bg-gray-200 text-gray-800 px-3 py-1.5 h-8 rounded-md flex items-center space-x-1.5 text-sm font-medium border border-gray-300 transition-colors"
            >
              <Eye className="w-4 h-4" />
              <span>Förhandsvisning</span>
            </Button>
            <FloatingActionPanel triggerText="Actions">
              {/* Editing Actions - Show when editing */}
              {isEditing && (
                <>
                  <FloatingActionItem
                    icon={<Save className="w-4 h-4" />}
                    label="Spara ändringar"
                    onClick={handleSaveChanges}
                    variant="success"
                  />
                  <FloatingActionItem
                    icon={<X className="w-4 h-4" />}
                    label="Avbryt"
                    onClick={() => setIsEditing(false)}
                    variant="danger"
                  />
                </>
              )}
              
              {/* Normal Actions - Show when NOT editing */}
              {!isEditing && (
                <>
                  {/* Status Actions */}
                  {estimate.status === 'draft' && (
                    <FloatingActionItem
                      icon={<Send className="w-4 h-4" />}
                      label="Skicka offert"
                      onClick={() => handleStatusUpdate('sent')}
                      variant="success"
                    />
                  )}
                  {estimate.status === 'sent' && (
                    <>
                      <FloatingActionItem
                        icon={<Check className="w-4 h-4" />}
                        label="Acceptera"
                        onClick={() => handleStatusUpdate('accepted')}
                        variant="success"
                      />
                      <FloatingActionItem
                        icon={<X className="w-4 h-4" />}
                        label="Avvisa"
                        onClick={() => handleStatusUpdate('rejected')}
                        variant="danger"
                      />
                    </>
                  )}

                  <FloatingActionSeparator />

                  {/* View Actions */}
                  <FloatingActionItem
                    icon={<Download className="w-4 h-4" />}
                    label="Ladda ner PDF"
                    onClick={() => window.open(`/api/estimates/${estimate.id}/pdf`, '_blank')}
                    variant="default"
                  />

                  <FloatingActionSeparator />

                  {/* Edit Actions */}
                  <FloatingActionItem
                    icon={<Edit className="w-4 h-4" />}
                    label="Redigera"
                    onClick={() => setIsEditing(true)}
                    variant="warning"
                  />

                  <FloatingActionSeparator />

                  {/* Delete Actions */}
                  <FloatingActionItem
                    icon={<Trash2 className="w-4 h-4" />}
                    label="Ta bort"
                    onClick={() => setShowDeleteDialog(true)}
                    variant="danger"
                  />
                </>
              )}
            </FloatingActionPanel>
          </div>
        </div>

        <div className="grid grid-cols-1 xl:grid-cols-4 gap-6 lg:gap-8">
          {/* Main Content */}
          <div className="xl:col-span-3 space-y-6">
            {/* Basic Information */}
            <FormSection title="Grundläggande information">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Offertnummer
                  </label>
                  <div className="p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <span className="text-gray-900 dark:text-white font-medium">
                      {estimate.estimate_number}
                    </span>
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Datum
                  </label>
                  <div className="p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <span className="text-gray-900 dark:text-white">
                      {formatDate(estimate.date)}
                    </span>
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Giltigt till
                  </label>
                  {isEditing ? (
                    <Input
                      type="date"
                      value={editForm.valid_until}
                      onChange={(e) => setEditForm(prev => ({ ...prev, valid_until: e.target.value }))}
                      className="bg-white dark:bg-gray-800"
                    />
                  ) : (
                    <div className="p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                      <span className="text-gray-900 dark:text-white">
                        {estimate.valid_until ? formatDate(estimate.valid_until) : 'Ej angivet'}
                      </span>
                    </div>
                  )}
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Betalningsvillkor
                  </label>
                  {isEditing ? (
                    <Input
                      value={editForm.payment_terms}
                      onChange={(e) => setEditForm(prev => ({ ...prev, payment_terms: e.target.value }))}
                      placeholder="T.ex. 30 dagar netto"
                      className="bg-white dark:bg-gray-800"
                    />
                  ) : (
                    <div className="p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                      <span className="text-gray-900 dark:text-white">
                        {estimate.payment_terms || 'Ej angivet'}
                      </span>
                    </div>
                  )}
                </div>
                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Projektnamn
                  </label>
                  {isEditing ? (
                    <Input
                      value={editForm.project_name}
                      onChange={(e) => setEditForm(prev => ({ ...prev, project_name: e.target.value }))}
                      placeholder="Projektnamn"
                      className="bg-white dark:bg-gray-800"
                    />
                  ) : (
                    <div className="p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                      <span className="text-gray-900 dark:text-white">
                        {estimate.project_name || 'Ej angivet'}
                      </span>
                    </div>
                  )}
                </div>
                <div className="md:col-span-3">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Projektbeskrivning
                  </label>
                  {isEditing ? (
                    <Textarea
                      value={editForm.description}
                      onChange={(e) => setEditForm(prev => ({ ...prev, description: e.target.value }))}
                      placeholder="Beskriv projektet..."
                      rows={4}
                      className="bg-white dark:bg-gray-800"
                    />
                  ) : (
                    <div className="p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                      <span className="text-gray-900 dark:text-white whitespace-pre-wrap">
                        {estimate.description || 'Ej angivet'}
                      </span>
                    </div>
                  )}
                </div>
              </div>
            </FormSection>

            {/* Customer Information */}
            <FormSection title="Kundinformation">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    <User className="w-4 h-4 inline mr-2" />
                    Kund
                  </label>
                  <div className="p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <span className="text-gray-900 dark:text-white font-medium">
                      {estimate.customers.company_name || estimate.customers.name}
                    </span>
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    <AtSign className="w-4 h-4 inline mr-2" />
                    E-post
                  </label>
                  <div className="p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <span className="text-gray-900 dark:text-white">
                      {estimate.customers.email}
                    </span>
                  </div>
                </div>
                {estimate.customers.phone && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      <Phone className="w-4 h-4 inline mr-2" />
                      Telefon
                    </label>
                    <div className="p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                      <span className="text-gray-900 dark:text-white">
                        {estimate.customers.phone}
                      </span>
                    </div>
                  </div>
                )}
              </div>
            </FormSection>

            {/* Line Items */}
            <FormSection title="Arbetsmoment & Material">
              <div className="space-y-8">
                {/* Work Items */}
                <div>
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center">
                      <Hammer className="w-5 h-5 text-blue-600 mr-2" />
                      <h4 className="text-lg font-medium text-gray-900">Arbetsmoment</h4>
                    </div>
                    {isEditing && (
                      <Button
                        onClick={() => setWorkItems(prev => [...prev, { 
                          id: `temp-${Date.now()}`, 
                          description: '', 
                          quantity: 1, 
                          unitPrice: 0, 
                          total: 0, 
                          unit: 'tim',
                          rotEligible: true 
                        } as WorkItem])}
                        size="sm"
                        className="bg-blue-600 hover:bg-blue-700 text-white"
                      >
                        <Plus className="w-4 h-4 mr-2" />
                        Lägg till arbetsmoment
                      </Button>
                    )}
                  </div>
                  
                  <div className="overflow-x-auto">
                    <table className="w-full border-collapse">
                      <thead>
                        <tr className="border-b border-gray-200">
                          <th className="text-left py-3 text-sm font-medium text-gray-700">Beskrivning</th>
                          <th className="text-right py-3 text-sm font-medium text-gray-700 w-24">Antal</th>
                          <th className="text-right py-3 text-sm font-medium text-gray-700 w-24">Enhet</th>
                          <th className="text-right py-3 text-sm font-medium text-gray-700 w-32">Pris</th>
                          <th className="text-center py-3 text-sm font-medium text-gray-700 w-16">ROT</th>
                          <th className="text-right py-3 text-sm font-medium text-gray-700 w-32">Totalt</th>
                          {isEditing && <th className="text-center py-3 text-sm font-medium text-gray-700 w-16">Ta bort</th>}
                        </tr>
                      </thead>
                      <tbody>
                        {workItems.map((item: WorkItem, index: number) => {
                          const total = item.quantity * item.unitPrice;
                          return (
                            <tr key={item.id} className="border-b border-gray-100">
                              <td className="py-3">
                                {isEditing ? (
                                  <Input
                                    value={item.description}
                                    onChange={(e) => setWorkItems(prev => prev.map((i, iIndex) =>
                                      iIndex === index ? { ...i, description: e.target.value } : i
                                    ))}
                                    placeholder="Beskrivning av arbetsmoment"
                                    className="bg-white dark:bg-gray-800"
                                  />
                                ) : (
                                  <span className="text-gray-900">{item.description}</span>
                                )}
                              </td>
                              <td className="text-right py-3">
                                {isEditing ? (
                                  <Input
                                    type="number"
                                    value={item.quantity}
                                    onChange={(e) => setWorkItems(prev => prev.map((i, iIndex) =>
                                      iIndex === index ? { ...i, quantity: parseFloat(e.target.value) || 0, total: (parseFloat(e.target.value) || 0) * i.unitPrice } : i
                                    ))}
                                    className="bg-white dark:bg-gray-800 w-20 text-right"
                                    min="0"
                                    step="0.1"
                                  />
                                ) : (
                                  <span className="text-gray-900">{item.quantity}</span>
                                )}
                              </td>
                              <td className="text-right py-3">
                                {isEditing ? (
                                  <Input
                                    value={item.unit || 'tim'}
                                    onChange={(e) => setWorkItems(prev => prev.map((i, iIndex) =>
                                      iIndex === index ? { ...i, unit: e.target.value } : i
                                    ))}
                                    className="bg-white dark:bg-gray-800 w-16 text-right"
                                  />
                                ) : (
                                  <span className="text-gray-600">{item.unit || 'tim'}</span>
                                )}
                              </td>
                              <td className="text-right py-3">
                                {isEditing ? (
                                  <Input
                                    type="number"
                                    value={item.unitPrice}
                                    onChange={(e) => setWorkItems(prev => prev.map((i, iIndex) =>
                                      iIndex === index ? { ...i, unitPrice: parseFloat(e.target.value) || 0, total: i.quantity * (parseFloat(e.target.value) || 0) } : i
                                    ))}
                                    className="bg-white dark:bg-gray-800 w-24 text-right"
                                    min="0"
                                    step="0.01"
                                  />
                                ) : (
                                  <span className="text-gray-900">{formatCurrency(item.unitPrice)}</span>
                                )}
                              </td>
                              <td className="text-center py-3">
                                {isEditing ? (
                                  <input
                                    type="checkbox"
                                    checked={item.rotEligible || false}
                                    onChange={(e) => setWorkItems(prev => prev.map((i, iIndex) =>
                                      iIndex === index ? { ...i, rotEligible: e.target.checked } : i
                                    ))}
                                    className="w-4 h-4 text-green-600 bg-gray-100 border-gray-300 rounded focus:ring-green-500"
                                  />
                                ) : (
                                  item.rotEligible ? (
                                    <CheckCircle2 className="w-4 h-4 text-green-500 mx-auto" />
                                  ) : (
                                    <XCircle className="w-4 h-4 text-red-500 mx-auto" />
                                  )
                                )}
                              </td>
                              <td className="text-right py-3 text-gray-900">
                                {formatCurrency(item.total)}
                              </td>
                              {isEditing && (
                                <td className="text-center py-3">
                                  <Button
                                    onClick={() => setWorkItems(prev => prev.filter((_, i) => i !== index))}
                                    size="sm"
                                    variant="outline"
                                    className="text-red-600 hover:text-red-700 hover:bg-red-50"
                                  >
                                    <MinusCircle className="w-4 h-4" />
                                  </Button>
                                </td>
                              )}
                            </tr>
                          );
                        })}
                        
                        {workItems.length === 0 && (
                          <tr>
                            <td colSpan={isEditing ? 7 : 6} className="py-8 text-center text-gray-500">
                              {isEditing ? 'Inga arbetsmoment ännu. Klicka "Lägg till" för att lägga till ett.' : 'Inga arbetsmoment tillagda ännu.'}
                            </td>
                          </tr>
                        )}
                      </tbody>
                    </table>
                  </div>
                </div>

                {/* Material Items */}
                <div>
                  <div className="flex items-center justify-between mb-4 mt-8">
                    <div className="flex items-center">
                      <Calculator className="w-5 h-5 text-blue-600 mr-2" />
                      <h4 className="text-lg font-medium text-gray-900">Material</h4>
                    </div>
                    {isEditing && (
                      <Button
                        onClick={() => setMaterialItems(prev => [...prev, { 
                          id: `temp-${Date.now()}`, 
                          articleNumber: '', 
                          description: '', 
                          quantity: 1, 
                          unitPrice: 0, 
                          total: 0, 
                          unit: 'st'
                        } as MaterialItem])}
                        size="sm"
                        className="bg-blue-600 hover:bg-blue-700 text-white"
                      >
                        <Plus className="w-4 h-4 mr-2" />
                        Lägg till material
                      </Button>
                    )}
                  </div>
                  
                  <div className="overflow-x-auto">
                    <table className="w-full border-collapse">
                      <thead>
                        <tr className="border-b border-gray-200">
                          <th className="text-left py-3 text-sm font-medium text-gray-700">Artikel</th>
                          <th className="text-left py-3 text-sm font-medium text-gray-700">Beskrivning</th>
                          <th className="text-right py-3 text-sm font-medium text-gray-700 w-24">Antal</th>
                          <th className="text-right py-3 text-sm font-medium text-gray-700 w-24">Enhet</th>
                          <th className="text-right py-3 text-sm font-medium text-gray-700 w-32">Pris</th>
                          <th className="text-right py-3 text-sm font-medium text-gray-700 w-32">Totalt</th>
                          {isEditing && <th className="text-center py-3 text-sm font-medium text-gray-700 w-16">Ta bort</th>}
                        </tr>
                      </thead>
                      <tbody>
                        {materialItems.map((item: MaterialItem, index: number) => {
                          const total = item.quantity * item.unitPrice;
                          return (
                            <tr key={item.id} className="border-b border-gray-100">
                              <td className="py-3">
                                {isEditing ? (
                                  <Input
                                    value={item.articleNumber}
                                    onChange={(e) => setMaterialItems(prev => prev.map((i, iIndex) =>
                                      iIndex === index ? { ...i, articleNumber: e.target.value } : i
                                    ))}
                                    placeholder="Artikelnummer"
                                    className="bg-white dark:bg-gray-800 w-24"
                                  />
                                ) : (
                                  <span className="text-sm text-gray-600">{item.articleNumber}</span>
                                )}
                              </td>
                              <td className="py-3">
                                {isEditing ? (
                                  <Input
                                    value={item.description}
                                    onChange={(e) => setMaterialItems(prev => prev.map((i, iIndex) =>
                                      iIndex === index ? { ...i, description: e.target.value } : i
                                    ))}
                                    placeholder="Beskrivning av material"
                                    className="bg-white dark:bg-gray-800"
                                  />
                                ) : (
                                  <span className="text-gray-900">{item.description}</span>
                                )}
                              </td>
                              <td className="text-right py-3">
                                {isEditing ? (
                                  <Input
                                    type="number"
                                    value={item.quantity}
                                    onChange={(e) => setMaterialItems(prev => prev.map((i, iIndex) =>
                                      iIndex === index ? { ...i, quantity: parseFloat(e.target.value) || 0, total: (parseFloat(e.target.value) || 0) * i.unitPrice } : i
                                    ))}
                                    className="bg-white dark:bg-gray-800 w-20 text-right"
                                    min="0"
                                    step="0.1"
                                  />
                                ) : (
                                  <span className="text-gray-900">{item.quantity}</span>
                                )}
                              </td>
                              <td className="text-right py-3">
                                {isEditing ? (
                                  <Input
                                    value={item.unit || 'st'}
                                    onChange={(e) => setMaterialItems(prev => prev.map((i, iIndex) =>
                                      iIndex === index ? { ...i, unit: e.target.value } : i
                                    ))}
                                    className="bg-white dark:bg-gray-800 w-16 text-right"
                                  />
                                ) : (
                                  <span className="text-gray-600">{item.unit || 'st'}</span>
                                )}
                              </td>
                              <td className="text-right py-3">
                                {isEditing ? (
                                  <Input
                                    type="number"
                                    value={item.unitPrice}
                                    onChange={(e) => setMaterialItems(prev => prev.map((i, iIndex) =>
                                      iIndex === index ? { ...i, unitPrice: parseFloat(e.target.value) || 0, total: i.quantity * (parseFloat(e.target.value) || 0) } : i
                                    ))}
                                    className="bg-white dark:bg-gray-800 w-24 text-right"
                                    min="0"
                                    step="0.01"
                                  />
                                ) : (
                                  <span className="text-gray-900">{formatCurrency(item.unitPrice)}</span>
                                )}
                              </td>
                              <td className="text-right py-3 text-gray-900">
                                {formatCurrency(item.total)}
                              </td>
                              {isEditing && (
                                <td className="text-center py-3">
                                  <Button
                                    onClick={() => setMaterialItems(prev => prev.filter((_, i) => i !== index))}
                                    size="sm"
                                    variant="outline"
                                    className="text-red-600 hover:text-red-700 hover:bg-red-50"
                                  >
                                    <MinusCircle className="w-4 h-4" />
                                  </Button>
                                </td>
                              )}
                            </tr>
                          );
                        })}
                        
                        {materialItems.length === 0 && (
                          <tr>
                            <td colSpan={isEditing ? 7 : 6} className="py-8 text-center text-gray-500">
                              {isEditing ? 'Inga material ännu. Klicka "Lägg till" för att lägga till material.' : 'Inga material tillagda ännu.'}
                            </td>
                          </tr>
                        )}
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </FormSection>

            {/* Additional Information */}
            <FormSection title="Ytterligare information">
              <div className="space-y-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Inledande text
                  </label>
                  {isEditing ? (
                    <Textarea
                      value={editForm.intro_text}
                      onChange={(e) => setEditForm(prev => ({ ...prev, intro_text: e.target.value }))}
                      placeholder="Inledande text för offerten..."
                      rows={3}
                      className="bg-white dark:bg-gray-800"
                    />
                  ) : (
                    <div className="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                      <span className="text-gray-900 dark:text-white whitespace-pre-wrap">
                        {estimate.intro_text || 'Ej angivet'}
                      </span>
                    </div>
                  )}
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Avslutande text
                  </label>
                  {isEditing ? (
                    <Textarea
                      value={editForm.closing_text}
                      onChange={(e) => setEditForm(prev => ({ ...prev, closing_text: e.target.value }))}
                      placeholder="Avslutande text för offerten..."
                      rows={3}
                      className="bg-white dark:bg-gray-800"
                    />
                  ) : (
                    <div className="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                      <span className="text-gray-900 dark:text-white whitespace-pre-wrap">
                        {estimate.closing_text || 'Ej angivet'}
                      </span>
                    </div>
                  )}
                </div>
              </div>
            </FormSection>
          </div>

          {/* Summary Sidebar */}
          <div className="xl:col-span-1 space-y-6 sticky top-6 self-start">
            <FormSection title="Sammanfattning">
              <div className="space-y-4">
                {workItems.length > 0 && (
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600 dark:text-gray-400">Arbetsmoment:</span>
                    <span className="font-medium text-gray-900 dark:text-white text-sm">
                      {formatCurrency(workItems.reduce((sum, item) => sum + (item.quantity * item.unitPrice), 0))}
                    </span>
                  </div>
                )}
                {materialItems.length > 0 && (
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600 dark:text-gray-400">Material:</span>
                    <span className="font-medium text-gray-900 dark:text-white text-sm">
                      {formatCurrency(materialItems.reduce((sum, item) => sum + (item.quantity * item.unitPrice), 0))}
                    </span>
                  </div>
                )}
                <div className="border-t border-gray-200 dark:border-gray-700 pt-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600 dark:text-gray-400">Delsumma:</span>
                    <span className="font-medium text-gray-900 dark:text-white text-sm">
                      {formatCurrency(estimate.subtotal || (workItems.reduce((sum, item) => sum + (item.quantity * item.unitPrice), 0) + materialItems.reduce((sum, item) => sum + (item.quantity * item.unitPrice), 0)))}
                    </span>
                  </div>
                  <div className="flex justify-between items-center mt-2">
                    <span className="text-base font-semibold text-gray-900 dark:text-white">Totalt:</span>
                    <span className="text-base font-bold text-blue-600 dark:text-blue-400">
                      {formatCurrency(estimate.total_amount || (workItems.reduce((sum, item) => sum + (item.quantity * item.unitPrice), 0) + materialItems.reduce((sum, item) => sum + (item.quantity * item.unitPrice), 0)))}
                    </span>
                  </div>
                </div>
                {workItems.filter((item: WorkItem) => item.rotEligible).length > 0 && (
                  <div className="bg-green-50 dark:bg-green-900/20 p-3 rounded-lg">
                    <p className="text-sm text-green-700 dark:text-green-300">
                      <strong>ROT-avdrag tillämpligt</strong><br />
                      Kunden kan få skattereduktion för arbetskostnader.
                    </p>
                  </div>
                )}
              </div>
            </FormSection>

            {/* Metadata */}
            <FormSection title="Information">
              <div className="space-y-3 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Skapad:</span>
                  <span className="text-gray-900 dark:text-white">
                    {formatDate(estimate.created_at)}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Uppdaterad:</span>
                  <span className="text-gray-900 dark:text-white">
                    {formatDate(estimate.updated_at)}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Status:</span>
                  <StatusBadge status={estimate.status} />
                </div>
              </div>
            </FormSection>
          </div>
        </div>
      </div>

      {/* PDF Preview Modal */}
      {showPDFPreview && (
        <PDFPreviewModal
          isOpen={showPDFPreview}
          onClose={() => setShowPDFPreview(false)}
          estimate={estimate}
          workItems={workItems}
          materialItems={materialItems}
        />
      )}

      {/* Delete Confirmation Dialog */}
      {showDeleteDialog && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-md w-full">
            <div className="flex items-center mb-4">
              <Trash2 className="w-6 h-6 text-red-600 mr-3" />
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">Bekräfta borttagning</h3>
            </div>
            <p className="text-gray-600 dark:text-gray-300 mb-6">
              Är du säker på att du vill ta bort denna offert? Detta kan inte ångras.
            </p>
            <div className="flex justify-end space-x-3">
              <Button 
                variant="outline" 
                onClick={() => setShowDeleteDialog(false)}
              >
                Avbryt
              </Button>
              <Button 
                onClick={() => {
                  handleDelete();
                  setShowDeleteDialog(false);
                }}
                className="bg-red-600 hover:bg-red-700 text-white"
              >
                Ta bort
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
} 