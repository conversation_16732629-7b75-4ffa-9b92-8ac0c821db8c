"use client"

import React, { useState, useEffect, useRef, Suspense } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { AutogrowingTextarea } from "@/components/ui/autogrowing-textarea";
import { 
  FileText, Calendar, Receipt, 
  Plus, MinusCircle, ChevronLeft, 
  Check, Save, Send, ArrowLeft, CheckCircle2, User, Building, Phone,
  AtSign, MapPin, CalendarIcon, CreditCardIcon, AlertTriangle, Briefcase, Calculator,
  Eye, X, Hammer, Wrench, Zap, Droplets, Search, ChevronDown
} from 'lucide-react';
import Link from "next/link";
import { Card } from "@/components/ui/card";
import { supabase } from "@/lib/supabase/supabase";

interface LineItem {
  id: string;
  description: string;
  quantity: number;
  unitPrice: number;
  total: number;
  unit?: string;
  rotEligible?: boolean;
}

interface MaterialItem {
  id: string;
  articleNumber: string;
  description: string;
  quantity: number;
  unitPrice: number;
  total: number;
  unit?: string;
}

interface FormState {
  estimateNumber: string;
  customerId: string;
  customerName: string;
  customerEmail: string;
  customerPhone: string;
  customerAddress: string;
  projectName: string;
  description: string;
  validUntil: string;
  status: string;
  workItems: LineItem[];
  materialItems: MaterialItem[];
  notes: string;
  subtotal: number;
  vatAmount: number;
  totalAmount: number;
  vatRate: number;
  introText: string;
  closingText: string;
  paymentTerms: string;
}

interface Customer {
  id: string;
  name: string;
  company_name: string;
  email: string;
  phone: string;
  address: string;
}

// Carpentry/Construction specific line item templates
const carpentryLineItems = [
  { description: "Snickeriarbete - Köksinredning", unit: "tim", unitPrice: 650, rotEligible: true },
  { description: "Material - Köksluckor och stommar", unit: "st", unitPrice: 25000, rotEligible: false },
  { description: "Installation - Vitvaror", unit: "tim", unitPrice: 550, rotEligible: true },
  { description: "Måleriarbete - Väggar och tak", unit: "m²", unitPrice: 180, rotEligible: true },
  { description: "Elarbete - Belysning och uttag", unit: "tim", unitPrice: 750, rotEligible: true },
  { description: "VVS-arbete - Kranar och rör", unit: "tim", unitPrice: 700, rotEligible: true },
  { description: "Kakelläggning - Stänkskydd", unit: "m²", unitPrice: 450, rotEligible: true },
  { description: "Golvläggning - Parkett/Klinker", unit: "m²", unitPrice: 320, rotEligible: true },
  { description: "Rivningsarbete", unit: "tim", unitPrice: 450, rotEligible: true },
  { description: "Städning efter arbete", unit: "tim", unitPrice: 350, rotEligible: true },
];

// Common construction materials
const materialItems = [
  { articleNumber: "TR-001", description: "Plywood 18mm", unit: "m²", unitPrice: 450 },
  { articleNumber: "TR-002", description: "Reglar 45x95mm", unit: "m", unitPrice: 28 },
  { articleNumber: "HW-001", description: "Spånskruv 4x50mm", unit: "förp", unitPrice: 85 },
  { articleNumber: "HW-002", description: "Gångjärn 75mm", unit: "st", unitPrice: 45 },
  { articleNumber: "FN-001", description: "Grundfärg vit", unit: "liter", unitPrice: 180 },
  { articleNumber: "FN-002", description: "Lasyr ek", unit: "liter", unitPrice: 220 },
  { articleNumber: "EL-001", description: "Kabel 1.5mm²", unit: "m", unitPrice: 12 },
  { articleNumber: "VVS-001", description: "Köksblandare", unit: "st", unitPrice: 850 },
];

const FormSection = ({ title, children, className }: { title?: string, children: React.ReactNode, className?: string }) => (
  <div className={`bg-white dark:bg-gray-800 rounded-xl border border-gray-100 dark:border-gray-700 shadow-sm ${className}`}>
    {title && (
      <div className="px-6 py-4 border-b border-gray-100 dark:border-gray-700">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white">{title}</h3>
      </div>
    )}
    <div className="p-6">{children}</div>
  </div>
);

const FormLabel = ({ htmlFor, children, className, optional }: { htmlFor: string, children: React.ReactNode, className?: string, optional?: boolean }) => (
  <label htmlFor={htmlFor} className={`block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 ${className}`}>
    {children}
    {optional && <span className="text-gray-400 ml-1">(valfritt)</span>}
  </label>
);

const MaterialInput = ({ id, name, value, onChange, placeholder, type = "text", className, readOnly, min, max, step }: { 
  id: string, 
  name: string, 
  value: any, 
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void, 
  placeholder?: string, 
  type?: string,
  className?: string,
  readOnly?: boolean,
  min?: string,
  max?: string,
  step?: string
}) => (
  <Input
    id={id}
    name={name}
    type={type}
    value={value}
    onChange={onChange}
    placeholder={placeholder}
    readOnly={readOnly}
    min={min}
    max={max}
    step={step}
    className={`border-gray-200 dark:border-gray-600 focus:border-blue-500 focus:ring-blue-500 ${className}`}
  />
);

const StatusBadge = ({ status }: { status: string }) => {
  const getStatusStyles = () => {
    switch (status) {
      case 'draft':
        return 'bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300';
      case 'sent':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400';
      case 'accepted':
        return 'bg-green-100 text-green-700 dark:bg-green-900/20 dark:text-green-400';
      case 'rejected':
        return 'bg-red-100 text-red-700 dark:bg-red-900/20 dark:text-red-400';
      default:
        return 'bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300';
    }
  };

  const statusLabels = {
    'draft': 'Utkast',
    'sent': 'Skickad',
    'accepted': 'Accepterad',
    'rejected': 'Avvisad'
  };

  return (
    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusStyles()}`}>
      {statusLabels[status as keyof typeof statusLabels] || status}
    </span>
  );
};

// Auto-expanding textarea component for descriptions
const ExpandingTextarea = ({ 
  id, 
  value, 
  onChange, 
  placeholder, 
  maxLength = 500,
  className = "" 
}: { 
  id: string, 
  value: string, 
  onChange: (value: string) => void, 
  placeholder?: string,
  maxLength?: number,
  className?: string
}) => {
  const [isFocused, setIsFocused] = useState(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Auto-resize functionality
  const adjustHeight = () => {
    const textarea = textareaRef.current;
    if (textarea) {
      textarea.style.height = 'auto';
      const scrollHeight = textarea.scrollHeight;
      const maxHeight = isFocused ? 120 : 60; // Expand when focused
      textarea.style.height = `${Math.min(scrollHeight, maxHeight)}px`;
    }
  };

  useEffect(() => {
    adjustHeight();
  }, [value, isFocused]);

  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newValue = e.target.value;
    if (newValue.length <= maxLength) {
      onChange(newValue);
    }
  };

  const handleFocus = () => {
    setIsFocused(true);
  };

  const handleBlur = () => {
    setIsFocused(false);
  };

  return (
    <div className="relative">
      <textarea
        ref={textareaRef}
        id={id}
        value={value}
        onChange={handleChange}
        onFocus={handleFocus}
        onBlur={handleBlur}
        placeholder={placeholder}
        className={`w-full px-3 py-2 border border-gray-200 dark:border-gray-600 rounded-lg focus:border-blue-500 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-900 dark:text-white resize-none transition-all duration-200 ${className}`}
        style={{ 
          minHeight: '40px',
          maxHeight: isFocused ? '120px' : '60px'
        }}
      />
      <div className="absolute bottom-1 right-2 text-xs text-gray-400 pointer-events-none">
        {value.length}/{maxLength}
      </div>
    </div>
  );
};

// PDF Preview Modal Component
const PDFPreviewModal = ({ isOpen, onClose, formData }: { isOpen: boolean, onClose: () => void, formData: FormState }) => {
  if (!isOpen) return null;

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('sv-SE', { style: 'currency', currency: 'SEK' }).format(amount);
  };

  const formatDate = (dateString: string) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString('sv-SE');
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-800 rounded-lg max-w-4xl w-full max-h-[90vh] overflow-hidden">
        <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">Förhandsgranska offert</h3>
          <Button variant="outline" size="sm" onClick={onClose}>
            <X className="w-4 h-4" />
          </Button>
        </div>
        
        <div className="overflow-y-auto max-h-[calc(90vh-120px)]">
          <div className="p-8 bg-white text-gray-900" style={{ fontFamily: 'Helvetica, Arial, sans-serif' }}>
            {/* Header */}
            <div className="border-b-4 border-blue-600 pb-6 mb-8">
              <div className="text-right text-gray-600 text-sm mb-4">
                <strong>Ditt Snickeriföretag AB</strong><br />
                Hantverkargatan 123<br />
                123 45 Stockholm<br />
                Tel: 08-123 456 78<br />
                <EMAIL>
              </div>
              
              <div className="text-3xl font-bold text-blue-600 mb-2">OFFERT</div>
              <div className="text-lg text-gray-600 mb-4">
                {formData.estimateNumber}
                <StatusBadge status={formData.status} />
              </div>
            </div>

            {/* Customer and Estimate Info */}
            <div className="grid grid-cols-2 gap-8 mb-8">
              <div>
                <h3 className="text-lg font-bold text-blue-600 mb-3 border-b border-gray-200 pb-1">Kund</h3>
                <p><strong>{formData.customerName}</strong></p>
                {formData.customerEmail && <p>📧 {formData.customerEmail}</p>}
                {formData.customerPhone && <p>📞 {formData.customerPhone}</p>}
                {formData.customerAddress && <p>📍 {formData.customerAddress}</p>}
              </div>
              
              <div>
                <h3 className="text-lg font-bold text-blue-600 mb-3 border-b border-gray-200 pb-1">Offertinformation</h3>
                {formData.projectName && <p><strong>Projekt:</strong> {formData.projectName}</p>}
                <p><strong>Datum:</strong> {formatDate(new Date().toISOString())}</p>
                <p><strong>Giltigt till:</strong> {formatDate(formData.validUntil)}</p>
                <p><strong>Betalningsvillkor:</strong> {formData.paymentTerms}</p>
              </div>
            </div>

            {/* Intro Text */}
            {formData.introText && (
              <div className="bg-gray-50 p-6 rounded-lg mb-8">
                <h3 className="text-lg font-bold text-blue-600 mb-3">Inledning</h3>
                <p className="whitespace-pre-wrap">{formData.introText}</p>
              </div>
            )}

            {/* Project Description */}
            {formData.description && (
              <div className="bg-gray-50 p-6 rounded-lg mb-8">
                <h3 className="text-lg font-bold text-blue-600 mb-3">Projektbeskrivning</h3>
                <p className="whitespace-pre-wrap">{formData.description}</p>
              </div>
            )}

            {/* Line Items */}
            <div className="mb-8">
              {/* Work Items */}
              {formData.workItems.length > 0 && (
                <>
                  <h3 className="text-lg font-bold text-blue-600 mb-4">Arbetsmoment</h3>
                  <table className="w-full border-collapse mb-6">
                    <thead>
                      <tr className="border-b-2 border-gray-200">
                        <th className="text-left py-3 text-gray-700">Beskrivning</th>
                        <th className="text-right py-3 text-gray-700 w-20">Antal</th>
                        <th className="text-right py-3 text-gray-700 w-32">Pris/enhet</th>
                        <th className="text-right py-3 text-gray-700 w-32">Summa</th>
                      </tr>
                    </thead>
                    <tbody>
                      {formData.workItems.map((item) => (
                        <tr key={item.id} className="border-b border-gray-100">
                          <td className="py-3">
                            <strong>{item.description}</strong>
                            {item.rotEligible && (
                              <>
                                <br />
                                <span className="inline-block bg-green-100 text-green-700 px-2 py-1 rounded text-xs font-bold mt-1">ROT-berättigad</span>
                              </>
                            )}
                          </td>
                          <td className="text-right py-3">{item.quantity} {item.unit || 'tim'}</td>
                          <td className="text-right py-3">{formatCurrency(item.unitPrice)}</td>
                          <td className="text-right py-3">{formatCurrency(item.total)}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </>
              )}

              {/* Material Items */}
              {formData.materialItems.length > 0 && (
                <>
                  <h3 className="text-lg font-bold text-blue-600 mb-4">Material</h3>
                  <table className="w-full border-collapse">
                    <thead>
                      <tr className="border-b-2 border-gray-200">
                        <th className="text-left py-3 text-gray-700">Beskrivning</th>
                        <th className="text-left py-3 text-gray-700 w-24">Art.nr</th>
                        <th className="text-right py-3 text-gray-700 w-20">Antal</th>
                        <th className="text-right py-3 text-gray-700 w-32">Pris/enhet</th>
                        <th className="text-right py-3 text-gray-700 w-32">Summa</th>
                      </tr>
                    </thead>
                    <tbody>
                      {formData.materialItems.map((item) => (
                        <tr key={item.id} className="border-b border-gray-100">
                          <td className="py-3">
                            <strong>{item.description}</strong>
                          </td>
                          <td className="py-3 text-sm text-gray-600">{item.articleNumber}</td>
                          <td className="text-right py-3">{item.quantity} {item.unit || 'st'}</td>
                          <td className="text-right py-3">{formatCurrency(item.unitPrice)}</td>
                          <td className="text-right py-3">{formatCurrency(item.total)}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </>
              )}
            </div>

            {/* Summary */}
            <div className="border-t-2 border-gray-200 pt-6 mb-8">
              <table className="w-80 ml-auto">
                <tbody>
                  <tr>
                    <td className="py-2">Subtotal:</td>
                    <td className="text-right py-2">{formatCurrency(formData.subtotal)}</td>
                  </tr>
                  <tr>
                    <td className="py-2">Moms ({formData.vatRate}%):</td>
                    <td className="text-right py-2">{formatCurrency(formData.vatAmount)}</td>
                  </tr>
                  <tr className="border-t-2 border-blue-600 font-bold text-lg text-blue-600">
                    <td className="py-3"><strong>TOTALT:</strong></td>
                    <td className="text-right py-3"><strong>{formatCurrency(formData.totalAmount)}</strong></td>
                  </tr>
                </tbody>
              </table>
            </div>

            {/* Closing Text */}
            {formData.closingText && (
              <div className="bg-gray-50 p-6 rounded-lg mb-8">
                <h4 className="font-bold text-blue-600 mb-3">Avslutande information</h4>
                <p className="whitespace-pre-wrap">{formData.closingText}</p>
              </div>
            )}

            {/* Terms */}
            <div className="bg-gray-50 p-6 rounded-lg">
              <h4 className="font-bold text-blue-600 mb-3">Allmänna villkor</h4>
              <p>Denna offert är giltig till {formatDate(formData.validUntil)}. Betalning sker enligt överenskomna betalningsvillkor: {formData.paymentTerms}.</p>
              <p className="mt-2">Vi reserverar oss för eventuella tryckfel och prisändringar.</p>
              <p className="mt-4 italic">Tack för ert förtroende!</p>
            </div>
          </div>
        </div>
        
        <div className="flex items-center justify-end gap-3 p-4 border-t border-gray-200 dark:border-gray-700">
          <Button variant="outline" onClick={() => window.print()}>
            <FileText className="w-4 h-4 mr-2" />
            Skriv ut
          </Button>
          <Button onClick={onClose}>
            Stäng
          </Button>
        </div>
      </div>
    </div>
  );
};

// Custom Searchable Customer Select Component
const SearchableCustomerSelect = ({ 
  customers, 
  value, 
  onValueChange, 
  placeholder = "Välj kund..." 
}: {
  customers: Customer[];
  value: string;
  onValueChange: (value: string) => void;
  placeholder?: string;
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const dropdownRef = useRef<HTMLDivElement>(null);
  const searchInputRef = useRef<HTMLInputElement>(null);

  // Filter customers based on search term
  const filteredCustomers = customers.filter(customer => {
    const searchLower = searchTerm.toLowerCase();
    const name = (customer.company_name || customer.name || '').toLowerCase();
    const email = (customer.email || '').toLowerCase();
    return name.includes(searchLower) || email.includes(searchLower);
  });

  // Get selected customer display name
  const selectedCustomer = customers.find(c => c.id === value);
  const displayName = selectedCustomer ? (selectedCustomer.company_name || selectedCustomer.name) : '';

  // Handle click outside to close dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
        setSearchTerm('');
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Focus search input when dropdown opens
  useEffect(() => {
    if (isOpen && searchInputRef.current) {
      searchInputRef.current.focus();
    }
  }, [isOpen]);

  const handleSelect = (customerId: string) => {
    onValueChange(customerId);
    setIsOpen(false);
    setSearchTerm('');
  };

  return (
    <div className="relative" ref={dropdownRef}>
      {/* Trigger Button */}
      <button
        type="button"
        onClick={() => setIsOpen(!isOpen)}
        className="flex h-10 w-full items-center justify-between rounded-md border border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-800 px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
      >
        <span className={displayName ? "text-gray-900 dark:text-gray-100" : "text-gray-500 dark:text-gray-400"}>
          {displayName || placeholder}
        </span>
        <ChevronDown className={`h-4 w-4 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
      </button>

      {/* Dropdown Content */}
      {isOpen && (
        <div className="absolute top-full left-0 right-0 z-50 mt-1 max-h-80 overflow-hidden rounded-md border border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-800 shadow-lg">
          {/* Search Input */}
          <div className="p-3 border-b border-gray-200 dark:border-gray-600">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                ref={searchInputRef}
                type="text"
                placeholder="Sök kund..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-3 py-2 text-sm border border-gray-200 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder:text-gray-500 dark:placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          </div>

          {/* Customer List */}
          <div className="max-h-60 overflow-y-auto">
            {filteredCustomers.length > 0 ? (
              filteredCustomers.map((customer) => (
                <button
                  key={customer.id}
                  type="button"
                  onClick={() => handleSelect(customer.id)}
                  className={`w-full px-3 py-3 text-left text-sm hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:bg-gray-50 dark:focus:bg-gray-700 border-b border-gray-100 dark:border-gray-700 last:border-b-0 ${
                    value === customer.id ? 'bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300' : 'text-gray-900 dark:text-gray-100'
                  }`}
                >
                  <div className="flex items-center space-x-3">
                    <div className="flex-shrink-0">
                      <div className="w-8 h-8 bg-gray-200 dark:bg-gray-600 rounded-full flex items-center justify-center">
                        {customer.company_name ? (
                          <Building className="w-4 h-4 text-gray-600 dark:text-gray-300" />
                        ) : (
                          <User className="w-4 h-4 text-gray-600 dark:text-gray-300" />
                        )}
                      </div>
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="font-medium truncate">
                        {customer.company_name || customer.name}
                      </div>
                      {customer.email && (
                        <div className="text-xs text-gray-500 dark:text-gray-400 truncate">
                          {customer.email}
                        </div>
                      )}
                    </div>
                    {value === customer.id && (
                      <Check className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                    )}
                  </div>
                </button>
              ))
            ) : (
              <div className="px-3 py-6 text-center text-sm text-gray-500 dark:text-gray-400">
                <Search className="w-8 h-8 mx-auto mb-2 text-gray-300 dark:text-gray-600" />
                <div>Inga kunder hittades</div>
                <div className="text-xs mt-1">Försök med ett annat sökord</div>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

function CreateEstimatePageContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  
  const [loading, setLoading] = useState(false);
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [showPDFPreview, setShowPDFPreview] = useState(false);

  const [formData, setFormData] = useState<FormState>({
    estimateNumber: '',
    customerId: '',
    customerName: '',
    customerEmail: '',
    customerPhone: '',
    customerAddress: '',
    projectName: '',
    description: '',
    validUntil: '',
    status: 'draft',
    workItems: [
      {
        id: '1',
        description: 'Snickeriarbete - Köksinredning',
        quantity: 40,
        unitPrice: 650,
        total: 26000,
        unit: 'tim',
        rotEligible: true
      }
    ],
    materialItems: [
      {
        id: '1',
        articleNumber: 'KL-001',
        description: 'Köksluckor och stommar',
        quantity: 1,
        unitPrice: 25000,
        total: 25000,
        unit: 'st'
      }
    ],
    notes: '',
    subtotal: 0,
    vatAmount: 0,
    totalAmount: 0,
    vatRate: 25,
    introText: 'Vi tackar för förtroendet att få lämna denna offert för ert köksprojekt.',
    closingText: 'Alla arbeten utförs enligt gällande branschstandarder och med 5 års garanti på utfört arbete.',
    paymentTerms: '30 dagar netto'
  });

  // Fetch customers on component mount
  useEffect(() => {
    fetchCustomers();
    generateEstimateNumber();
    setDefaultValidUntil();
  }, []);

  // Auto-populate valid until date (30 days from now)
  const setDefaultValidUntil = () => {
    const validUntil = new Date();
    validUntil.setDate(validUntil.getDate() + 30);
    setFormData(prev => ({
      ...prev,
      validUntil: validUntil.toISOString().split('T')[0]
    }));
  };

  // Recalculate totals when work items and material items change
  useEffect(() => {
    const workSubtotal = formData.workItems.reduce((sum, item) => sum + item.total, 0);
    const materialSubtotal = formData.materialItems.reduce((sum, item) => sum + item.total, 0);
    const subtotal = workSubtotal + materialSubtotal;
    const vatAmount = subtotal * (formData.vatRate / 100);
    const totalAmount = subtotal + vatAmount;

    setFormData(prev => ({
      ...prev,
      subtotal,
      vatAmount,
      totalAmount
    }));
  }, [formData.workItems, formData.materialItems, formData.vatRate]);

  const fetchCustomers = async () => {
    try {
      const { data, error } = await supabase
        .from('customers')
        .select('id, name, company_name, email, phone, address')
        .order('name');

      if (error) throw error;
      setCustomers(data || []);
    } catch (error) {
      console.error('Error fetching customers:', error);
    }
  };

  const generateEstimateNumber = async () => {
    try {
      const { data, error } = await supabase
        .from('estimates')
        .select('estimate_number')
        .order('created_at', { ascending: false })
        .limit(1);

      if (error) throw error;

      let nextNumber = 1;
      if (data && data.length > 0) {
        const lastNumber = data[0].estimate_number;
        const match = lastNumber.match(/EST-(\d+)/);
        if (match) {
          nextNumber = parseInt(match[1]) + 1;
        }
      }

      const estimateNumber = `EST-${nextNumber.toString().padStart(4, '0')}`;
      
      setFormData(prev => ({
        ...prev,
        estimateNumber
      }));
    } catch (error) {
      console.error('Error generating estimate number:', error);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleLineItemChange = (id: string, field: keyof LineItem, value: any) => {
    setFormData(prev => ({
      ...prev,
      workItems: prev.workItems.map(item => {
        if (item.id === id) {
          const updatedItem = { ...item, [field]: value };
          if (field === 'quantity' || field === 'unitPrice') {
            updatedItem.total = updatedItem.quantity * updatedItem.unitPrice;
          }
          return updatedItem;
        }
        return item;
      })
    }));
  };

  const handleMaterialItemChange = (id: string, field: keyof MaterialItem, value: any) => {
    setFormData(prev => ({
      ...prev,
      materialItems: prev.materialItems.map(item => {
        if (item.id === id) {
          const updatedItem = { ...item, [field]: value };
          if (field === 'quantity' || field === 'unitPrice') {
            updatedItem.total = updatedItem.quantity * updatedItem.unitPrice;
          }
          return updatedItem;
        }
        return item;
      })
    }));
  };

  const addLineItem = () => {
    const newItem: LineItem = {
      id: Date.now().toString(),
      description: '',
      quantity: 1,
      unitPrice: 0,
      total: 0,
      unit: 'tim',
      rotEligible: false
    };
    
    setFormData(prev => ({
      ...prev,
      workItems: [...prev.workItems, newItem]
    }));
  };

  const addMaterialItem = () => {
    const newItem: MaterialItem = {
      id: Date.now().toString(),
      articleNumber: '',
      description: '',
      quantity: 1,
      unitPrice: 0,
      total: 0,
      unit: 'st',
    };
    
    setFormData(prev => ({
      ...prev,
      materialItems: [...prev.materialItems, newItem]
    }));
  };

  const addCarpentryLineItem = (template: any) => {
    const newItem: LineItem = {
      id: Date.now().toString(),
      description: template.description,
      quantity: 1,
      unitPrice: template.unitPrice,
      total: template.unitPrice,
      unit: template.unit,
      rotEligible: template.rotEligible
    };
    
    setFormData(prev => ({
      ...prev,
      workItems: [...prev.workItems, newItem]
    }));
  };

  const addMaterialTemplate = (template: any) => {
    const newItem: MaterialItem = {
      id: Date.now().toString(),
      articleNumber: template.articleNumber,
      description: template.description,
      quantity: 1,
      unitPrice: template.unitPrice,
      total: template.unitPrice,
      unit: template.unit
    };
    
    setFormData(prev => ({
      ...prev,
      materialItems: [...prev.materialItems, newItem]
    }));
  };

  const removeLineItem = (id: string) => {
    setFormData(prev => ({
      ...prev,
      workItems: prev.workItems.filter(item => item.id !== id)
    }));
  };

  const removeMaterialItem = (id: string) => {
    setFormData(prev => ({
      ...prev,
      materialItems: prev.materialItems.filter(item => item.id !== id)
    }));
  };

  const handleCustomerChange = (customerId: string) => {
    const customer = customers.find(c => c.id === customerId);
    if (customer) {
      setFormData(prev => ({
        ...prev,
        customerId,
        customerName: customer.company_name || customer.name,
        customerEmail: customer.email,
        customerPhone: customer.phone,
        customerAddress: customer.address
      }));
    }
  };

  const saveEstimate = async () => {
    try {
      if (!formData.customerName || !formData.customerEmail) {
        alert('Vänligen fyll i kund information');
        return;
      }

      if (!formData.projectName) {
        alert('Vänligen fyll i projektnamn');
        return;
      }

      if (formData.workItems.length === 0 && formData.materialItems.length === 0) {
        alert('Vänligen lägg till minst ett arbetsmoment eller material');
        return;
      }

      const lineItems = [
        ...formData.workItems.map(item => ({
          description: item.description,
          quantity: item.quantity,
          unit_price: item.unitPrice,
          total: item.total,
          unit: item.unit,
          rot_eligible: item.rotEligible,
          item_type: 'work' as const
        })),
        ...formData.materialItems.map(item => ({
          description: item.description,
          quantity: item.quantity,
          unit_price: item.unitPrice,
          total: item.total,
          unit: item.unit,
          article_number: item.articleNumber,
          item_type: 'material' as const
        }))
      ];

      const estimateData = {
        customer_id: formData.customerId,
        project_name: formData.projectName,
        customer_name: formData.customerName,
        customer_email: formData.customerEmail,
        customer_phone: formData.customerPhone,
        customer_address: formData.customerAddress,
        description: formData.description,
        valid_until: formData.validUntil,
        payment_terms: formData.paymentTerms,
        total_amount: lineItems.reduce((sum, item) => sum + item.total, 0),
        line_items: lineItems
      };

      const response = await fetch('/api/estimates', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(estimateData),
      });

      if (!response.ok) {
        const errorText = await response.text();
        let errorData;
        try {
          errorData = JSON.parse(errorText);
        } catch (parseError) {
          errorData = { message: errorText };
        }
        throw new Error(`HTTP ${response.status}: ${errorData.message || errorText}`);
      }

      const result = await response.json();
      alert('Offert sparad!');
      // Reset form or navigate
    } catch (error) {
      console.error('Error saving estimate:', error instanceof Error ? error.message : String(error));
    }
  };

  const handleSaveDraft = () => saveEstimate();
  const handleSendEstimate = () => saveEstimate();

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center space-x-4">
            <Link href="/dashboard/estimates">
              <Button variant="outline" size="sm" className="border-gray-200 dark:border-gray-600">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Tillbaka
              </Button>
            </Link>
            <div>
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Skapa offert</h1>
              <p className="text-gray-500 dark:text-gray-400">Skapa en ny offert för dina snickeri- och byggprojekt</p>
            </div>
          </div>
          <div className="flex items-center space-x-3">
            <StatusBadge status={formData.status} />
            <Button 
              variant="outline" 
              onClick={handleSaveDraft}
              disabled={loading}
              className="border-gray-200 dark:border-gray-600"
            >
              <Save className="w-4 h-4 mr-2" />
              Spara utkast
            </Button>
            <Button 
              onClick={handleSendEstimate}
              disabled={loading || !formData.customerId || !formData.projectName}
              className="bg-blue-600 hover:bg-blue-700"
            >
              <Send className="w-4 h-4 mr-2" />
              Skicka offert
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-1 xl:grid-cols-4 gap-6 lg:gap-8">
          {/* Main Form */}
          <div className="xl:col-span-3 space-y-6">
            {/* Basic Information */}
            <FormSection title="Grundläggande information">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <FormLabel htmlFor="estimateNumber">Offertnummer</FormLabel>
                  <MaterialInput
                    id="estimateNumber"
                    name="estimateNumber"
                    value={formData.estimateNumber}
                    onChange={handleInputChange}
                    placeholder="Genereras automatiskt"
                    className="bg-gray-50 dark:bg-gray-700"
                    readOnly
                  />
                </div>
                <div>
                  <FormLabel htmlFor="validUntil">Giltigt till</FormLabel>
                  <MaterialInput
                    id="validUntil"
                    name="validUntil"
                    type="date"
                    value={formData.validUntil}
                    onChange={handleInputChange}
                  />
                </div>
                <div>
                  <FormLabel htmlFor="paymentTerms">Betalningsvillkor</FormLabel>
                  <Select value={formData.paymentTerms} onValueChange={(value) => setFormData(prev => ({ ...prev, paymentTerms: value }))}>
                    <SelectTrigger className="border-gray-200 dark:border-gray-600">
                      <SelectValue placeholder="Välj betalningsvillkor..." />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="30 dagar netto">30 dagar netto</SelectItem>
                      <SelectItem value="15 dagar netto">15 dagar netto</SelectItem>
                      <SelectItem value="Kontant vid leverans">Kontant vid leverans</SelectItem>
                      <SelectItem value="50% förskott, 50% vid slutbesiktning">50% förskott, 50% vid slutbesiktning</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <FormLabel htmlFor="vatRate">Moms (%)</FormLabel>
                  <MaterialInput
                    id="vatRate"
                    name="vatRate"
                    type="number"
                    value={formData.vatRate}
                    onChange={handleInputChange}
                    min="0"
                    max="100"
                  />
                </div>
                <div className="md:col-span-2">
                  <FormLabel htmlFor="projectName">Projektnamn</FormLabel>
                  <MaterialInput
                    id="projectName"
                    name="projectName"
                    value={formData.projectName}
                    onChange={handleInputChange}
                    placeholder="T.ex. Köksinredning Villa Andersson"
                  />
                </div>
                <div className="md:col-span-2">
                  <FormLabel htmlFor="description">Projektbeskrivning</FormLabel>
                  <AutogrowingTextarea
                    id="description"
                    name="description"
                    value={formData.description}
                    onChange={handleInputChange}
                    placeholder="Beskriv snickeri-/byggprojektet i detalj..."
                    defaultRows={3}
                  />
                </div>
              </div>
            </FormSection>

            {/* Customer Information */}
            <FormSection title="Kundinformation">
              <div className="space-y-4">
                <div>
                  <FormLabel htmlFor="customerId">Kund</FormLabel>
                  <SearchableCustomerSelect
                    customers={customers}
                    value={formData.customerId}
                    onValueChange={handleCustomerChange}
                  />
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <FormLabel htmlFor="customerEmail">E-post</FormLabel>
                    <MaterialInput
                      id="customerEmail"
                      name="customerEmail"
                      type="email"
                      value={formData.customerEmail}
                      onChange={handleInputChange}
                      className="bg-gray-50 dark:bg-gray-700"
                      readOnly
                    />
                  </div>
                  <div>
                    <FormLabel htmlFor="customerPhone">Telefon</FormLabel>
                    <MaterialInput
                      id="customerPhone"
                      name="customerPhone"
                      value={formData.customerPhone}
                      onChange={handleInputChange}
                      className="bg-gray-50 dark:bg-gray-700"
                      readOnly
                    />
                  </div>
                </div>
                <div>
                  <FormLabel htmlFor="customerAddress">Adress</FormLabel>
                  <MaterialInput
                    id="customerAddress"
                    name="customerAddress"
                    value={formData.customerAddress}
                    onChange={handleInputChange}
                    className="bg-gray-50 dark:bg-gray-700"
                    readOnly
                  />
                </div>
              </div>
            </FormSection>

            {/* Arbetsmoment & Material */}
            <FormSection title="Arbetsmoment & Material">
              <div className="space-y-8">
                {/* Arbetsmoment Section */}
                <div>
                  <div className="flex items-center mb-4">
                    <Hammer className="w-5 h-5 text-blue-600 mr-2" />
                    <h4 className="text-lg font-medium text-gray-900 dark:text-white">Arbetsmoment</h4>
                  </div>
                  
                  {/* Quick Add Carpentry Items */}
                  <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg mb-4">
                    <h5 className="text-sm font-medium text-blue-900 dark:text-blue-200 mb-3 flex items-center">
                      <Hammer className="w-4 h-4 mr-2" />
                      Snabblägg vanliga snickeri/bygg-poster
                    </h5>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                      {carpentryLineItems.slice(0, 6).map((template, index) => (
                        <Button
                          key={index}
                          variant="outline"
                          size="sm"
                          onClick={() => addCarpentryLineItem(template)}
                          className="justify-start text-xs h-8 border-blue-200 hover:bg-blue-100 dark:border-blue-700 dark:hover:bg-blue-800"
                        >
                          {template.rotEligible && <span className="text-green-600 mr-1">★</span>}
                          {template.description}
                        </Button>
                      ))}
                    </div>
                    <p className="text-xs text-blue-700 dark:text-blue-300 mt-2">★ = ROT-berättigad</p>
                  </div>

                  <div className="overflow-x-auto">
                    <div className="min-w-full">
                      <table className="w-full table-fixed">
                        <thead>
                          <tr className="border-b border-gray-200 dark:border-gray-700">
                            <th className="text-left py-2 pl-3 text-sm font-medium text-gray-700 dark:text-gray-300 w-2/5">Beskrivning</th>
                            <th className="text-left py-2 pl-3 text-sm font-medium text-gray-700 dark:text-gray-300 w-20">Antal</th>
                            <th className="text-left py-2 pl-3 text-sm font-medium text-gray-700 dark:text-gray-300 w-20">Enhet</th>
                            <th className="text-left py-2 pl-3 text-sm font-medium text-gray-700 dark:text-gray-300 w-24">Pris</th>
                            <th className="text-left py-2 pl-3 text-sm font-medium text-gray-700 dark:text-gray-300 w-16">ROT</th>
                            <th className="text-left py-2 pl-3 text-sm font-medium text-gray-700 dark:text-gray-300 w-28">Totalt</th>
                            <th className="w-12"></th>
                          </tr>
                        </thead>
                        <tbody>
                          {formData.workItems.map((item) => (
                            <tr key={item.id} className="border-b border-gray-100 dark:border-gray-800">
                              <td className="py-3 pr-3 align-top">
                                <AutogrowingTextarea
                                  id={`description-${item.id}`}
                                  name="description"
                                  value={item.description}
                                  onChange={(e) => handleLineItemChange(item.id, 'description', e.target.value)}
                                  placeholder="Beskrivning av arbetsmoment"
                                  defaultRows={1}
                                  className="text-sm"
                                />
                              </td>
                              <td className="py-3 px-2 align-top">
                                <MaterialInput
                                  id={`quantity-${item.id}`}
                                  name="quantity"
                                  type="number"
                                  value={item.quantity}
                                  onChange={(e) => handleLineItemChange(item.id, 'quantity', parseFloat(e.target.value) || 0)}
                                  min="0"
                                  step="0.01"
                                  className="text-sm h-[40px]"
                                />
                              </td>
                              <td className="py-3 px-2 align-top">
                                <Select value={item.unit || 'tim'} onValueChange={(value) => handleLineItemChange(item.id, 'unit', value)}>
                                  <SelectTrigger className="border-gray-200 dark:border-gray-600 text-sm h-[40px]">
                                    <SelectValue />
                                  </SelectTrigger>
                                  <SelectContent>
                                    <SelectItem value="tim">tim</SelectItem>
                                    <SelectItem value="dag">dag</SelectItem>
                                    <SelectItem value="st">st</SelectItem>
                                    <SelectItem value="m²">m²</SelectItem>
                                    <SelectItem value="m">m</SelectItem>
                                    <SelectItem value="lm">lm</SelectItem>
                                  </SelectContent>
                                </Select>
                              </td>
                              <td className="py-3 px-2 align-top">
                                <MaterialInput
                                  id={`unitPrice-${item.id}`}
                                  name="unitPrice"
                                  type="number"
                                  value={item.unitPrice}
                                  onChange={(e) => handleLineItemChange(item.id, 'unitPrice', parseFloat(e.target.value) || 0)}
                                  min="0"
                                  step="0.01"
                                  className="text-sm h-[40px]"
                                />
                              </td>
                              <td className="py-3 px-2 align-top">
                                <div className="flex justify-center items-center h-[40px]">
                                  <input
                                    type="checkbox"
                                    checked={item.rotEligible || false}
                                    onChange={(e) => handleLineItemChange(item.id, 'rotEligible', e.target.checked)}
                                    className="w-4 h-4 text-green-600 bg-gray-100 border-gray-300 rounded focus:ring-green-500"
                                  />
                                </div>
                              </td>
                              <td className="py-3 px-2 align-top">
                                <div className="text-sm font-medium text-gray-900 dark:text-white flex items-center h-[40px]">
                                  {new Intl.NumberFormat('sv-SE', { style: 'currency', currency: 'SEK' }).format(item.total)}
                                </div>
                              </td>
                              <td className="py-3 align-top">
                                <div className="flex items-center h-[40px]">
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => removeLineItem(item.id)}
                                    className="text-red-600 hover:text-red-700 border-red-200 hover:border-red-300 h-8 w-8 p-0"
                                  >
                                    <MinusCircle className="w-4 h-4" />
                                  </Button>
                                </div>
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                  <Button
                    variant="outline"
                    onClick={addLineItem}
                    className="border-blue-200 dark:border-blue-600 text-blue-700 dark:text-blue-300 hover:bg-blue-50 dark:hover:bg-blue-900/20"
                  >
                    <Plus className="w-4 h-4 mr-2" />
                    Lägg till arbetsmoment
                  </Button>
                </div>

                {/* Material Section */}
                <div>
                  <div className="flex items-center mb-4">
                    <Calculator className="w-5 h-5 text-blue-600 mr-2" />
                    <h4 className="text-lg font-medium text-gray-900 dark:text-white">Material</h4>
                  </div>

                  {/* Quick Add Material Items */}
                  <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg mb-4">
                    <h5 className="text-sm font-medium text-blue-900 dark:text-blue-200 mb-3 flex items-center">
                      <Calculator className="w-4 h-4 mr-2" />
                      Snabblägg vanliga material
                    </h5>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                      {materialItems.slice(0, 6).map((template, index) => (
                        <Button
                          key={index}
                          variant="outline"
                          size="sm"
                          onClick={() => addMaterialTemplate(template)}
                          className="justify-start text-xs h-8 border-blue-200 hover:bg-blue-100 dark:border-blue-700 dark:hover:bg-blue-800"
                        >
                          <span className="text-blue-600 mr-2 font-mono text-xs">{template.articleNumber}</span>
                          {template.description}
                        </Button>
                      ))}
                    </div>
                    <p className="text-xs text-blue-700 dark:text-blue-300 mt-2">Klicka för att lägga till vanliga material med artikelnummer</p>
                  </div>

                  <div className="overflow-x-auto">
                    <div className="min-w-full">
                      <table className="w-full table-fixed">
                        <thead>
                          <tr className="border-b border-gray-200 dark:border-gray-700">
                            <th className="text-left py-2 pl-3 text-sm font-medium text-gray-700 dark:text-gray-300 w-1/3">Beskrivning</th>
                            <th className="text-left py-2 pl-3 text-sm font-medium text-gray-700 dark:text-gray-300 w-24">Art.nr</th>
                            <th className="text-left py-2 pl-3 text-sm font-medium text-gray-700 dark:text-gray-300 w-20">Antal</th>
                            <th className="text-left py-2 pl-3 text-sm font-medium text-gray-700 dark:text-gray-300 w-16">Enhet</th>
                            <th className="text-left py-2 pl-3 text-sm font-medium text-gray-700 dark:text-gray-300 w-24">Pris</th>
                            <th className="text-left py-2 pl-3 text-sm font-medium text-gray-700 dark:text-gray-300 w-28">Totalt</th>
                            <th className="w-12"></th>
                          </tr>
                        </thead>
                        <tbody>
                          {formData.materialItems.map((item) => (
                            <tr key={item.id} className="border-b border-gray-100 dark:border-gray-800">
                              <td className="py-3 pr-2 align-top w-1/3">
                                <AutogrowingTextarea
                                  id={`material-description-${item.id}`}
                                  name="description"
                                  value={item.description}
                                  onChange={(e) => handleMaterialItemChange(item.id, 'description', e.target.value)}
                                  placeholder="Materialbeskrivning"
                                  defaultRows={1}
                                  className="text-sm"
                                />
                              </td>
                              <td className="py-3 pr-2 align-top w-24">
                                <MaterialInput
                                  id={`articleNumber-${item.id}`}
                                  name="articleNumber"
                                  value={item.articleNumber}
                                  onChange={(e) => handleMaterialItemChange(item.id, 'articleNumber', e.target.value)}
                                  placeholder="Art.nr"
                                  className="text-sm h-[40px]"
                                />
                              </td>
                              <td className="py-3 px-2 align-top w-20">
                                <MaterialInput
                                  id={`material-quantity-${item.id}`}
                                  name="quantity"
                                  type="number"
                                  value={item.quantity}
                                  onChange={(e) => handleMaterialItemChange(item.id, 'quantity', parseFloat(e.target.value) || 0)}
                                  min="0"
                                  step="0.01"
                                  className="text-sm h-[40px]"
                                />
                              </td>
                              <td className="py-3 px-2 align-top w-16">
                                <Select value={item.unit || 'st'} onValueChange={(value) => handleMaterialItemChange(item.id, 'unit', value)}>
                                  <SelectTrigger className="border-gray-200 dark:border-gray-600 text-sm h-[40px]">
                                    <SelectValue />
                                  </SelectTrigger>
                                  <SelectContent>
                                    <SelectItem value="st">st</SelectItem>
                                    <SelectItem value="m²">m²</SelectItem>
                                    <SelectItem value="m">m</SelectItem>
                                    <SelectItem value="kg">kg</SelectItem>
                                    <SelectItem value="liter">liter</SelectItem>
                                    <SelectItem value="lm">lm</SelectItem>
                                    <SelectItem value="förp">förp</SelectItem>
                                  </SelectContent>
                                </Select>
                              </td>
                              <td className="py-3 px-2 align-top w-24">
                                <MaterialInput
                                  id={`material-unitPrice-${item.id}`}
                                  name="unitPrice"
                                  type="number"
                                  value={item.unitPrice}
                                  onChange={(e) => handleMaterialItemChange(item.id, 'unitPrice', parseFloat(e.target.value) || 0)}
                                  min="0"
                                  step="0.01"
                                  className="text-sm h-[40px]"
                                />
                              </td>
                              <td className="py-3 px-2 align-top w-28">
                                <div className="text-sm font-medium text-gray-900 dark:text-white flex items-center h-[40px]">
                                  {new Intl.NumberFormat('sv-SE', { style: 'currency', currency: 'SEK' }).format(item.total)}
                                </div>
                              </td>
                              <td className="py-3 align-top w-12">
                                <div className="flex items-center h-[40px]">
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => removeMaterialItem(item.id)}
                                    className="text-red-600 hover:text-red-700 border-red-200 hover:border-red-300 h-8 w-8 p-0"
                                  >
                                    <MinusCircle className="w-4 h-4" />
                                  </Button>
                                </div>
                              </td>
                            </tr>
                          ))}
                          {formData.materialItems.length === 0 && (
                            <tr>
                              <td colSpan={7} className="py-8 text-center text-gray-500 dark:text-gray-400">
                                Inga material tillagda ännu. Klicka på "Lägg till material" för att börja.
                              </td>
                            </tr>
                          )}
                        </tbody>
                      </table>
                    </div>
                  </div>
                  <Button
                    variant="outline"
                    onClick={addMaterialItem}
                    className="border-blue-200 dark:border-blue-600 text-blue-700 dark:text-blue-300 hover:bg-blue-50 dark:hover:bg-blue-900/20"
                  >
                    <Plus className="w-4 h-4 mr-2" />
                    Lägg till material
                  </Button>
                </div>
              </div>
            </FormSection>

            {/* Additional Text Sections */}
            <FormSection title="Ytterligare information">
              <div className="space-y-4">
                <div>
                  <FormLabel htmlFor="introText" optional>Inledande text</FormLabel>
                  <AutogrowingTextarea
                    id="introText"
                    name="introText"
                    value={formData.introText}
                    onChange={handleInputChange}
                    placeholder="Text som visas i början av offerten..."
                    defaultRows={2}
                  />
                </div>
                <div>
                  <FormLabel htmlFor="closingText" optional>Avslutande text</FormLabel>
                  <AutogrowingTextarea
                    id="closingText"
                    name="closingText"
                    value={formData.closingText}
                    onChange={handleInputChange}
                    placeholder="Text som visas i slutet av offerten..."
                    defaultRows={2}
                  />
                </div>
                <div>
                  <FormLabel htmlFor="notes" optional>Interna anteckningar</FormLabel>
                  <AutogrowingTextarea
                    id="notes"
                    name="notes"
                    value={formData.notes}
                    onChange={handleInputChange}
                    placeholder="Lägg till interna anteckningar om offerten..."
                    defaultRows={3}
                  />
                </div>
              </div>
            </FormSection>
          </div>

          {/* Summary Sidebar */}
          <div className="xl:col-span-1 space-y-6 sticky top-6 self-start">
            <FormSection title="Sammanfattning">
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600 dark:text-gray-400">Arbetsmoment:</span>
                  <span className="font-medium text-gray-900 dark:text-white text-sm">
                    {new Intl.NumberFormat('sv-SE', { style: 'currency', currency: 'SEK' }).format(
                      formData.workItems.reduce((sum, item) => sum + item.total, 0)
                    )}
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600 dark:text-gray-400">Material:</span>
                  <span className="font-medium text-gray-900 dark:text-white text-sm">
                    {new Intl.NumberFormat('sv-SE', { style: 'currency', currency: 'SEK' }).format(
                      formData.materialItems.reduce((sum, item) => sum + item.total, 0)
                    )}
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600 dark:text-gray-400">ROT-berättigat:</span>
                  <span className="font-medium text-green-700 dark:text-green-400 text-sm">
                    {new Intl.NumberFormat('sv-SE', { style: 'currency', currency: 'SEK' }).format(
                      formData.workItems.filter(item => item.rotEligible).reduce((sum, item) => sum + item.total, 0)
                    )}
                  </span>
                </div>
                <div className="border-t border-gray-200 dark:border-gray-700 pt-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600 dark:text-gray-400">Delsumma:</span>
                    <span className="font-medium text-gray-900 dark:text-white text-sm">
                      {new Intl.NumberFormat('sv-SE', { style: 'currency', currency: 'SEK' }).format(formData.subtotal)}
                    </span>
                  </div>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600 dark:text-gray-400">Moms ({formData.vatRate}%):</span>
                  <span className="font-medium text-gray-900 dark:text-white text-sm">
                    {new Intl.NumberFormat('sv-SE', { style: 'currency', currency: 'SEK' }).format(formData.vatAmount)}
                  </span>
                </div>
                <div className="border-t border-gray-200 dark:border-gray-700 pt-4">
                  <div className="flex justify-between items-center">
                    <span className="text-base font-semibold text-gray-900 dark:text-white">Totalt:</span>
                    <span className="text-base font-bold text-blue-600 dark:text-blue-400">
                      {new Intl.NumberFormat('sv-SE', { style: 'currency', currency: 'SEK' }).format(formData.totalAmount)}
                    </span>
                  </div>
                </div>
                {formData.workItems.some(item => item.rotEligible) && (
                  <div className="bg-green-50 dark:bg-green-900/20 p-3 rounded-lg">
                    <p className="text-sm text-green-700 dark:text-green-300">
                      <strong>ROT-avdrag tillämpligt</strong><br />
                      Kunden kan få skattereduktion för arbetskostnader.
                    </p>
                  </div>
                )}
              </div>
            </FormSection>

            {/* Quick Actions */}
            <FormSection title="Snabbåtgärder">
              <div className="space-y-3">
                <Button 
                  variant="outline" 
                  className="w-full justify-start border-gray-200 dark:border-gray-600 text-sm"
                  onClick={() => setShowPDFPreview(true)}
                >
                  <Eye className="w-4 h-4 mr-2" />
                  Förhandsgranska offert
                </Button>
                <Button 
                  variant="outline" 
                  className="w-full justify-start border-gray-200 dark:border-gray-600 text-sm"
                  disabled={!formData.customerEmail}
                >
                  <Send className="w-4 h-4 mr-2" />
                  Skicka via e-post
                </Button>
              </div>
            </FormSection>

            {/* Validation Warnings */}
            {(!formData.customerId || formData.workItems.some(item => !item.description) || formData.materialItems.some(item => !item.description)) && (
              <FormSection>
                <div className="flex items-start space-x-3">
                  <AlertTriangle className="w-5 h-5 text-amber-500 mt-0.5" />
                  <div>
                    <h4 className="text-sm font-medium text-amber-800 dark:text-amber-200">Ofullständig information</h4>
                    <ul className="text-sm text-amber-700 dark:text-amber-300 mt-1 space-y-1">
                      {!formData.customerId && <li>• Välj en kund</li>}
                      {formData.workItems.some(item => !item.description) && <li>• Fyll i beskrivning för alla rader</li>}
                      {formData.materialItems.some(item => !item.description) && <li>• Fyll i beskrivning för alla material</li>}
                    </ul>
                  </div>
                </div>
              </FormSection>
            )}
          </div>
        </div>
      </div>

      {/* PDF Preview Modal */}
      <PDFPreviewModal 
        isOpen={showPDFPreview} 
        onClose={() => setShowPDFPreview(false)} 
        formData={formData} 
      />
    </div>
  );
}

export default function CreateEstimatePage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="w-8 h-8 border-2 border-gray-300 border-t-gray-600 rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-500 dark:text-gray-400">Loading...</p>
        </div>
      </div>
    }>
      <CreateEstimatePageContent />
    </Suspense>
  );
}