"use client"

import { FileText, Filter, PlusCircle, Search, LayoutGrid, Table, Plus } from "lucide-react"
import { useState } from "react"
import { EstimatesList } from "@/components/estimates/estimates-list"
import { EstimatesSummary } from "@/components/estimates/estimates-summary"
import { EstimatesKanban } from "@/components/estimates/estimates-kanban"
import { EstimatesProvider } from "@/lib/hooks/use-estimates"
import Link from "next/link"
import { Button } from "@/components/ui/button"

export default function EstimatesPage() {
  const [view, setView] = useState<'table' | 'kanban'>("table")

  return (
    <EstimatesProvider>
      <div className="p-4 md:p-6 lg:p-8 space-y-6 max-w-full overflow-hidden">
        <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
          <div>
            <h2 className="text-2xl md:text-3xl font-bold tracking-tight text-gray-900 dark:text-white">Offerter</h2>
            <p className="text-gray-500 dark:text-gray-400 mt-1">Hantera och spåra offerter</p>
          </div>
          <div className="flex flex-wrap items-center gap-2">
            <Link href="/dashboard/estimates/create">
              <Button 
                className="bg-blue-600 hover:bg-blue-700 text-white font-medium px-6 py-2.5 rounded-lg shadow-sm transition-all duration-200 ease-in-out hover:shadow-md active:scale-95 flex items-center space-x-2"
              >
                <Plus className="w-4 h-4" />
                <span>Ny offert</span>
              </Button>
            </Link>
          </div>
        </div>

        <div className="space-y-6">
          <EstimatesSummary />
          
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <button
                onClick={() => setView('table')}
                className={`p-2 rounded-md ${view === 'table' ? 'bg-blue-100 text-blue-600' : 'text-gray-500 hover:text-gray-700'}`}
              >
                <Table className="w-4 h-4" />
              </button>
              <button
                onClick={() => setView('kanban')}
                className={`p-2 rounded-md ${view === 'kanban' ? 'bg-blue-100 text-blue-600' : 'text-gray-500 hover:text-gray-700'}`}
              >
                <LayoutGrid className="w-4 h-4" />
              </button>
            </div>
          </div>

          {view === 'table' ? <EstimatesList /> : <EstimatesKanban />}
        </div>
      </div>
    </EstimatesProvider>
  )
} 