'use client'

import React, { useState, Suspense, lazy } from 'react'
import { Settings, User, Building, Globe, Shield, Database } from 'lucide-react'
import { <PERSON><PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Skeleton } from '@/components/ui/loading-skeleton'

// Lazy load settings components
const UserProfileSettings = lazy(() => import('@/components/settings/user-profile-settings').then(module => ({ default: module.UserProfileSettings })))
const CompanySettings = lazy(() => import('@/components/settings/company-settings').then(module => ({ default: module.CompanySettings })))
const SystemSettings = lazy(() => import('@/components/settings/system-settings').then(module => ({ default: module.SystemSettings })))

export default function SettingsPage() {
  const [activeTab, setActiveTab] = useState('profile')

  return (
    <div className="p-4 md:p-6 lg:p-8 space-y-6 max-w-full overflow-hidden">
      <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
        <div>
          <h2 className="text-2xl md:text-3xl font-bold tracking-tight text-gray-900 dark:text-white">Inställningar</h2>
          <p className="text-gray-500 dark:text-gray-400 mt-1">Hantera dina konto- och systeminställningar</p>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-3 lg:w-auto lg:grid-cols-3">
          <TabsTrigger value="profile" className="flex items-center gap-2">
            <User className="h-4 w-4" />
            <span className="hidden sm:inline">Profil</span>
          </TabsTrigger>
          <TabsTrigger value="company" className="flex items-center gap-2">
            <Building className="h-4 w-4" />
            <span className="hidden sm:inline">Företag</span>
          </TabsTrigger>
          <TabsTrigger value="system" className="flex items-center gap-2">
            <Settings className="h-4 w-4" />
            <span className="hidden sm:inline">System</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="profile" className="space-y-6">
          <Suspense fallback={<div className="space-y-4">{Array.from({length: 3}).map((_, i) => <Skeleton key={i} className="h-32 w-full" />)}</div>}>
            <UserProfileSettings />
          </Suspense>
        </TabsContent>

        <TabsContent value="company" className="space-y-6">
          <Suspense fallback={<div className="space-y-4">{Array.from({length: 3}).map((_, i) => <Skeleton key={i} className="h-32 w-full" />)}</div>}>
            <CompanySettings />
          </Suspense>
        </TabsContent>

        <TabsContent value="system" className="space-y-6">
          <Suspense fallback={<div className="space-y-4">{Array.from({length: 3}).map((_, i) => <Skeleton key={i} className="h-32 w-full" />)}</div>}>
            <SystemSettings />
          </Suspense>
        </TabsContent>
      </Tabs>
    </div>
  )
}