'use client';

import React, { useEffect, useState } from 'react';
import { supabase } from '@/lib/supabase/supabase';
import Link from 'next/link';
import { Building, Edit, Search, Plus, Trash2, AlertTriangle, FileText, Briefcase, DollarSign, Users, Mail, 
Phone, MapPin } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { toast } from 'sonner';
import { ExpandableList } from '@/components/ui/expandable-list';
import { StatsCards } from '@/components/ui/stats-cards';

interface Customer {
  id: string;
  name: string;
  company_name?: string;
  is_company?: boolean;
  email?: string;
  phone?: string;
  address?: string;
  city?: string;
  postal_code?: string;
  country?: string;
  vat_number?: string;
  notes?: string;
  status?: string;
  website?: string;
  industry?: string;
  annual_revenue?: number;
  source?: string;
  assigned_to?: string;
  created_at: string;
  updated_at: string;
}

interface RelatedRecords {
  contacts: number;
  projects: number;
  estimates: number;
  invoices: number;
}

export default function CustomersPage() {
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [customerToDelete, setCustomerToDelete] = useState<Customer | null>(null);
  const [relatedRecords, setRelatedRecords] = useState<RelatedRecords | null>(null);
  const [isCheckingRelated, setIsCheckingRelated] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [customerStats, setCustomerStats] = useState({
    totalCustomers: 0,
    companies: 0,
    individuals: 0,
    activeProjects: 0,
    totalRevenue: 0
  });

  useEffect(() => {
    async function fetchCustomers() {
      try {
        setLoading(true);
        const { data, error } = await supabase
          .from('customers')
          .select('*')
          .order('name');

        if (error) {
          throw error;
        }

        const customersData = data || [];
        setCustomers(customersData);

        // Calculate statistics
        const totalCustomers = customersData.length;
        const companies = customersData.filter(c => c.is_company).length;
        const individuals = customersData.filter(c => !c.is_company).length;

        // Fetch additional stats from related tables
        const [projectsResult, invoicesResult] = await Promise.all([
          supabase.from('projects').select('customer_id, status').eq('status', 'active'),
          supabase.from('invoices').select('customer_id, total_amount').eq('status', 'paid')
        ]);

        const activeProjects = projectsResult.data?.length || 0;
        const totalRevenue = invoicesResult.data?.reduce((sum, inv) => sum + parseFloat(inv.total_amount || '0'), 0) || 0;

        setCustomerStats({
          totalCustomers,
          companies,
          individuals,
          activeProjects,
          totalRevenue
        });
      } catch (error: any) {
        console.error('Error fetching customers:', error.message);
        toast.error('Kunde inte hämta kunder');
      } finally {
        setLoading(false);
      }
    }
    
    fetchCustomers();
  }, []);

  const filteredCustomers = customers.filter(customer => 
    customer.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    (customer.company_name && customer.company_name.toLowerCase().includes(searchQuery.toLowerCase())) ||
    (customer.email && customer.email.toLowerCase().includes(searchQuery.toLowerCase()))
  );

  const confirmDelete = async (id: string) => {
    const customer = customers.find(c => c.id === id);
    if (!customer) return;
    
    setCustomerToDelete(customer);
    setIsCheckingRelated(true);
    setIsDeleteModalOpen(true);
    
    try {
      // Check for related records
      const [contactsResult, projectsResult, estimatesResult, invoicesResult] = await Promise.all([
        supabase.from('customer_contacts').select('id', { count: 'exact' }).eq('customer_id', id),
        supabase.from('projects').select('id', { count: 'exact' }).eq('customer_id', id),
        supabase.from('estimates').select('id', { count: 'exact' }).eq('customer_id', id),
        supabase.from('invoices').select('id', { count: 'exact' }).eq('customer_id', id)
      ]);
      
      setRelatedRecords({
        contacts: contactsResult.count || 0,
        projects: projectsResult.count || 0,
        estimates: estimatesResult.count || 0,
        invoices: invoicesResult.count || 0
      });
    } catch (error: any) {
      console.error('Error checking related records:', error.message);
      toast.error('Error checking related records');
      setIsDeleteModalOpen(false);
    } finally {
      setIsCheckingRelated(false);
    }
  };

  const handleDelete = async () => {
    if (!customerToDelete) return;
    
    try {
      setIsDeleting(true);
      
      // If there are related records, we need to delete them first (cascade delete)
      if (relatedRecords) {
        const deletePromises = [];
        
        // Delete in the correct order to avoid foreign key violations
        if (relatedRecords.contacts > 0) {
          deletePromises.push(
            supabase.from('customer_contacts').delete().eq('customer_id', customerToDelete.id)
          );
        }
        
        if (relatedRecords.estimates > 0) {
          deletePromises.push(
            supabase.from('estimates').delete().eq('customer_id', customerToDelete.id)
          );
        }
        
        if (relatedRecords.invoices > 0) {
          deletePromises.push(
            supabase.from('invoices').delete().eq('customer_id', customerToDelete.id)
          );
        }
        
        if (relatedRecords.projects > 0) {
          deletePromises.push(
            supabase.from('projects').delete().eq('customer_id', customerToDelete.id)
          );
        }
        
        // Execute all deletions
        const results = await Promise.all(deletePromises);
        
        // Check for errors in any of the deletions
        for (const result of results) {
          if (result.error) throw result.error;
        }
      }
      
      // Finally delete the customer
      const { error } = await supabase
        .from('customers')
        .delete()
        .eq('id', customerToDelete.id);
      
      if (error) throw error;
      
      // Remove the deleted customer from state
      setCustomers(customers.filter(c => c.id !== customerToDelete.id));
      setIsDeleteModalOpen(false);
      setCustomerToDelete(null);
      setRelatedRecords(null);
      
      // Show success notification
      toast.success('Customer deleted successfully', {
        description: 'Customer and all related records have been removed.'
      });
      
    } catch (error: any) {
      console.error('Error deleting customer:', error.message);
      toast.error('Error deleting customer', {
        description: error.message
      });
    } finally {
      setIsDeleting(false);
    }
  };

  const customerMetrics = [
    {
      label: "Totalt kunder",
      value: customerStats.totalCustomers,
      icon: <Users className="w-5 h-5 text-blue-600 dark:text-blue-400" />,
      change: "+8%",
      changeType: "positive" as const,
      changeLabel: "senaste månaden"
    },
    {
      label: "Företag",
      value: customerStats.companies,
      icon: <Building className="w-5 h-5 text-indigo-600 dark:text-indigo-400" />,
      change: "+12%",
      changeType: "positive" as const,
      changeLabel: "senaste månaden"
    },
    {
      label: "Privatpersoner",
      value: customerStats.individuals,
      icon: <Users className="w-5 h-5 text-green-600 dark:text-green-400" />,
      change: "+5%",
      changeType: "positive" as const,
      changeLabel: "senaste månaden"
    },
    {
      label: "Aktiva projekt",
      value: customerStats.activeProjects,
      icon: <Briefcase className="w-5 h-5 text-amber-600 dark:text-amber-400" />,
      change: "+15%",
      changeType: "positive" as const,
      changeLabel: "senaste månaden"
    },
    {
      label: "Total omsättning",
      value: new Intl.NumberFormat('sv-SE', { style: 'currency', currency: 'SEK' }).format(customerStats.totalRevenue),
      icon: <DollarSign className="w-5 h-5 text-emerald-600 dark:text-emerald-400" />,
      change: "+18%",
      changeType: "positive" as const,
      changeLabel: "senaste månaden"
    }
  ];

  const customerColumns = [
    { key: 'name', label: 'Namn' },
    { key: 'type', label: 'Typ' },
    { key: 'email', label: 'E-post' },
    { key: 'phone', label: 'Telefon' },
    { key: 'actions', label: 'Åtgärder', className: 'text-right' }
  ];

  const renderCustomerCell = (customer: Customer, columnKey: string) => {
    switch (columnKey) {
      case 'name':
        return (
          <div>
            <Link href={`/dashboard/customers/${customer.id}`} className="text-blue-600 dark:text-blue-400 hover:underline font-medium">
              {customer.name}
            </Link>
            {customer.company_name && (
              <p className="text-sm text-gray-500 dark:text-gray-400">{customer.company_name}</p>
            )}
          </div>
        );
      case 'type':
        return (
          <span className={`px-3 py-1 rounded-full text-xs font-medium ${
            customer.is_company 
              ? 'bg-blue-100 text-blue-700 dark:bg-blue-900/20 dark:text-blue-400' 
              : 'bg-green-100 text-green-700 dark:bg-green-900/20 dark:text-green-400'
          }`}>
            {customer.is_company ? 'Företag' : 'Privatperson'}
          </span>
        );
      case 'email':
        return (
          <span className="text-gray-700 dark:text-gray-300">
            {customer.email || '-'}
          </span>
        );
      case 'phone':
        return (
          <span className="text-gray-700 dark:text-gray-300">
            {customer.phone || '-'}
          </span>
        );
      case 'actions':
        return (
          <div className="flex justify-end space-x-2" onClick={(e) => e.stopPropagation()}>
            <Link href={`/dashboard/customers/${customer.id}/edit`} className="text-indigo-600 dark:text-indigo-400 hover:text-indigo-900 dark:hover:text-indigo-200">
              <Edit className="h-5 w-5" />
            </Link>
            <button
              onClick={() => confirmDelete(customer.id)}
              className="text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-200"
            >
              <Trash2 className="h-5 w-5" />
            </button>
          </div>
        );
      default:
        return null;
    }
  };

  const renderCustomerExpandedContent = (customer: Customer) => (
    <div className="flex flex-col md:flex-row gap-8">
      <div className="flex-1 space-y-3">
        <div className="text-lg font-medium text-gray-900 dark:text-white mb-4">Kunddetaljer</div>
        <div><span className="font-medium text-gray-700 dark:text-gray-300">Namn:</span> <span className="text-gray-900 dark:text-white">{customer.name}</span></div>
        {customer.company_name && (
          <div><span className="font-medium text-gray-700 dark:text-gray-300">Företag:</span> <span className="text-gray-900 dark:text-white">{customer.company_name}</span></div>
        )}
        <div><span className="font-medium text-gray-700 dark:text-gray-300">Typ:</span> <span className="text-gray-900 dark:text-white">{customer.is_company ? 'Företag' : 'Privatperson'}</span></div>
        {customer.email && (
          <div className="flex items-center"><Mail className="w-4 h-4 mr-2 text-gray-500" /><span className="font-medium text-gray-700 dark:text-gray-300">E-post:</span> <span className="text-gray-900 dark:text-white ml-2">{customer.email}</span></div>
        )}
        {customer.phone && (
          <div className="flex items-center"><Phone className="w-4 h-4 mr-2 text-gray-500" /><span className="font-medium text-gray-700 dark:text-gray-300">Telefon:</span> <span className="text-gray-900 dark:text-white ml-2">{customer.phone}</span></div>
        )}
        {(customer.address || customer.city) && (
          <div className="flex items-start"><MapPin className="w-4 h-4 mr-2 text-gray-500 mt-0.5" /><span className="font-medium text-gray-700 dark:text-gray-300">Adress:</span>
            <span className="text-gray-900 dark:text-white ml-2">
              {customer.address && `${customer.address}, `}
              {customer.city && `${customer.city}`}
              {customer.postal_code && `, ${customer.postal_code}`}
            </span>
          </div>
        )}
        {customer.vat_number && (
          <div><span className="font-medium text-gray-700 dark:text-gray-300">Org.nr:</span> <span className="text-gray-900 dark:text-white">{customer.vat_number}</span></div>
        )}
        {customer.notes && (
          <div><span className="font-medium text-gray-700 dark:text-gray-300">Anteckningar:</span> <span className="text-gray-900 dark:text-white">{customer.notes}</span></div>
        )}
      </div>
      <div className="flex-1">
        <div className="text-lg font-medium text-gray-900 dark:text-white mb-4">Snabbåtgärder</div>
        <div className="space-y-3">
          <Link href={`/dashboard/customers/${customer.id}`} className="block">
            <Button variant="outline" className="w-full justify-start">
              <FileText className="w-4 h-4 mr-2" />
              Visa detaljer
            </Button>
          </Link>
          <Link href={`/dashboard/customers/${customer.id}/edit`} className="block">
            <Button variant="outline" className="w-full justify-start">
              <Edit className="w-4 h-4 mr-2" />
              Redigera kund
            </Button>
          </Link>
          <Link href={`/dashboard/customers/${customer.id}/contacts`} className="block">
            <Button variant="outline" className="w-full justify-start">
              <Users className="w-4 h-4 mr-2" />
              Hantera kontakter
            </Button>
          </Link>
        </div>
      </div>
    </div>
  );

  const renderCustomersContent = () => (
    <>
      <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
        <div>
          <h2 className="text-2xl md:text-3xl font-bold tracking-tight text-gray-900 dark:text-white">Kunder</h2>
          <p className="text-gray-500 dark:text-gray-400 mt-1">Hantera och spåra kundrelationer</p>
        </div>
        <div className="flex flex-wrap items-center gap-2">
          <Link href="/dashboard/customers/new">
            <Button className="bg-[#5D5FEF] hover:bg-[#4B4AEF]">
              <Plus className="h-4 w-4 mr-2" />
              Lägg till kund
            </Button>
          </Link>
        </div>
      </div>

      {/* Statistics Cards */}
      <StatsCards metrics={customerMetrics} />
      
      <div className="flex flex-col gap-4 md:flex-row">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400 dark:text-gray-500" />
          <Input type="search" placeholder="Sök kunder..." className="w-full pl-10 border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-800" value={searchQuery} onChange={(e) => setSearchQuery(e.target.value)} />
        </div>
      </div>
      
      {loading ? (
        <div className="flex justify-center p-8">
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      ) : filteredCustomers.length > 0 ? (
        <ExpandableList
          data={filteredCustomers}
          columns={customerColumns}
          renderCell={renderCustomerCell}
          renderExpandedContent={renderCustomerExpandedContent}
          getRowKey={(customer) => customer.id}
        />
      ) : (
        <div className="text-center py-12 bg-white dark:bg-gray-800 rounded-lg shadow">
          {searchQuery ? (
            <div>
              <Building className="h-12 w-12 mx-auto text-gray-400 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">Inga matchande kunder</h3>
              <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
                Försök justera din sökning för att hitta det du letar efter.
              </p>
              <Button className="mt-4" onClick={() => setSearchQuery('')}>
                Rensa sökning
              </Button>
            </div>
          ) : (
            <div>
              <Building className="h-12 w-12 mx-auto text-gray-400 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">Inga kunder ännu</h3>
              <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
                Kom igång genom att skapa din första kund.
              </p>
              <Link href="/dashboard/customers/new">
                <Button className="mt-4">
                  <Plus className="h-4 w-4 mr-2" />
                  Lägg till kund
                </Button>
              </Link>
            </div>
          )}
        </div>
      )}
      
      {/* Delete Confirmation Modal */}
      {isDeleteModalOpen && customerToDelete && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
          <div className="bg-white dark:bg-gray-800 rounded-lg max-w-lg w-full p-6 shadow-xl">
            <div className="flex items-center mb-4">
              <div className="flex-shrink-0">
                <AlertTriangle className="h-6 w-6 text-red-500" />
              </div>
              <div className="ml-3">
                <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
                  Delete Customer
                </h3>
              </div>
            </div>
            
            <div className="mb-4">
              <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                You're about to delete <strong>{customerToDelete.name}</strong>
                {customerToDelete.company_name && ` (${customerToDelete.company_name})`}.
              </p>
              
              {isCheckingRelated ? (
                <div className="flex items-center justify-center py-4">
                  <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-blue-500"></div>
                  <span className="ml-2 text-sm text-gray-600 dark:text-gray-400">
                    Checking related records...
                  </span>
                </div>
              ) : relatedRecords ? (
                <div className="space-y-3">
                  {(relatedRecords.contacts > 0 || relatedRecords.projects > 0 || 
                    relatedRecords.estimates > 0 || relatedRecords.invoices > 0) ? (
                    <>
                      <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-3">
                        <p className="text-sm font-medium text-red-800 dark:text-red-200 mb-2">
                          ⚠️ This will also delete the following related records:
                        </p>
                        <div className="space-y-1">
                          {relatedRecords.contacts > 0 && (
                            <div className="flex items-center text-sm text-red-700 dark:text-red-300">
                              <Users className="h-4 w-4 mr-2" />
                              {relatedRecords.contacts} contact{relatedRecords.contacts !== 1 ? 's' : ''}
                            </div>
                          )}
                          {relatedRecords.projects > 0 && (
                            <div className="flex items-center text-sm text-red-700 dark:text-red-300">
                              <Briefcase className="h-4 w-4 mr-2" />
                              {relatedRecords.projects} project{relatedRecords.projects !== 1 ? 's' : ''}
                            </div>
                          )}
                          {relatedRecords.estimates > 0 && (
                            <div className="flex items-center text-sm text-red-700 dark:text-red-300">
                              <FileText className="h-4 w-4 mr-2" />
                              {relatedRecords.estimates} estimate{relatedRecords.estimates !== 1 ? 's' : ''}
                            </div>
                          )}
                          {relatedRecords.invoices > 0 && (
                            <div className="flex items-center text-sm text-red-700 dark:text-red-300">
                              <DollarSign className="h-4 w-4 mr-2" />
                              {relatedRecords.invoices} invoice{relatedRecords.invoices !== 1 ? 's' : ''}
                            </div>
                          )}
                        </div>
                      </div>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        <strong>This action cannot be undone.</strong> All data associated with this customer will be permanently removed.
                      </p>
                    </>
                  ) : (
                    <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-3">
                      <p className="text-sm text-green-800 dark:text-green-200">
                        ✅ No related records found. This customer can be safely deleted.
                      </p>
                    </div>
                  )}
                </div>
              ) : null}
            </div>
            
            <div className="flex justify-end space-x-3">
              <Button 
                variant="outline" 
                onClick={() => {
                  setIsDeleteModalOpen(false);
                  setCustomerToDelete(null);
                  setRelatedRecords(null);
                }}
                disabled={isDeleting}
              >
                Cancel
              </Button>
              <Button 
                variant="destructive" 
                onClick={handleDelete}
                disabled={isCheckingRelated || isDeleting}
                className="min-w-[100px]"
              >
                {isDeleting ? (
                  <div className="flex items-center">
                    <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2"></div>
                    Deleting...
                  </div>
                ) : (
                  'Delete'
                )}
              </Button>
            </div>
          </div>
        </div>
      )}
    </>
  );

  return (
    <div className="p-4 md:p-6 lg:p-8 space-y-6 max-w-full overflow-hidden">
      {renderCustomersContent()}
    </div>
  );
} 