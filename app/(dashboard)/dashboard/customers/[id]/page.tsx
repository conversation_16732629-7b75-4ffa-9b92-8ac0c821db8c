'use client';

import React, { useEffect, useState } from 'react';
import { supabase } from '@/lib/supabase/supabase';
import { Customer, CustomerContact } from '@/lib/supabase/supabase';
import Link from 'next/link';
import { 
  ArrowLeft, 
  Edit, 
  Trash2, 
  Building, 
  Mail, 
  Phone, 
  MapPin, 
  FileText, 
  CreditCard,
  User,
  Plus,
  AlertTriangle,
  Copy,
  MoreHorizontal,
  Calendar,
  Clock,
  DollarSign,
  CheckCircle,
  XCircle,
  Hammer
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useParams, useRouter } from 'next/navigation';
import { toast } from 'sonner';
import { formatDate, formatCurrency } from '@/lib/utils';

interface Estimate {
  id: string;
  estimate_number: string;
  project_name: string;
  date: string;
  total_amount: number;
  status: 'draft' | 'pending' | 'accepted' | 'rejected';
  valid_until?: string;
}

interface SiteVisit {
  id: string;
  date: string;
  purpose: string;
  status: 'scheduled' | 'completed' | 'cancelled';
  notes?: string;
}

interface Invoice {
  id: string;
  invoice_number: string;
  date: string;
  amount: number;
  status: 'draft' | 'sent' | 'paid' | 'overdue';
  due_date?: string;
}

interface Project {
  id: string;
  name: string;
  status: 'planning' | 'in-progress' | 'completed' | 'on-hold';
  start_date: string;
  end_date?: string;
  budget: number;
  description?: string;
}

export default function CustomerDetailPage() {
  const params = useParams();
  const router = useRouter();
  const customerId = params.id as string;
  
  const [customer, setCustomer] = useState<Customer | null>(null);
  const [loading, setLoading] = useState(true);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [activeTab, setActiveTab] = useState<'visits' | 'offerts' | 'invoices' | 'projects'>('visits');
  
  // Tab data states
  const [estimates, setEstimates] = useState<Estimate[]>([]);
  const [siteVisits, setSiteVisits] = useState<SiteVisit[]>([]);
  const [invoices, setInvoices] = useState<Invoice[]>([]);
  const [projects, setProjects] = useState<Project[]>([]);
  const [tabLoading, setTabLoading] = useState(false);

  useEffect(() => {
    if (!customerId) return;
    
    async function fetchCustomerData() {
      try {
        setLoading(true);
        
        // Fetch customer details
        const { data: customerData, error: customerError } = await supabase
          .from('customers')
          .select('*')
          .eq('id', customerId)
          .single();
        
        if (customerError) throw customerError;
        setCustomer(customerData);
      } catch (error: any) {
        console.error('Error fetching customer:', error.message);
        toast.error('Error loading customer details');
      } finally {
        setLoading(false);
      }
    }
    
    fetchCustomerData();
  }, [customerId]);

  useEffect(() => {
    if (!customer) return;
    fetchTabData();
  }, [customer, activeTab]);

  const fetchTabData = async () => {
    if (!customer) return;
    
    setTabLoading(true);
    try {
      if (activeTab === 'offerts') {
        const { data: estimatesData, error } = await supabase
          .from('estimates')
          .select('id, estimate_number, project_name, date, total_amount, status, valid_until')
          .eq('customer_id', customer.id)
          .order('created_at', { ascending: false });
        
        if (error) throw error;
        setEstimates(estimatesData || []);
      } else if (activeTab === 'visits') {
        // Mock data for site visits - you'll need to implement this table
        setSiteVisits([
          {
            id: '1',
            date: '2024-04-15',
            purpose: 'Initial site inspection',
            status: 'completed',
            notes: 'Measured living room and kitchen for renovation'
          },
          {
            id: '2', 
            date: '2024-04-22',
            purpose: 'Follow-up measurement',
            status: 'scheduled'
          }
        ]);
      } else if (activeTab === 'invoices') {
        // Mock data for invoices - you'll need to implement this properly
        setInvoices([
          {
            id: '1',
            invoice_number: 'INV-2024-001',
            date: '2024-04-01',
            amount: 25000,
            status: 'paid',
            due_date: '2024-04-30'
          },
          {
            id: '2',
            invoice_number: 'INV-2024-002',
            date: '2024-04-15',
            amount: 15000,
            status: 'sent',
            due_date: '2024-05-15'
          },
          {
            id: '3',
            invoice_number: 'INV-2024-003',
            date: '2024-03-15',
            amount: 8500,
            status: 'overdue',
            due_date: '2024-04-15'
          },
          {
            id: '4',
            invoice_number: 'INV-2024-004',
            date: '2024-03-01',
            amount: 32000,
            status: 'paid',
            due_date: '2024-03-31'
          },
          {
            id: '5',
            invoice_number: 'INV-2024-005',
            date: '2024-04-20',
            amount: 12000,
            status: 'sent',
            due_date: '2024-05-20'
          }
        ]);
      } else if (activeTab === 'projects') {
        // Mock data for projects - you'll need to implement this properly
        setProjects([
          {
            id: '1',
            name: 'Kitchen Renovation',
            status: 'in-progress',
            start_date: '2024-04-01',
            end_date: '2024-05-15',
            budget: 100000,
            description: 'Complete kitchen renovation with new cabinets and appliances'
          },
          {
            id: '2',
            name: 'Bathroom Remodel',
            status: 'planning',
            start_date: '2024-05-15',
            budget: 75000,
            description: 'Master bathroom renovation'
          },
          {
            id: '3',
            name: 'Deck Construction',
            status: 'completed',
            start_date: '2024-02-01',
            end_date: '2024-03-15',
            budget: 50000,
            description: 'New outdoor deck with pergola'
          }
        ]);
      }
    } catch (error: any) {
      console.error('Error fetching tab data:', error);
      toast.error('Error loading data');
    } finally {
      setTabLoading(false);
    }
  };

  const confirmDelete = () => {
    setIsDeleteModalOpen(true);
  };

  const handleDelete = async () => {
    setIsDeleting(true);
    
    try {
      const { error } = await supabase
        .from('customers')
        .delete()
        .eq('id', customerId);
      
      if (error) throw error;
      
      toast.success('Customer deleted successfully');
      router.push('/dashboard/customers');
    } catch (error: any) {
      console.error('Error deleting customer:', error.message);
      toast.error('Error deleting customer', {
        description: error.message
      });
    } finally {
      setIsDeleting(false);
      setIsDeleteModalOpen(false);
    }
  };

  const copyCustomerId = () => {
    navigator.clipboard.writeText(customer?.id || '');
    toast.success('Customer ID copied to clipboard');
  };

  const getStatusBadge = (status: string, type: 'estimate' | 'visit' | 'invoice' | 'project') => {
    const baseClasses = "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium";
    
    if (type === 'estimate') {
      switch (status) {
        case 'accepted':
          return `${baseClasses} bg-green-100 text-green-800`;
        case 'pending':
          return `${baseClasses} bg-yellow-100 text-yellow-800`;
        case 'rejected':
          return `${baseClasses} bg-red-100 text-red-800`;
        default:
          return `${baseClasses} bg-gray-100 text-gray-800`;
      }
    } else if (type === 'visit') {
      switch (status) {
        case 'completed':
          return `${baseClasses} bg-green-100 text-green-800`;
        case 'scheduled':
          return `${baseClasses} bg-blue-100 text-blue-800`;
        case 'cancelled':
          return `${baseClasses} bg-red-100 text-red-800`;
        default:
          return `${baseClasses} bg-gray-100 text-gray-800`;
      }
    } else if (type === 'project') {
      switch (status) {
        case 'completed':
          return `${baseClasses} bg-green-100 text-green-800`;
        case 'in-progress':
          return `${baseClasses} bg-blue-100 text-blue-800`;
        case 'planning':
          return `${baseClasses} bg-yellow-100 text-yellow-800`;
        case 'on-hold':
          return `${baseClasses} bg-gray-100 text-gray-800`;
        default:
          return `${baseClasses} bg-gray-100 text-gray-800`;
      }
    } else {
      switch (status) {
        case 'paid':
          return `${baseClasses} bg-green-100 text-green-800`;
        case 'sent':
          return `${baseClasses} bg-blue-100 text-blue-800`;
        case 'overdue':
          return `${baseClasses} bg-red-100 text-red-800`;
        default:
          return `${baseClasses} bg-gray-100 text-gray-800`;
      }
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto px-6 py-8">
        <div className="flex justify-center p-8">
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      </div>
    );
  }
  
  if (!customer) {
    return (
      <div className="container mx-auto px-6 py-8">
        <div className="bg-white shadow-sm rounded-lg p-6 text-center">
          <h3 className="text-lg font-medium text-gray-900">Customer not found</h3>
          <p className="mt-2 text-sm text-gray-500">
            The customer you're looking for doesn't exist or you don't have permission to view it.
          </p>
          <Link href="/dashboard/customers">
            <Button className="mt-4">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to customers
            </Button>
          </Link>
        </div>
      </div>
    );
  }

  const customerSince = customer.created_at ? new Date(customer.created_at).toLocaleDateString('sv-SE', { 
    year: 'numeric', 
    month: 'long',
    day: 'numeric'
  }) : 'Unknown';

  return (
    <div className="bg-gray-50 min-h-screen">
      <div className="container mx-auto px-6 py-8 max-w-7xl">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
        <div className="flex items-center">
          <Link href="/dashboard/customers" className="mr-4">
              <Button variant="ghost" size="icon" className="hover:bg-white">
              <ArrowLeft className="h-5 w-5" />
            </Button>
          </Link>
          <div>
              <h1 className="text-2xl font-semibold text-gray-900">{customer.name}</h1>
            {customer.company_name && (
                <p className="text-gray-500">{customer.company_name}</p>
            )}
          </div>
        </div>
          <div className="flex items-center space-x-3">
            <Button variant="outline" className="bg-white">
              <Edit className="h-4 w-4 mr-2" />
              Edit information
            </Button>
            <Button variant="outline" className="bg-white">
              <Plus className="h-4 w-4 mr-2" />
              Send invoice
            </Button>
            <Button variant="ghost" size="icon" onClick={confirmDelete}>
              <MoreHorizontal className="h-5 w-5" />
          </Button>
        </div>
      </div>
      
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Left Sidebar - Customer Details */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
              <div className="p-6">
                <h2 className="text-lg font-medium text-gray-900 mb-6">Customer details</h2>
                
                {/* Lifetime Value Section */}
                <div className="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl p-4 mb-6 border border-blue-100">
                  <div className="flex items-center justify-between">
                <div>
                      <div className="text-xs font-medium text-blue-600 uppercase tracking-wide mb-1">Total Income</div>
                      <div className="text-2xl font-semibold text-gray-900">
                        {formatCurrency(
                          estimates
                            .filter(est => est.status === 'accepted')
                            .reduce((sum, est) => sum + (est.total_amount || 0), 0) +
                          invoices
                            .filter(inv => inv.status === 'paid')
                            .reduce((sum, inv) => sum + (inv.amount || 0), 0)
                        )}
                      </div>
                      <div className="text-xs text-blue-600 font-medium mt-1">
                        {estimates.filter(est => est.status === 'accepted').length + invoices.filter(inv => inv.status === 'paid').length} completed transactions
                      </div>
                    </div>
                    <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                      <DollarSign className="w-6 h-6 text-blue-600" />
                    </div>
                  </div>
                </div>
                
                <div className="space-y-6">
                  {/* Customer since */}
                  <div className="flex items-center">
                    <Clock className="h-4 w-4 text-gray-400 mr-3" />
                    <div>
                      <p className="text-sm text-gray-500">Customer since</p>
                      <p className="text-sm font-medium text-gray-900">{customerSince}</p>
                    </div>
                  </div>

                  {/* Customer ID */}
                  <div className="flex items-center">
                    <div className="w-4 h-4 mr-3 flex items-center justify-center">
                      <div className="w-2 h-2 bg-gray-400 rounded-full"></div>
                    </div>
                    <div className="flex-1">
                      <p className="text-sm text-gray-500">Customer ID</p>
                      <div className="flex items-center">
                        <p className="text-sm font-medium text-gray-900 font-mono">{customer.id.substring(0, 13)}...</p>
                        <Button 
                          variant="ghost" 
                          size="sm" 
                          className="p-1 h-6 w-6 ml-2"
                          onClick={copyCustomerId}
                        >
                          <Copy className="h-3 w-3" />
                        </Button>
                      </div>
                </div>
              </div>
              
                  {/* Customer type */}
                  <div className="flex items-center">
                    <User className="h-4 w-4 text-gray-400 mr-3" />
                  <div>
                      <p className="text-sm text-gray-500">Customer type</p>
                      <p className="text-sm font-medium text-gray-900">
                        {customer.is_company ? 'Business' : 'Individual'}
                      </p>
                    </div>
                  </div>

                  {/* Email */}
                  {customer.email && (
                    <div className="flex items-center">
                      <Mail className="h-4 w-4 text-gray-400 mr-3" />
                      <div>
                        <p className="text-sm text-gray-500">Email address</p>
                        <a 
                          href={`mailto:${customer.email}`} 
                          className="text-sm font-medium text-blue-600 hover:text-blue-800"
                        >
                          {customer.email}
                        </a>
                  </div>
                </div>
              )}
              
                  {/* Mobile number */}
              {customer.phone && (
                    <div className="flex items-center">
                      <Phone className="h-4 w-4 text-gray-400 mr-3" />
                  <div>
                        <p className="text-sm text-gray-500">Mobile number</p>
                        <a 
                          href={`tel:${customer.phone}`} 
                          className="text-sm font-medium text-gray-900"
                        >
                          {customer.phone} 🇸🇪
                        </a>
                      </div>
                  </div>
                  )}
                </div>
              </div>

              {/* Address section */}
              {(customer.address || customer.city || customer.postal_code) && (
                <>
                  <div className="border-t border-gray-200 px-6 py-4">
                    <h3 className="text-sm font-medium text-gray-900 mb-4">Address</h3>
                    
                    <div className="space-y-4">
                      <div>
                        <p className="text-xs font-medium text-gray-500 uppercase tracking-wide">BILLING ADDRESS</p>
                        <div className="mt-1 text-sm text-gray-900">
                          {customer.address && <div>{customer.address}</div>}
                          <div>
                            {customer.city && customer.postal_code && 
                              `${customer.city}, ${customer.postal_code}`
                            }
                          </div>
                          {customer.country && <div>{customer.country}</div>}
                          {customer.phone && (
                            <div className="mt-1 text-gray-600">
                              {customer.phone}
                </div>
              )}
                        </div>
                      </div>

                      <div>
                        <p className="text-xs font-medium text-gray-500 uppercase tracking-wide">SHIPPING DETAILS</p>
                        <div className="mt-1 text-sm text-gray-900">
                          {customer.address && <div>{customer.address}</div>}
                  <div>
                            {customer.city && customer.postal_code && 
                              `${customer.city}, ${customer.postal_code}`
                            }
                          </div>
                          {customer.country && <div>{customer.country}</div>}
                          {customer.phone && (
                            <div className="mt-1 text-gray-600">
                              {customer.phone}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                </>
              )}
            </div>
          </div>

          {/* Right Content - Tabbed Interface */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
              {/* Tab Headers */}
              <div className="border-b border-gray-200">
                <nav className="flex space-x-8 px-6" aria-label="Tabs">
                  <button
                    onClick={() => setActiveTab('visits')}
                    className={`py-4 px-1 border-b-2 font-medium text-sm whitespace-nowrap transition-colors duration-200 ${
                      activeTab === 'visits'
                        ? 'border-blue-500 text-blue-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                  >
                    Site Visits
                  </button>
                  <button
                    onClick={() => setActiveTab('offerts')}
                    className={`py-4 px-1 border-b-2 font-medium text-sm whitespace-nowrap transition-colors duration-200 ${
                      activeTab === 'offerts'
                        ? 'border-blue-500 text-blue-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                  >
                    Offerts
                  </button>
                  <button
                    onClick={() => setActiveTab('invoices')}
                    className={`py-4 px-1 border-b-2 font-medium text-sm whitespace-nowrap transition-colors duration-200 ${
                      activeTab === 'invoices'
                        ? 'border-blue-500 text-blue-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                  >
                    Invoices
                  </button>
                  <button
                    onClick={() => setActiveTab('projects')}
                    className={`py-4 px-1 border-b-2 font-medium text-sm whitespace-nowrap transition-colors duration-200 ${
                      activeTab === 'projects'
                        ? 'border-blue-500 text-blue-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                  >
                    Projects
                  </button>
                </nav>
            </div>
            
              {/* Tab Content */}
              <div className="p-6">
                {tabLoading ? (
                  <div className="flex justify-center py-8">
                    <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-blue-500"></div>
                  </div>
                ) : (
                  <>
                    {/* Site Visits Tab */}
                    {activeTab === 'visits' && (
                      <div>
                        <div className="flex items-center justify-between mb-6">
                          <h3 className="text-lg font-medium text-gray-900">
                            {siteVisits.length}
                            <span className="ml-2 text-sm font-normal text-gray-500">TOTAL SITE VISITS</span>
                          </h3>
                          <Button size="sm" className="bg-blue-600 hover:bg-blue-700">
                            <Plus className="w-4 h-4 mr-2" />
                            Schedule visit
                          </Button>
                        </div>

                        {siteVisits.length > 0 ? (
                          <div className="space-y-3">
                            {siteVisits.map((visit) => (
                              <div key={visit.id} className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors">
                                <div className="flex items-center justify-between">
                                  <div className="flex items-center space-x-4">
                                    <div className="w-6 h-6 bg-orange-100 rounded flex items-center justify-center">
                                      <Hammer className="w-3 h-3 text-orange-600" />
                                    </div>
                                    <div>
                                      <p className="font-medium text-gray-900">{visit.purpose}</p>
                                      <p className="text-sm text-gray-500">{formatDate(visit.date)}</p>
                </div>
                                  </div>
                                  <span className={getStatusBadge(visit.status, 'visit')}>
                                    {visit.status}
                                  </span>
                                </div>
                                {visit.notes && (
                                  <div className="mt-3 text-sm text-gray-600">
                                    {visit.notes}
                                  </div>
                                )}
                              </div>
                            ))}
                          </div>
                        ) : (
                          <div className="text-center py-8">
                            <Hammer className="mx-auto h-8 w-8 text-gray-400 mb-4" />
                            <p className="text-gray-500">No site visits scheduled</p>
                          </div>
                        )}
              </div>
            )}
            
                    {/* Offerts Tab */}
                    {activeTab === 'offerts' && (
                      <div>
                        <div className="flex items-center justify-between mb-6">
                          <h3 className="text-lg font-medium text-gray-900">Offerts Overview</h3>
                          <Button size="sm" className="bg-blue-600 hover:bg-blue-700">
                            <Plus className="w-4 h-4 mr-2" />
                            Create offert
                          </Button>
                        </div>

                        {/* Status Cards */}
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                          {/* Awaiting Response (Pending) Offerts */}
                          <div className="shadow-sm border-0 bg-white rounded-xl overflow-hidden hover:shadow-md transition-shadow duration-300">
                            <div className="p-4">
                              <div className="flex items-center justify-between mb-2">
                                <div className="flex items-center">
                                  <div className="w-8 h-8 rounded-full bg-amber-50 flex items-center justify-center mr-3">
                                    <Clock className="w-4 h-4 text-amber-600" />
                                  </div>
                                  <div>
                                    <div className="text-xs font-medium text-gray-500 uppercase tracking-wide">Awaiting response</div>
                                    <div className="text-xl font-semibold text-gray-900">
                                      {estimates.filter(est => est.status === 'pending').length}
                                    </div>
                                  </div>
                                </div>
                              </div>
                              <div className="flex items-center justify-between">
                                <div className="text-base font-medium text-gray-900">
                                  {formatCurrency(
                                    estimates
                                      .filter(est => est.status === 'pending')
                                      .reduce((sum, est) => sum + (est.total_amount || 0), 0)
                                  )}
                                </div>
                                <span className="text-xs font-medium flex items-center text-amber-600">
                                  <Clock className="w-3 h-3 mr-1" />
                                  Pending
                                </span>
                              </div>
                            </div>
                          </div>
        
                          {/* Rejected Offerts */}
                          <div className="shadow-sm border-0 bg-white rounded-xl overflow-hidden hover:shadow-md transition-shadow duration-300">
                            <div className="p-4">
                              <div className="flex items-center justify-between mb-2">
                                <div className="flex items-center">
                                  <div className="w-8 h-8 rounded-full bg-red-50 flex items-center justify-center mr-3">
                                    <XCircle className="w-4 h-4 text-red-600" />
                                  </div>
                                  <div>
                                    <div className="text-xs font-medium text-gray-500 uppercase tracking-wide">Rejected offerts</div>
                                    <div className="text-xl font-semibold text-gray-900">
                                      {estimates.filter(est => est.status === 'rejected').length}
                                    </div>
                                  </div>
                                </div>
                              </div>
                              <div className="flex items-center justify-between">
                                <div className="text-base font-medium text-gray-900">
                                  {formatCurrency(
                                    estimates
                                      .filter(est => est.status === 'rejected')
                                      .reduce((sum, est) => sum + (est.total_amount || 0), 0)
                                  )}
                                </div>
                                <span className="text-xs font-medium flex items-center text-red-600">
                                  <XCircle className="w-3 h-3 mr-1" />
                                  Rejected
                                </span>
                              </div>
                            </div>
                          </div>
            
                          {/* Accepted Offerts */}
                          <div className="shadow-sm border-0 bg-white rounded-xl overflow-hidden hover:shadow-md transition-shadow duration-300">
                            <div className="p-4">
                              <div className="flex items-center justify-between mb-2">
                                <div className="flex items-center">
                                  <div className="w-8 h-8 rounded-full bg-green-50 flex items-center justify-center mr-3">
                                    <CheckCircle className="w-4 h-4 text-green-600" />
                                  </div>
                                  <div>
                                    <div className="text-xs font-medium text-gray-500 uppercase tracking-wide">Accepted offerts</div>
                                    <div className="text-xl font-semibold text-gray-900">
                                      {estimates.filter(est => est.status === 'accepted').length}
                                    </div>
                                  </div>
                                </div>
                              </div>
                              <div className="flex items-center justify-between">
                                <div className="text-base font-medium text-gray-900">
                                  {formatCurrency(
                                    estimates
                                      .filter(est => est.status === 'accepted')
                                      .reduce((sum, est) => sum + (est.total_amount || 0), 0)
                                  )}
                                </div>
                                <span className="text-xs font-medium flex items-center text-green-600">
                                  <CheckCircle className="w-3 h-3 mr-1" />
                                  Accepted
                                </span>
                              </div>
                            </div>
                          </div>
                        </div>

                        {/* Recent Offerts List */}
                        {estimates.length > 0 && (
                          <div className="mt-8">
                            <h4 className="text-base font-medium text-gray-900 mb-4">Recent Offerts</h4>
                            <div className="space-y-3">
                              {estimates.slice(0, 5).map((estimate) => (
                                <div key={estimate.id} className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors">
                                  <div className="flex items-center justify-between">
                                    <div className="flex items-center space-x-4">
                                      <div className="w-6 h-6 bg-blue-100 rounded flex items-center justify-center">
                                        <FileText className="w-3 h-3 text-blue-600" />
                      </div>
                      <div>
                                        <p className="font-medium text-gray-900">{estimate.project_name || 'Untitled Project'}</p>
                                        <p className="text-sm text-gray-500">{estimate.estimate_number}</p>
                                      </div>
                                    </div>
                                    <div className="text-right">
                                      <p className="font-medium text-gray-900">{formatCurrency(estimate.total_amount || 0)}</p>
                                      <div className="flex items-center space-x-2">
                                        <span className={getStatusBadge(estimate.status, 'estimate')}>
                                          {estimate.status}
                            </span>
                                      </div>
                                    </div>
                                  </div>
                                  <div className="mt-3 flex items-center justify-between text-sm text-gray-500">
                                    <span>{formatDate(estimate.date)}</span>
                                    {estimate.valid_until && (
                                      <span>Valid until {formatDate(estimate.valid_until)}</span>
                                    )}
                                  </div>
                                </div>
                              ))}
                            </div>
                            {estimates.length > 5 && (
                              <div className="mt-4 text-center">
                                <Button variant="outline" size="sm">
                                  View all {estimates.length} offerts
                                </Button>
                              </div>
                            )}
                          </div>
                        )}

                        {estimates.length === 0 && (
                          <div className="text-center py-12 mt-8">
                            <FileText className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                            <h4 className="text-lg font-medium text-gray-900 mb-2">No offerts yet</h4>
                            <p className="text-gray-500 mb-6">Get started by creating your first offert for this customer.</p>
                            <Button className="bg-blue-600 hover:bg-blue-700">
                              <Plus className="w-4 h-4 mr-2" />
                              Create first offert
                            </Button>
                          </div>
                        )}
                      </div>
                    )}

                    {/* Invoices Tab */}
                    {activeTab === 'invoices' && (
                      <div>
                        <div className="flex items-center justify-between mb-6">
                          <h3 className="text-lg font-medium text-gray-900">Invoices Overview</h3>
                          <Button size="sm" className="bg-blue-600 hover:bg-blue-700">
                            <Plus className="w-4 h-4 mr-2" />
                            Create invoice
                          </Button>
                        </div>

                        {/* Status Cards */}
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                          {/* Paid Invoices */}
                          <div className="shadow-sm border-0 bg-white rounded-xl overflow-hidden hover:shadow-md transition-shadow duration-300">
                            <div className="p-4">
                              <div className="flex items-center justify-between mb-2">
                                <div className="flex items-center">
                                  <div className="w-8 h-8 rounded-full bg-green-50 flex items-center justify-center mr-3">
                                    <CheckCircle className="w-4 h-4 text-green-600" />
                                  </div>
                                  <div>
                                    <div className="text-xs font-medium text-gray-500 uppercase tracking-wide">Paid invoices</div>
                                    <div className="text-xl font-semibold text-gray-900">
                                      {invoices.filter(inv => inv.status === 'paid').length}
                                    </div>
                                  </div>
                                </div>
                              </div>
                              <div className="flex items-center justify-between">
                                <div className="text-base font-medium text-gray-900">
                                  {formatCurrency(
                                    invoices
                                      .filter(inv => inv.status === 'paid')
                                      .reduce((sum, inv) => sum + (inv.amount || 0), 0)
                                  )}
                                </div>
                                <span className="text-xs font-medium flex items-center text-green-600">
                                  <CheckCircle className="w-3 h-3 mr-1" />
                                  Paid
                                </span>
                              </div>
                            </div>
                          </div>

                          {/* Sent Invoices */}
                          <div className="shadow-sm border-0 bg-white rounded-xl overflow-hidden hover:shadow-md transition-shadow duration-300">
                            <div className="p-4">
                              <div className="flex items-center justify-between mb-2">
                                <div className="flex items-center">
                                  <div className="w-8 h-8 rounded-full bg-blue-50 flex items-center justify-center mr-3">
                                    <FileText className="w-4 h-4 text-blue-600" />
                                  </div>
                                  <div>
                                    <div className="text-xs font-medium text-gray-500 uppercase tracking-wide">Sent invoices</div>
                                    <div className="text-xl font-semibold text-gray-900">
                                      {invoices.filter(inv => inv.status === 'sent').length}
                                    </div>
                                  </div>
                                </div>
                              </div>
                              <div className="flex items-center justify-between">
                                <div className="text-base font-medium text-gray-900">
                                  {formatCurrency(
                                    invoices
                                      .filter(inv => inv.status === 'sent')
                                      .reduce((sum, inv) => sum + (inv.amount || 0), 0)
                                  )}
                                </div>
                                <span className="text-xs font-medium flex items-center text-blue-600">
                                  <FileText className="w-3 h-3 mr-1" />
                                  Sent
                                </span>
                              </div>
                            </div>
                          </div>
        
                          {/* Overdue Invoices */}
                          <div className="shadow-sm border-0 bg-white rounded-xl overflow-hidden hover:shadow-md transition-shadow duration-300">
                            <div className="p-4">
                              <div className="flex items-center justify-between mb-2">
                                <div className="flex items-center">
                                  <div className="w-8 h-8 rounded-full bg-red-50 flex items-center justify-center mr-3">
                                    <AlertTriangle className="w-4 h-4 text-red-600" />
                                  </div>
                                  <div>
                                    <div className="text-xs font-medium text-gray-500 uppercase tracking-wide">Overdue invoices</div>
                                    <div className="text-xl font-semibold text-gray-900">
                                      {invoices.filter(inv => inv.status === 'overdue').length}
                                    </div>
                                  </div>
                                </div>
                              </div>
                              <div className="flex items-center justify-between">
                                <div className="text-base font-medium text-gray-900">
                                  {formatCurrency(
                                    invoices
                                      .filter(inv => inv.status === 'overdue')
                                      .reduce((sum, inv) => sum + (inv.amount || 0), 0)
                                  )}
                                </div>
                                <span className="text-xs font-medium flex items-center text-red-600">
                                  <AlertTriangle className="w-3 h-3 mr-1" />
                                  Overdue
                                </span>
                              </div>
                            </div>
                          </div>
                        </div>

                        {/* Recent Invoices List */}
                        {invoices.length > 0 && (
                          <div className="mt-8">
                            <h4 className="text-base font-medium text-gray-900 mb-4">Recent Invoices</h4>
                            <div className="space-y-3">
                              {invoices.slice(0, 5).map((invoice) => (
                                <div key={invoice.id} className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors">
                                  <div className="flex items-center justify-between">
                                    <div className="flex items-center space-x-4">
                                      <div className="w-6 h-6 bg-green-100 rounded flex items-center justify-center">
                                        <DollarSign className="w-3 h-3 text-green-600" />
                                      </div>
                                      <div>
                                        <p className="font-medium text-gray-900">{invoice.invoice_number}</p>
                                        <p className="text-sm text-gray-500">{formatDate(invoice.date)}</p>
                                      </div>
                                    </div>
                                    <div className="text-right">
                                      <p className="font-medium text-gray-900">{formatCurrency(invoice.amount || 0)}</p>
                                      <span className={getStatusBadge(invoice.status, 'invoice')}>
                                        {invoice.status}
                                      </span>
                                    </div>
                                  </div>
                                  {invoice.due_date && (
                                    <div className="mt-3 text-sm text-gray-500">
                                      Due {formatDate(invoice.due_date)}
                                    </div>
                                  )}
                                </div>
                              ))}
                            </div>
                            {invoices.length > 5 && (
                              <div className="mt-4 text-center">
                                <Button variant="outline" size="sm">
                                  View all {invoices.length} invoices
                                </Button>
                              </div>
                            )}
                          </div>
                        )}

                        {invoices.length === 0 && (
                          <div className="text-center py-12 mt-8">
                            <DollarSign className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                            <h4 className="text-lg font-medium text-gray-900 mb-2">No invoices yet</h4>
                            <p className="text-gray-500 mb-6">Get started by creating your first invoice for this customer.</p>
                            <Button className="bg-blue-600 hover:bg-blue-700">
                              <Plus className="w-4 h-4 mr-2" />
                              Create first invoice
                            </Button>
                          </div>
                        )}
                      </div>
                    )}

                    {/* Projects Tab */}
                    {activeTab === 'projects' && (
                      <div>
                        <div className="flex items-center justify-between mb-6">
                          <h3 className="text-lg font-medium text-gray-900">
                            {projects.length}
                            <span className="ml-2 text-sm font-normal text-gray-500">TOTAL PROJECTS</span>
                          </h3>
                          <Button size="sm" className="bg-blue-600 hover:bg-blue-700">
                            <Plus className="w-4 h-4 mr-2" />
                            Create project
                          </Button>
                        </div>

                        {projects.length > 0 ? (
                          <div className="space-y-3">
                            {projects.map((project) => (
                              <div key={project.id} className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors">
                                <div className="flex items-center justify-between">
                                  <div className="flex items-center space-x-4">
                                    <div className="w-6 h-6 bg-purple-100 rounded flex items-center justify-center">
                                      <Building className="w-3 h-3 text-purple-600" />
                                    </div>
                                    <div>
                                      <p className="font-medium text-gray-900">{project.name}</p>
                                      <p className="text-sm text-gray-500">Started {formatDate(project.start_date)}</p>
                                    </div>
                                  </div>
                                  <div className="text-right">
                                    <p className="font-medium text-gray-900">{formatCurrency(project.budget || 0)}</p>
                                    <span className={getStatusBadge(project.status, 'project')}>
                                      {project.status.replace('-', ' ')}
                                    </span>
                                  </div>
                                </div>
                                <div className="mt-3 flex items-center justify-between text-sm text-gray-500">
                                  <span>{project.description}</span>
                                  {project.end_date && (
                                    <span>
                                      {project.status === 'completed' ? 'Completed' : 'Due'} {formatDate(project.end_date)}
                                    </span>
                                  )}
                                </div>
                  </div>
                ))}
              </div>
            ) : (
                          <div className="text-center py-8">
                            <Building className="mx-auto h-8 w-8 text-gray-400 mb-4" />
                            <h4 className="text-lg font-medium text-gray-900 mb-2">No projects yet</h4>
                            <p className="text-gray-500 mb-6">Start by creating your first project for this customer.</p>
                            <Button className="bg-blue-600 hover:bg-blue-700">
                              <Plus className="w-4 h-4 mr-2" />
                              Create first project
                            </Button>
                          </div>
                        )}
                      </div>
                    )}
                  </>
                )}
              </div>

              {/* Tab footer with pagination */}
              <div className="border-t border-gray-200 px-6 py-3">
                <div className="flex items-center justify-between">
                  <p className="text-sm text-gray-500">
                    Viewing 1-{
                      activeTab === 'visits' ? siteVisits.length :
                      activeTab === 'offerts' ? estimates.length :
                      activeTab === 'invoices' ? invoices.length :
                      projects.length
                    } of {
                      activeTab === 'visits' ? siteVisits.length :
                      activeTab === 'offerts' ? estimates.length :
                      activeTab === 'invoices' ? invoices.length :
                      projects.length
                    } results
                  </p>
                  <div className="flex items-center space-x-2">
                    <Button variant="outline" size="sm" disabled>
                      Previous
                    </Button>
                    <Button variant="outline" size="sm" disabled>
                      Next
                </Button>
                  </div>
                </div>
              </div>
          </div>
        </div>
      </div>
      
      {/* Delete Confirmation Modal */}
      {isDeleteModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
            <div className="bg-white rounded-lg max-w-md w-full p-6 shadow-xl">
            <div className="flex items-center mb-4">
              <div className="flex-shrink-0">
                <AlertTriangle className="h-6 w-6 text-red-500" />
              </div>
              <div className="ml-3">
                  <h3 className="text-lg font-medium text-gray-900">
                    Confirm deletion
                </h3>
              </div>
            </div>
            
            <div className="mb-4">
                <p className="text-sm text-gray-600">
                  Are you sure you want to delete this customer? This action cannot be undone and will delete all associated contacts, estimates, and invoices.
              </p>
            </div>
            
            <div className="flex justify-end space-x-3">
              <Button 
                variant="outline" 
                onClick={() => setIsDeleteModalOpen(false)}
                disabled={isDeleting}
              >
                  Cancel
              </Button>
              <Button 
                variant="destructive" 
                onClick={handleDelete}
                disabled={isDeleting}
                className="min-w-[100px]"
              >
                {isDeleting ? (
                  <div className="flex items-center">
                    <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2"></div>
                      Deleting...
                  </div>
                ) : (
                    'Delete'
                )}
              </Button>
            </div>
          </div>
        </div>
      )}
      </div>
    </div>
  );
} 