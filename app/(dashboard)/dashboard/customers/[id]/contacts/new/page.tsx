'use client';

import React, { useEffect, useState } from 'react';
import { supabase } from '@/lib/supabase/supabase';
import { Customer } from '@/lib/supabase/supabase';
import { useParams, useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ArrowLeft, Save } from 'lucide-react';
import Link from 'next/link';
import { toast } from 'sonner';

export default function NewContactPage() {
  const params = useParams();
  const router = useRouter();
  const customerId = params.id as string;
  
  const [customer, setCustomer] = useState<Customer | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);

  const [formData, setFormData] = useState({
    customer_id: customerId,
    first_name: '',
    last_name: '',
    email: '',
    phone: '',
    position: '',
    is_primary: false,
    notes: '',
  });
  
  const [errors, setErrors] = useState<Record<string, string>>({});

  useEffect(() => {
    if (!customerId) return;
    
    async function fetchCustomer() {
      try {
        setLoading(true);
        const { data, error } = await supabase
          .from('customers')
          .select('*')
          .eq('id', customerId)
          .single();
        
        if (error) throw error;
        setCustomer(data);

        // Check if there are any existing contacts for this customer
        const { count, error: countError } = await supabase
          .from('customer_contacts')
          .select('*', { count: 'exact', head: true })
          .eq('customer_id', customerId);
        
        // If no contacts exist yet, make this one primary by default
        if ((count === 0 || count === null) && !countError) {
          setFormData(prev => ({ ...prev, is_primary: true }));
        }
      } catch (error: any) {
        console.error('Error fetching customer:', error.message);
        toast.error('Error loading customer details');
      } finally {
        setLoading(false);
      }
    }
    
    fetchCustomer();
  }, [customerId]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    
    if (type === 'checkbox') {
      const checked = (e.target as HTMLInputElement).checked;
      setFormData(prev => ({ ...prev, [name]: checked }));
    } else {
      setFormData(prev => ({ ...prev, [name]: value }));
    }
    
    // Clear error when field is edited
    if (errors[name]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};
    
    if (!formData.first_name.trim()) {
      newErrors.first_name = 'Förnamn krävs';
    }
    
    if (!formData.last_name.trim()) {
      newErrors.last_name = 'Efternamn krävs';
    }
    
    // Basic email validation if provided
    if (formData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'Giltig e-post krävs';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }
    
    setSaving(true);
    
    try {
      // If this is set as primary, reset primary status for all other contacts
      if (formData.is_primary) {
        await supabase
          .from('customer_contacts')
          .update({ is_primary: false })
          .eq('customer_id', customerId);
      }
      
      // Insert the new contact
      const { data, error } = await supabase
        .from('customer_contacts')
        .insert([formData])
        .select()
        .single();
      
      if (error) {
        throw error;
      }
      
      toast.success('Kontakt skapad framgångsrikt');
      // Navigate back to contacts list
      router.push(`/dashboard/customers/${customerId}/contacts`);
    } catch (error: any) {
      console.error('Error creating contact:', error.message);
      toast.error('Error creating contact', {
        description: error.message
      });
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-center p-8">
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      </div>
    );
  }
  
  if (!customer) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="bg-white dark:bg-gray-800 shadow-md rounded-lg p-6 text-center">
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">Kunden hittades inte</h3>
          <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
            Kunden du letar efter finns inte eller så har du inte behörighet att visa den.
          </p>
          <Link href="/dashboard/customers">
            <Button className="mt-4">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Tillbaka till kunder
            </Button>
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8 max-w-4xl">
      <div className="flex items-center mb-6">
        <Link href={`/dashboard/customers/${customerId}/contacts`} className="mr-4">
          <Button variant="ghost" size="icon">
            <ArrowLeft className="h-5 w-5" />
          </Button>
        </Link>
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Ny kontakt</h1>
          <p className="text-gray-500 dark:text-gray-400">
            {customer.company_name || customer.name}
          </p>
        </div>
      </div>
      
      <div className="bg-white dark:bg-gray-800 shadow-md rounded-lg p-6">
        <form onSubmit={handleSubmit}>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* First Name Field */}
            <div>
              <label htmlFor="first_name" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Förnamn *
              </label>
              <Input
                id="first_name"
                name="first_name"
                value={formData.first_name}
                onChange={handleChange}
                required
                className={errors.first_name ? 'border-red-500' : ''}
              />
              {errors.first_name && <p className="text-sm text-red-500 mt-1">{errors.first_name}</p>}
            </div>
            
            {/* Last Name Field */}
            <div>
              <label htmlFor="last_name" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Efternamn *
              </label>
              <Input
                id="last_name"
                name="last_name"
                value={formData.last_name}
                onChange={handleChange}
                required
                className={errors.last_name ? 'border-red-500' : ''}
              />
              {errors.last_name && <p className="text-sm text-red-500 mt-1">{errors.last_name}</p>}
            </div>
            
            {/* Email Field */}
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                E-post
              </label>
              <Input
                id="email"
                name="email"
                type="email"
                value={formData.email}
                onChange={handleChange}
                className={errors.email ? 'border-red-500' : ''}
              />
              {errors.email && <p className="text-sm text-red-500 mt-1">{errors.email}</p>}
            </div>
            
            {/* Phone Field */}
            <div>
              <label htmlFor="phone" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Telefon
              </label>
              <Input
                id="phone"
                name="phone"
                value={formData.phone}
                onChange={handleChange}
                className={errors.phone ? 'border-red-500' : ''}
              />
              {errors.phone && <p className="text-sm text-red-500 mt-1">{errors.phone}</p>}
            </div>
            
            {/* Position Field */}
            <div className="md:col-span-2">
              <label htmlFor="position" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Position
              </label>
              <Input
                id="position"
                name="position"
                value={formData.position}
                onChange={handleChange}
                className={errors.position ? 'border-red-500' : ''}
              />
              {errors.position && <p className="text-sm text-red-500 mt-1">{errors.position}</p>}
            </div>

            {/* Primary Contact Checkbox */}
            <div className="md:col-span-2">
              <label className="inline-flex items-center">
                <input
                  type="checkbox"
                  name="is_primary"
                  checked={formData.is_primary}
                  onChange={handleChange}
                  className="h-4 w-4 text-blue-600 dark:text-blue-400"
                />
                <span className="ml-2 text-gray-700 dark:text-gray-300">Gör detta till primär kontakt</span>
              </label>
              <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                Den primära kontakten visas först och används som standard för kommunikation.
              </p>
            </div>
            
            {/* Notes Field */}
            <div className="md:col-span-2">
              <label htmlFor="notes" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Anteckningar
              </label>
              <textarea
                id="notes"
                name="notes"
                rows={4}
                value={formData.notes}
                onChange={handleChange}
                className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
              />
            </div>
          </div>
          
          <div className="mt-8 flex justify-end">
            <Link href={`/dashboard/customers/${customerId}/contacts`}>
              <Button type="button" variant="outline" className="mr-2">
                Avbryt
              </Button>
            </Link>
            <Button type="submit" disabled={saving}>
              {saving ? 
                <div className="flex items-center">
                  <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2"></div>
                  Sparar...
                </div> : 
                <>
                  <Save className="h-4 w-4 mr-2" />
                  Spara kontakt
                </>
              }
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
} 