'use client';

import React, { useEffect, useState } from 'react';
import { supabase } from '@/lib/supabase/supabase';
import { CustomerContact, Customer } from '@/lib/supabase/supabase';
import Link from 'next/link';
import { ArrowLeft, Edit, Plus, Trash2, User, Mail, Phone, AlertTriangle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useParams, useRouter } from 'next/navigation';
import { toast } from 'sonner';

export default function CustomerContactsPage() {
  const params = useParams();
  const router = useRouter();
  const customerId = params.id as string;
  
  const [customer, setCustomer] = useState<Customer | null>(null);
  const [contacts, setContacts] = useState<CustomerContact[]>([]);
  const [loading, setLoading] = useState(true);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [contactToDelete, setContactToDelete] = useState<CustomerContact | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);

  useEffect(() => {
    if (!customerId) return;
    
    async function fetchData() {
      try {
        setLoading(true);
        
        // Fetch customer details
        const { data: customerData, error: customerError } = await supabase
          .from('customers')
          .select('*')
          .eq('id', customerId)
          .single();
        
        if (customerError) throw customerError;
        setCustomer(customerData);
        
        // Fetch customer contacts
        const { data: contactsData, error: contactsError } = await supabase
          .from('customer_contacts')
          .select('*')
          .eq('customer_id', customerId)
          .order('is_primary', { ascending: false })
          .order('first_name');
        
        if (contactsError) {
          console.error('Error fetching contacts:', contactsError);
          // Don't throw error for contacts, just log it
        } else {
          setContacts(contactsData || []);
        }
      } catch (error: any) {
        console.error('Error fetching data:', error.message);
        toast.error('Error loading customer details');
      } finally {
        setLoading(false);
      }
    }
    
    fetchData();
  }, [customerId]);

  const confirmDelete = (contact: CustomerContact) => {
    setContactToDelete(contact);
    setIsDeleteModalOpen(true);
  };

  const handleDelete = async () => {
    if (!contactToDelete) return;
    
    setIsDeleting(true);
    
    try {
      const { error } = await supabase
        .from('customer_contacts')
        .delete()
        .eq('id', contactToDelete.id);
      
      if (error) throw error;
      
      // Remove the deleted contact from state
      setContacts(contacts.filter(c => c.id !== contactToDelete.id));
      toast.success('Kontakt raderad framgångsrikt');
    } catch (error: any) {
      console.error('Error deleting contact:', error.message);
      toast.error('Error deleting contact', {
        description: error.message
      });
    } finally {
      setIsDeleting(false);
      setIsDeleteModalOpen(false);
      setContactToDelete(null);
    }
  };

  const setPrimaryContact = async (contactId: string) => {
    try {
      // First, set all contacts for this customer to not primary
      const { error: resetError } = await supabase
        .from('customer_contacts')
        .update({ is_primary: false })
        .eq('customer_id', customerId);
      
      if (resetError) throw resetError;
      
      // Then set the selected contact as primary
      const { error: updateError } = await supabase
        .from('customer_contacts')
        .update({ is_primary: true })
        .eq('id', contactId);
      
      if (updateError) throw updateError;
      
      // Update the local state
      setContacts(contacts.map(contact => ({
        ...contact,
        is_primary: contact.id === contactId
      })));
      
      toast.success('Primär kontakt uppdaterad');
    } catch (error: any) {
      console.error('Error setting primary contact:', error.message);
      toast.error('Error updating primary contact', {
        description: error.message
      });
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-center p-8">
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      </div>
    );
  }
  
  if (!customer) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="bg-white dark:bg-gray-800 shadow-md rounded-lg p-6 text-center">
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">Kunden hittades inte</h3>
          <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
            Kunden du letar efter finns inte eller så har du inte behörighet att visa den.
          </p>
          <Link href="/dashboard/customers">
            <Button className="mt-4">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Tillbaka till kunder
            </Button>
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8 max-w-6xl">
      <div className="flex items-center mb-6">
        <Link href={`/dashboard/customers/${customerId}`} className="mr-4">
          <Button variant="ghost" size="icon">
            <ArrowLeft className="h-5 w-5" />
          </Button>
        </Link>
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">{customer.name}</h1>
          {customer.company_name && (
            <p className="text-gray-500 dark:text-gray-400">{customer.company_name}</p>
          )}
        </div>
      </div>
      
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white">Kontakter</h2>
        <Link href={`/dashboard/customers/${customerId}/contacts/new`}>
          <Button>
            <Plus className="h-4 w-4 mr-2" />
            Lägg till kontakt
          </Button>
        </Link>
      </div>
      
      {contacts.length > 0 ? (
        <div className="bg-white dark:bg-gray-800 shadow-md rounded-lg overflow-hidden">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead className="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Namn</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Position</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">E-post</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Telefon</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Primär</th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Åtgärder</th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              {contacts.map((contact) => (
                <tr key={contact.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="w-8 h-8 rounded-full bg-blue-100 dark:bg-blue-800 flex items-center justify-center text-blue-600 dark:text-blue-200 mr-3">
                        <User className="h-4 w-4" />
                      </div>
                      <div className="font-medium text-gray-900 dark:text-gray-100">
                        {contact.first_name} {contact.last_name}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                    {contact.position || '-'}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                    {contact.email ? (
                      <a href={`mailto:${contact.email}`} className="text-blue-600 dark:text-blue-400 hover:underline flex items-center">
                        <Mail className="h-3 w-3 mr-1" />
                        {contact.email}
                      </a>
                    ) : (
                      '-'
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                    {contact.phone ? (
                      <a href={`tel:${contact.phone}`} className="text-blue-600 dark:text-blue-400 hover:underline flex items-center">
                        <Phone className="h-3 w-3 mr-1" />
                        {contact.phone}
                      </a>
                    ) : (
                      '-'
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {contact.is_primary ? (
                      <span className="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100">
                        Primär
                      </span>
                    ) : (
                      <Button 
                        variant="ghost" 
                        size="sm" 
                        onClick={() => setPrimaryContact(contact.id)}
                        className="text-xs"
                      >
                        Gör primär
                      </Button>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div className="flex justify-end space-x-2">
                      <Button variant="ghost" size="icon" className="h-8 w-8">
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button 
                        variant="ghost" 
                        size="icon" 
                        className="h-8 w-8 text-red-600 hover:text-red-700"
                        onClick={() => confirmDelete(contact)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      ) : (
        <div className="text-center py-12 bg-white dark:bg-gray-800 rounded-lg shadow">
          <User className="h-12 w-12 mx-auto text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">Inga kontakter ännu</h3>
          <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
            Kom igång genom att lägga till den första kontakten för denna kund.
          </p>
          <Link href={`/dashboard/customers/${customerId}/contacts/new`}>
            <Button className="mt-4">
              <Plus className="h-4 w-4 mr-2" />
              Lägg till kontakt
            </Button>
          </Link>
        </div>
      )}
      
      {/* Delete Confirmation Modal */}
      {isDeleteModalOpen && contactToDelete && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
          <div className="bg-white dark:bg-gray-800 rounded-lg max-w-md w-full p-6 shadow-xl">
            <div className="flex items-center mb-4">
              <div className="flex-shrink-0">
                <AlertTriangle className="h-6 w-6 text-red-500" />
              </div>
              <div className="ml-3">
                <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
                  Bekräfta radering
                </h3>
              </div>
            </div>
            
            <div className="mb-4">
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Är du säker på att du vill radera kontakten <strong>{contactToDelete.first_name} {contactToDelete.last_name}</strong>? 
                Denna åtgärd kan inte ångras.
              </p>
            </div>
            
            <div className="flex justify-end space-x-3">
              <Button 
                variant="outline" 
                onClick={() => {
                  setIsDeleteModalOpen(false);
                  setContactToDelete(null);
                }}
                disabled={isDeleting}
              >
                Avbryt
              </Button>
              <Button 
                variant="destructive" 
                onClick={handleDelete}
                disabled={isDeleting}
                className="min-w-[100px]"
              >
                {isDeleting ? (
                  <div className="flex items-center">
                    <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2"></div>
                    Raderar...
                  </div>
                ) : (
                  'Radera'
                )}
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
} 