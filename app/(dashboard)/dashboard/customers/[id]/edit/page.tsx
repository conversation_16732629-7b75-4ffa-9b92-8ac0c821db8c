'use client'

import { useEffect, useState } from 'react'
import { useRouter, useParams } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { ArrowLeft, Save, Loader2 } from 'lucide-react'
import Link from 'next/link'
import { supabase } from '@/lib/supabase/supabase'

interface Customer {
  id: string
  name: string
  company_name?: string
  email: string
  phone?: string
  address?: string
  project_description?: string
  site_visit_date?: string
  calculation_status?: string
  quote_status?: string
  acceptance_status?: string
  project_status_notes?: string
  project_stage?: string
  source?: string
}

export default function EditCustomerPage() {
  const router = useRouter()
  const params = useParams()
  const customerId = params.id as string

  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [customer, setCustomer] = useState<Customer | null>(null)
  const [formData, setFormData] = useState({
    name: '',
    company_name: '',
    email: '',
    phone: '',
    address: '',
    project_description: '',
    site_visit_date: '',
    calculation_status: '',
    quote_status: '',
    acceptance_status: '',
    project_status_notes: '',
    project_stage: ''
  })

  useEffect(() => {
    if (customerId) {
      fetchCustomer()
    }
  }, [customerId])

  const fetchCustomer = async () => {
    try {
      setLoading(true)
      const { data, error } = await supabase
        .from('customers')
        .select('*')
        .eq('id', customerId)
        .single()

      if (error) {
        console.error('Error fetching customer:', error)
        return
      }

      setCustomer(data)
      setFormData({
        name: data.name || '',
        company_name: data.company_name || '',
        email: data.email || '',
        phone: data.phone || '',
        address: data.address || '',
        project_description: data.project_description || '',
        site_visit_date: data.site_visit_date || '',
        calculation_status: data.calculation_status || '',
        quote_status: data.quote_status || '',
        acceptance_status: data.acceptance_status || '',
        project_status_notes: data.project_status_notes || '',
        project_stage: data.project_stage || ''
      })
    } catch (error) {
      console.error('Error:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    try {
      setSaving(true)
      
      // Clean the form data to ensure no undefined/null strings
      const updateData = {
        name: formData.name || '',
        company_name: formData.company_name || null,
        email: formData.email || '',
        phone: formData.phone || null,
        address: formData.address || null,
        project_description: formData.project_description || null,
        site_visit_date: formData.site_visit_date || null,
        calculation_status: formData.calculation_status || null,
        quote_status: formData.quote_status || null,
        acceptance_status: formData.acceptance_status || null,
        project_status_notes: formData.project_status_notes || null,
        project_stage: formData.project_stage || null,
        updated_at: new Date().toISOString()
      }

      console.log('Updating customer with data:', updateData)
      console.log('Customer ID:', customerId)
      
      const { data, error } = await supabase
        .from('customers')
        .update(updateData)
        .eq('id', customerId)
        .select()

      if (error) {
        console.error('Supabase error details:', error)
        console.error('Error code:', error.code)
        console.error('Error message:', error.message)
        console.error('Error details:', error.details)
        alert(`Fel vid uppdatering av kund: ${error.message}`)
        return
      }

      console.log('Update successful:', data)
      alert('Kund uppdaterad!')
      router.push('/dashboard/customers')
    } catch (error) {
      console.error('Unexpected error:', error)
      console.error('Error type:', typeof error)
      console.error('Error properties:', Object.keys(error || {}))
      alert(`Fel vid uppdatering av kund: ${error instanceof Error ? error.message : 'Okänt fel'}`)
    } finally {
      setSaving(false)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <Loader2 className="w-8 h-8 animate-spin" />
      </div>
    )
  }

  if (!customer) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold mb-2">Kund hittades inte</h2>
          <Link href="/dashboard/customers">
            <Button variant="outline">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Tillbaka till kunder
            </Button>
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center space-x-4">
            <Link href="/dashboard/customers">
              <Button variant="outline" size="sm">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Tillbaka
              </Button>
            </Link>
            <div>
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                Redigera kund
              </h1>
              <p className="text-gray-500 dark:text-gray-400">
                Uppdatera kundinformation och projektstatus
              </p>
            </div>
          </div>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <span>Grundläggande information</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Namn *
                  </label>
                  <Input
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    placeholder="Kundnamn"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Företagsnamn
                  </label>
                  <Input
                    name="company_name"
                    value={formData.company_name}
                    onChange={handleInputChange}
                    placeholder="Företagsnamn (valfritt)"
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    E-post *
                  </label>
                  <Input
                    name="email"
                    type="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    placeholder="E-postadress"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Telefon
                  </label>
                  <Input
                    name="phone"
                    value={formData.phone}
                    onChange={handleInputChange}
                    placeholder="Telefonnummer"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Adress
                </label>
                <Input
                  name="address"
                  value={formData.address}
                  onChange={handleInputChange}
                  placeholder="Fullständig adress"
                />
              </div>
            </CardContent>
          </Card>

          {/* Project Information */}
          <Card>
            <CardHeader>
              <CardTitle>Projektinformation</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Projektbeskrivning
                </label>
                <Textarea
                  name="project_description"
                  value={formData.project_description}
                  onChange={handleInputChange}
                  placeholder="Beskrivning av projektet..."
                  rows={3}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Platsbesök datum
                  </label>
                  <Input
                    name="site_visit_date"
                    type="date"
                    value={formData.site_visit_date}
                    onChange={handleInputChange}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Projektstatus
                  </label>
                  <select
                    name="project_stage"
                    value={formData.project_stage}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-200 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
                  >
                    <option value="">Välj status</option>
                    <option value="completed_paid">Slutförd (inbetald)</option>
                    <option value="accepted">Accepterat</option>
                    <option value="quote_sent">Offert skickad</option>
                    <option value="rejected">Ej accepterat</option>
                    <option value="calculation_in_progress">Pågående kalkyl</option>
                  </select>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Kalkylstatus
                  </label>
                  <Input
                    name="calculation_status"
                    value={formData.calculation_status}
                    onChange={handleInputChange}
                    placeholder="Status för kalkyl"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Offertstatus
                  </label>
                  <Input
                    name="quote_status"
                    value={formData.quote_status}
                    onChange={handleInputChange}
                    placeholder="Status för offert"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Acceptansstatus
                  </label>
                  <Input
                    name="acceptance_status"
                    value={formData.acceptance_status}
                    onChange={handleInputChange}
                    placeholder="Status för acceptans"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Statusanteckningar
                </label>
                <Textarea
                  name="project_status_notes"
                  value={formData.project_status_notes}
                  onChange={handleInputChange}
                  placeholder="Ytterligare anteckningar om projektstatus..."
                  rows={3}
                />
              </div>
            </CardContent>
          </Card>

          {/* Actions */}
          <div className="flex items-center justify-end space-x-4">
            <Link href="/dashboard/customers">
              <Button variant="outline" type="button">
                Avbryt
              </Button>
            </Link>
            <Button type="submit" disabled={saving}>
              {saving ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Sparar...
                </>
              ) : (
                <>
                  <Save className="w-4 h-4 mr-2" />
                  Spara ändringar
                </>
              )}
            </Button>
          </div>
        </form>
      </div>
    </div>
  )
} 