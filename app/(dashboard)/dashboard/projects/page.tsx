'use client';

import React, { useEffect, useState } from 'react';
import { supabase } from '@/lib/supabase/supabase';
import Link from 'next/link';
import { 
  Briefcase, 
  Edit, 
  Search, 
  Plus, 
  Trash2, 
  AlertTriangle, 
  FileText, 
  DollarSign, 
  Users, 
  Calendar,
  Clock,
  User,
  MapPin,
  TrendingUp,
  Activity
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { toast } from 'sonner';
import { ExpandableList } from '@/components/ui/expandable-list';
import { StatsCards } from '@/components/ui/stats-cards';

interface Project {
  id: string;
  name: string;
  description?: string;
  status: 'planning' | 'active' | 'on-hold' | 'completed' | 'cancelled';
  start_date?: string;
  end_date?: string;
  customer_id?: string;
  budget?: number;
  estimated_hours?: number;
  notes?: string;
  priority: 'low' | 'medium' | 'high';
  created_at: string;
  updated_at: string;
  customers?: {
    name: string;
    company_name?: string;
    email?: string;
    phone?: string;
  };
}

interface RelatedRecords {
  timeline: number;
  staff: number;
  estimates: number;
  invoices: number;
}

export default function ProjectsPage() {
  const [projects, setProjects] = useState<Project[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [projectToDelete, setProjectToDelete] = useState<Project | null>(null);
  const [relatedRecords, setRelatedRecords] = useState<RelatedRecords | null>(null);
  const [isCheckingRelated, setIsCheckingRelated] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [projectStats, setProjectStats] = useState({
    totalProjects: 0,
    activeProjects: 0,
    completedProjects: 0,
    onHoldProjects: 0,
    totalBudget: 0
  });

  useEffect(() => {
    async function fetchProjects() {
      try {
        setLoading(true);
        const { data, error } = await supabase
          .from('projects')
          .select(`
            *,
            customers (
              name,
              company_name,
              email,
              phone
            )
          `)
          .order('created_at', { ascending: false });

        if (error) {
          throw error;
        }

        const projectsData = data || [];
        setProjects(projectsData);

        // Calculate statistics
        const totalProjects = projectsData.length;
        const activeProjects = projectsData.filter(p => p.status === 'active').length;
        const completedProjects = projectsData.filter(p => p.status === 'completed').length;
        const onHoldProjects = projectsData.filter(p => p.status === 'on-hold').length;
        const totalBudget = projectsData.reduce((sum, p) => sum + (parseFloat(p.budget as any) || 0), 0);

        setProjectStats({
          totalProjects,
          activeProjects,
          completedProjects,
          onHoldProjects,
          totalBudget
        });
      } catch (error: any) {
        console.error('Error fetching projects:', error.message);
        toast.error('Kunde inte hämta projekt');
      } finally {
        setLoading(false);
      }
    }
    
    fetchProjects();
  }, []);

  const filteredProjects = projects.filter(project => 
    project.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    (project.description && project.description.toLowerCase().includes(searchQuery.toLowerCase())) ||
    (project.customers?.name && project.customers.name.toLowerCase().includes(searchQuery.toLowerCase())) ||
    (project.customers?.company_name && project.customers.company_name.toLowerCase().includes(searchQuery.toLowerCase()))
  );

  const confirmDelete = async (id: string) => {
    const project = projects.find(p => p.id === id);
    if (!project) return;
    
    setProjectToDelete(project);
    setIsCheckingRelated(true);
    setIsDeleteModalOpen(true);
    
    try {
      // Check for related records
      const [timelineResult, staffResult, estimatesResult, invoicesResult] = await Promise.all([
        supabase.from('project_timeline').select('id', { count: 'exact' }).eq('project_id', id),
        supabase.from('project_staff').select('id', { count: 'exact' }).eq('project_id', id),
        supabase.from('estimates').select('id', { count: 'exact' }).eq('project_name', project.name),
        supabase.from('invoices').select('id', { count: 'exact' }).eq('project_id', id)
      ]);
      
      setRelatedRecords({
        timeline: timelineResult.count || 0,
        staff: staffResult.count || 0,
        estimates: estimatesResult.count || 0,
        invoices: invoicesResult.count || 0
      });
    } catch (error: any) {
      console.error('Error checking related records:', error.message);
      toast.error('Error checking related records');
      setIsDeleteModalOpen(false);
    } finally {
      setIsCheckingRelated(false);
    }
  };

  const handleDelete = async () => {
    if (!projectToDelete) return;
    
    try {
      setIsDeleting(true);
      
      // If there are related records, we need to delete them first (cascade delete)
      if (relatedRecords) {
        const deletePromises = [];
        
        // Delete in the correct order to avoid foreign key violations
        if (relatedRecords.timeline > 0) {
          deletePromises.push(
            supabase.from('project_timeline').delete().eq('project_id', projectToDelete.id)
          );
        }
        
        if (relatedRecords.staff > 0) {
          deletePromises.push(
            supabase.from('project_staff').delete().eq('project_id', projectToDelete.id)
          );
        }
        
        if (relatedRecords.invoices > 0) {
          deletePromises.push(
            supabase.from('invoices').delete().eq('project_id', projectToDelete.id)
          );
        }
        
        // Execute all deletions
        const results = await Promise.all(deletePromises);
        
        // Check for errors in any of the deletions
        for (const result of results) {
          if (result.error) throw result.error;
        }
      }
      
      // Finally delete the project
      const { error } = await supabase
        .from('projects')
        .delete()
        .eq('id', projectToDelete.id);
      
      if (error) throw error;
      
      // Remove the deleted project from state
      setProjects(projects.filter(p => p.id !== projectToDelete.id));
      setIsDeleteModalOpen(false);
      setProjectToDelete(null);
      setRelatedRecords(null);
      
      // Show success notification
      toast.success('Project deleted successfully', {
        description: 'Project and all related records have been removed.'
      });
      
    } catch (error: any) {
      console.error('Error deleting project:', error.message);
      toast.error('Error deleting project', {
        description: error.message
      });
    } finally {
      setIsDeleting(false);
    }
  };

  const getStatusDisplayName = (status: string) => {
    switch (status) {
      case 'planning': return 'Planering'
      case 'active': return 'Aktiv'
      case 'on-hold': return 'Pausad'
      case 'completed': return 'Klar'
      case 'cancelled': return 'Avbruten'
      default: return status
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': 
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
      case 'planning':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
      case 'on-hold':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
      case 'completed':
        return 'bg-emerald-100 text-emerald-800 dark:bg-emerald-900 dark:text-emerald-200'
      case 'cancelled':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
      case 'low':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('sv-SE', { style: 'currency', currency: 'SEK' }).format(amount);
  };

  const formatDate = (dateString: string) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString('sv-SE');
  };

  const projectMetrics = [
    {
      label: "Totalt projekt",
      value: projectStats.totalProjects,
      icon: <Briefcase className="w-5 h-5 text-blue-600 dark:text-blue-400" />,
      change: "+12%",
      changeType: "positive" as const,
      changeLabel: "senaste månaden"
    },
    {
      label: "Aktiva projekt",
      value: projectStats.activeProjects,
      icon: <Activity className="w-5 h-5 text-green-600 dark:text-green-400" />,
      change: "+8%",
      changeType: "positive" as const,
      changeLabel: "senaste månaden"
    },
    {
      label: "Slutförda projekt",
      value: projectStats.completedProjects,
      icon: <TrendingUp className="w-5 h-5 text-emerald-600 dark:text-emerald-400" />,
      change: "+15%",
      changeType: "positive" as const,
      changeLabel: "senaste månaden"
    },
    {
      label: "Pausade projekt",
      value: projectStats.onHoldProjects,
      icon: <Clock className="w-5 h-5 text-amber-600 dark:text-amber-400" />,
      change: "-5%",
      changeType: "negative" as const,
      changeLabel: "senaste månaden"
    },
    {
      label: "Total budget",
      value: formatCurrency(projectStats.totalBudget),
      icon: <DollarSign className="w-5 h-5 text-indigo-600 dark:text-indigo-400" />,
      change: "+22%",
      changeType: "positive" as const,
      changeLabel: "senaste månaden"
    }
  ];

  const projectColumns = [
    { key: 'name', label: 'Projektnamn' },
    { key: 'customer', label: 'Kund' },
    { key: 'status', label: 'Status' },
    { key: 'priority', label: 'Prioritet' },
    { key: 'budget', label: 'Budget' },
    { key: 'actions', label: 'Åtgärder', className: 'text-right' }
  ];

  const renderProjectCell = (project: Project, columnKey: string) => {
    switch (columnKey) {
      case 'name':
        return (
          <div>
            <Link href={`/dashboard/projects/${project.id}`} className="text-blue-600 dark:text-blue-400 hover:underline font-medium">
              {project.name}
            </Link>
            {project.description && (
              <p className="text-sm text-gray-500 dark:text-gray-400 truncate">{project.description}</p>
            )}
          </div>
        );
      case 'customer':
        return (
          <div>
            {project.customers ? (
              <>
                <span className="text-gray-700 dark:text-gray-300 font-medium">
                  {project.customers.name}
                </span>
                {project.customers.company_name && (
                  <p className="text-sm text-gray-500 dark:text-gray-400">{project.customers.company_name}</p>
                )}
              </>
            ) : (
              <span className="text-gray-500 dark:text-gray-400">-</span>
            )}
          </div>
        );
      case 'status':
        return (
          <span className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(project.status)} w-fit`}>
            {getStatusDisplayName(project.status)}
          </span>
        );
      case 'priority':
        return (
          <span className={`px-3 py-1 rounded-full text-xs font-medium ${getPriorityColor(project.priority)} w-fit`}>
            {project.priority === 'high' ? 'Hög' : project.priority === 'medium' ? 'Medium' : 'Låg'}
          </span>
        );
      case 'budget':
        return (
          <span className="text-gray-700 dark:text-gray-300 font-medium">
            {project.budget ? formatCurrency(project.budget) : '-'}
          </span>
        );
      case 'actions':
        return (
          <div className="flex justify-end space-x-2" onClick={(e) => e.stopPropagation()}>
            <Link href={`/dashboard/projects/${project.id}/edit`} className="text-indigo-600 dark:text-indigo-400 hover:text-indigo-900 dark:hover:text-indigo-200">
              <Edit className="h-5 w-5" />
            </Link>
            <button
              onClick={() => confirmDelete(project.id)}
              className="text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-200"
            >
              <Trash2 className="h-5 w-5" />
            </button>
          </div>
        );
      default:
        return null;
    }
  };

  const renderProjectExpandedContent = (project: Project) => (
    <div className="flex flex-col md:flex-row gap-8">
      <div className="flex-1 space-y-3">
        <div className="text-lg font-medium text-gray-900 dark:text-white mb-4">Projektdetaljer</div>
        <div><span className="font-medium text-gray-700 dark:text-gray-300">Namn:</span> <span className="text-gray-900 dark:text-white">{project.name}</span></div>
        {project.description && (
          <div><span className="font-medium text-gray-700 dark:text-gray-300">Beskrivning:</span> <span className="text-gray-900 dark:text-white">{project.description}</span></div>
        )}
        <div><span className="font-medium text-gray-700 dark:text-gray-300">Status:</span> 
          <span className={`ml-2 px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(project.status)}`}>
            {getStatusDisplayName(project.status)}
          </span>
        </div>
        <div><span className="font-medium text-gray-700 dark:text-gray-300">Prioritet:</span> 
          <span className={`ml-2 px-3 py-1 rounded-full text-xs font-medium ${getPriorityColor(project.priority)}`}>
            {project.priority === 'high' ? 'Hög' : project.priority === 'medium' ? 'Medium' : 'Låg'}
          </span>
        </div>
        {project.customers && (
          <div className="flex items-center"><User className="w-4 h-4 mr-2 text-gray-500" /><span className="font-medium text-gray-700 dark:text-gray-300">Kund:</span> <span className="text-gray-900 dark:text-white ml-2">{project.customers.name}</span></div>
        )}
        {project.budget && (
          <div className="flex items-center"><DollarSign className="w-4 h-4 mr-2 text-gray-500" /><span className="font-medium text-gray-700 dark:text-gray-300">Budget:</span> <span className="text-gray-900 dark:text-white ml-2 font-medium">{formatCurrency(project.budget)}</span></div>
        )}
        {project.estimated_hours && (
          <div className="flex items-center"><Clock className="w-4 h-4 mr-2 text-gray-500" /><span className="font-medium text-gray-700 dark:text-gray-300">Uppskattade timmar:</span> <span className="text-gray-900 dark:text-white ml-2">{project.estimated_hours}h</span></div>
        )}
        {project.start_date && (
          <div className="flex items-center"><Calendar className="w-4 h-4 mr-2 text-gray-500" /><span className="font-medium text-gray-700 dark:text-gray-300">Startdatum:</span> <span className="text-gray-900 dark:text-white ml-2">{formatDate(project.start_date)}</span></div>
        )}
        {project.end_date && (
          <div className="flex items-center"><Calendar className="w-4 h-4 mr-2 text-gray-500" /><span className="font-medium text-gray-700 dark:text-gray-300">Slutdatum:</span> <span className="text-gray-900 dark:text-white ml-2">{formatDate(project.end_date)}</span></div>
        )}
        {project.notes && (
          <div><span className="font-medium text-gray-700 dark:text-gray-300">Anteckningar:</span> <span className="text-gray-900 dark:text-white">{project.notes}</span></div>
        )}
      </div>
      <div className="flex-1">
        <div className="text-lg font-medium text-gray-900 dark:text-white mb-4">Snabbåtgärder</div>
        <div className="space-y-3">
          <Link href={`/dashboard/projects/${project.id}`} className="block">
            <Button variant="outline" className="w-full justify-start">
              <FileText className="w-4 h-4 mr-2" />
              Visa detaljer
            </Button>
          </Link>
          <Link href={`/dashboard/projects/${project.id}/edit`} className="block">
            <Button variant="outline" className="w-full justify-start">
              <Edit className="w-4 h-4 mr-2" />
              Redigera projekt
            </Button>
          </Link>
          <Link href={`/dashboard/projects/${project.id}/timeline`} className="block">
            <Button variant="outline" className="w-full justify-start">
              <Calendar className="w-4 h-4 mr-2" />
              Hantera tidslinje
            </Button>
          </Link>
          <Link href={`/dashboard/projects/${project.id}/staff`} className="block">
            <Button variant="outline" className="w-full justify-start">
              <Users className="w-4 h-4 mr-2" />
              Hantera personal
            </Button>
          </Link>
        </div>
      </div>
    </div>
  );

  const renderProjectsContent = () => (
    <>
      <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
        <div>
          <h2 className="text-2xl md:text-3xl font-bold tracking-tight text-gray-900 dark:text-white">Projekt</h2>
          <p className="text-gray-500 dark:text-gray-400 mt-1">Hantera och spåra projektframsteg</p>
        </div>
        <div className="flex flex-wrap items-center gap-2">
          <Link href="/dashboard/projects/create">
            <Button className="bg-[#5D5FEF] hover:bg-[#4B4AEF]">
              <Plus className="h-4 w-4 mr-2" />
              Nytt projekt
            </Button>
          </Link>
        </div>
      </div>

      {/* Statistics Cards */}
      <StatsCards metrics={projectMetrics} />
      
      <div className="flex flex-col gap-4 md:flex-row">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400 dark:text-gray-500" />
          <Input type="search" placeholder="Sök projekt..." className="w-full pl-10 border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-800" value={searchQuery} onChange={(e) => setSearchQuery(e.target.value)} />
        </div>
      </div>
      
      {loading ? (
        <div className="flex justify-center p-8">
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      ) : filteredProjects.length > 0 ? (
        <ExpandableList
          data={filteredProjects}
          columns={projectColumns}
          renderCell={renderProjectCell}
          renderExpandedContent={renderProjectExpandedContent}
          getRowKey={(project) => project.id}
        />
      ) : (
        <div className="text-center py-12 bg-white dark:bg-gray-800 rounded-lg shadow">
          {searchQuery ? (
            <div>
              <Briefcase className="h-12 w-12 mx-auto text-gray-400 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">Inga matchande projekt</h3>
              <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
                Försök justera din sökning för att hitta det du letar efter.
              </p>
              <Button className="mt-4" onClick={() => setSearchQuery('')}>
                Rensa sökning
              </Button>
            </div>
          ) : (
            <div>
              <Briefcase className="h-12 w-12 mx-auto text-gray-400 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">Inga projekt ännu</h3>
              <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
                Kom igång genom att skapa ditt första projekt.
              </p>
              <Link href="/dashboard/projects/create">
                <Button className="mt-4">
                  <Plus className="h-4 w-4 mr-2" />
                  Nytt projekt
                </Button>
              </Link>
            </div>
          )}
        </div>
      )}
      
      {/* Delete Confirmation Modal */}
      {isDeleteModalOpen && projectToDelete && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
          <div className="bg-white dark:bg-gray-800 rounded-lg max-w-lg w-full p-6 shadow-xl">
            <div className="flex items-center mb-4">
              <div className="flex-shrink-0">
                <AlertTriangle className="h-6 w-6 text-red-500" />
              </div>
              <div className="ml-3">
                <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
                  Ta bort projekt
                </h3>
              </div>
            </div>
            
            <div className="mb-4">
              <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                Du håller på att ta bort <strong>{projectToDelete.name}</strong>.
              </p>
              
              {isCheckingRelated ? (
                <div className="flex items-center justify-center py-4">
                  <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-blue-500"></div>
                  <span className="ml-2 text-sm text-gray-600 dark:text-gray-400">
                    Kontrollerar relaterade poster...
                  </span>
                </div>
              ) : relatedRecords ? (
                <div className="space-y-3">
                  {(relatedRecords.timeline > 0 || relatedRecords.staff > 0 || 
                    relatedRecords.estimates > 0 || relatedRecords.invoices > 0) ? (
                    <>
                      <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-3">
                        <p className="text-sm font-medium text-red-800 dark:text-red-200 mb-2">
                          ⚠️ Detta kommer också att ta bort följande relaterade poster:
                        </p>
                        <div className="space-y-1">
                          {relatedRecords.timeline > 0 && (
                            <div className="flex items-center text-sm text-red-700 dark:text-red-300">
                              <Calendar className="h-4 w-4 mr-2" />
                              {relatedRecords.timeline} tidslinje{relatedRecords.timeline !== 1 ? 'r' : ''}
                            </div>
                          )}
                          {relatedRecords.staff > 0 && (
                            <div className="flex items-center text-sm text-red-700 dark:text-red-300">
                              <Users className="h-4 w-4 mr-2" />
                              {relatedRecords.staff} personal{relatedRecords.staff !== 1 ? 'er' : ''}
                            </div>
                          )}
                          {relatedRecords.estimates > 0 && (
                            <div className="flex items-center text-sm text-red-700 dark:text-red-300">
                              <FileText className="h-4 w-4 mr-2" />
                              {relatedRecords.estimates} offert{relatedRecords.estimates !== 1 ? 'er' : ''}
                            </div>
                          )}
                          {relatedRecords.invoices > 0 && (
                            <div className="flex items-center text-sm text-red-700 dark:text-red-300">
                              <DollarSign className="h-4 w-4 mr-2" />
                              {relatedRecords.invoices} faktura{relatedRecords.invoices !== 1 ? 'r' : ''}
                            </div>
                          )}
                        </div>
                      </div>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        <strong>Denna åtgärd kan inte ångras.</strong> All data kopplad till detta projekt kommer att tas bort permanent.
                      </p>
                    </>
                  ) : (
                    <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-3">
                      <p className="text-sm text-green-800 dark:text-green-200">
                        ✅ Inga relaterade poster hittades. Detta projekt kan tas bort säkert.
                      </p>
                    </div>
                  )}
                </div>
              ) : null}
            </div>
            
            <div className="flex justify-end space-x-3">
              <Button 
                variant="outline" 
                onClick={() => {
                  setIsDeleteModalOpen(false);
                  setProjectToDelete(null);
                  setRelatedRecords(null);
                }}
                disabled={isDeleting}
              >
                Avbryt
              </Button>
              <Button 
                variant="destructive" 
                onClick={handleDelete}
                disabled={isCheckingRelated || isDeleting}
                className="min-w-[100px]"
              >
                {isDeleting ? (
                  <div className="flex items-center">
                    <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2"></div>
                    Tar bort...
                  </div>
                ) : (
                  'Ta bort'
                )}
              </Button>
            </div>
          </div>
        </div>
      )}
    </>
  );

  return (
    <div className="p-4 md:p-6 lg:p-8 space-y-6 max-w-full overflow-hidden">
      {renderProjectsContent()}
    </div>
  );
} 