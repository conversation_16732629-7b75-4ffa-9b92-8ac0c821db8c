'use client'

import { useEffect, useState, useMemo } from 'react'
import { use<PERSON><PERSON>er, useParams } from 'next/navigation'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { 
  ArrowLeft, 
  Edit, 
  Trash2, 
  Calendar, 
  Users, 
  DollarSign, 
  Clock, 
  User, 
  MapPin, 
  FileText,
  Settings,
  BarChart3,
  Plus,
  Search,
  Filter,
  CheckCircle,
  AlertCircle,
  PlayCircle,
  PauseCircle,
  MoreVertical,
  Target,
  Briefcase,
  Phone,
  Mail,
  Star,
  Award
} from 'lucide-react'
import Link from 'next/link'
import { supabase } from '@/lib/supabase/supabase'
import { toast } from 'sonner'

interface Project {
  id: string
  name: string
  description?: string
  status: 'planning' | 'active' | 'on-hold' | 'completed' | 'cancelled'
  start_date?: string
  end_date?: string
  customer_id?: string
  budget?: number
  estimated_hours?: number
  notes?: string
  priority: 'low' | 'medium' | 'high'
  created_at: string
  updated_at: string
  customers?: {
    name: string
    company_name?: string
    email?: string
    phone?: string
    address?: string
  }
}

// Mock data for timeline tasks
const mockTasks = [
  {
    id: '1',
    title: 'Projektinitiering',
    assignee: 'Erik Andersson',
    status: 'completed',
    priority: 'high',
    startDate: '2024-01-15',
    endDate: '2024-01-20',
    progress: 100,
    duration: '5 dagar',
    dependencies: [],
    description: 'Första fas av projektet med kravinsamling och planering'
  },
  // ... other tasks
]

// Mock data for staff
const mockStaff = [
  {
    id: '1',
    name: 'Erik Andersson',
    role: 'Projektledare',
    email: '<EMAIL>',
    phone: '+46 70 123 45 67',
    avatar: '/avatars/erik.jpg',
    skills: ['Projektledning', 'Agile', 'Scrum'],
    availability: 'Tillgänglig',
    hourlyRate: 850,
    totalHours: 120,
    activeTasksCount: 3
  },
  // ... other staff
]

export default function ProjectDetailPage() {
  const router = useRouter()
  const params = useParams()
  const projectId = params.id as string

  const [project, setProject] = useState<Project | null>(null)
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState<'overview' | 'timeline' | 'staff' | 'reports' | 'settings'>('overview')
  
  // Timeline tab states
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('all')
  const [priorityFilter, setPriorityFilter] = useState('all')
  const [timeRange, setTimeRange] = useState('month')
  const [viewMode, setViewMode] = useState<'gantt' | 'list'>('gantt')
  
  // Staff tab states  
  const [staffSearchTerm, setStaffSearchTerm] = useState('')
  const [roleFilter, setRoleFilter] = useState('all')
  const [availabilityFilter, setAvailabilityFilter] = useState('all')

  useEffect(() => {
    if (projectId) {
      fetchProject()
    }
  }, [projectId])

  const fetchProject = async () => {
    try {
      setLoading(true)
      const { data, error } = await supabase
        .from('projects')
        .select(`
          *,
          customers (
            name,
            company_name,
            email,
            phone,
            address
          )
        `)
        .eq('id', projectId)
        .single()

      if (error) {
        throw error
      }

      setProject(data)
    } catch (error: any) {
      console.error('Error fetching project:', error.message)
      toast.error('Kunde inte hämta projekt')
      router.push('/dashboard/projects')
    } finally {
      setLoading(false)
    }
  }

  // Helper functions
  const getStatusDisplayName = (status: string) => {
    switch (status) {
      case 'planning': return 'Planering'
      case 'active': return 'Aktiv'
      case 'on-hold': return 'Pausad'
      case 'completed': return 'Klar'
      case 'cancelled': return 'Avbruten'
      default: return status
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': 
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
      case 'planning':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
      case 'on-hold':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
      case 'completed':
        return 'bg-emerald-100 text-emerald-800 dark:bg-emerald-900 dark:text-emerald-200'
      case 'cancelled':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
      case 'low':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('sv-SE', { style: 'currency', currency: 'SEK' }).format(amount);
  }

  const formatDate = (dateString: string) => {
    if (!dateString) return ''
    const date = new Date(dateString)
    return date.toLocaleDateString('sv-SE')
  }

  // Timeline filtering
  const filteredTasks = useMemo(() => {
    return mockTasks.filter(task => {
      const matchesSearch = task.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           task.assignee.toLowerCase().includes(searchTerm.toLowerCase())
      const matchesStatus = statusFilter === 'all' || task.status === statusFilter
      const matchesPriority = priorityFilter === 'all' || task.priority === priorityFilter
      
      return matchesSearch && matchesStatus && matchesPriority
    })
  }, [searchTerm, statusFilter, priorityFilter])

  // Staff filtering
  const filteredStaff = useMemo(() => {
    return mockStaff.filter(member => {
      const matchesSearch = member.name.toLowerCase().includes(staffSearchTerm.toLowerCase()) ||
                           member.email.toLowerCase().includes(staffSearchTerm.toLowerCase()) ||
                           member.skills.some(skill => skill.toLowerCase().includes(staffSearchTerm.toLowerCase()))
      const matchesRole = roleFilter === 'all' || member.role === roleFilter
      const matchesAvailability = availabilityFilter === 'all' || 
                                 (availabilityFilter === 'available' && member.availability === 'Tillgänglig') ||
                                 (availabilityFilter === 'busy' && member.availability !== 'Tillgänglig')
      
      return matchesSearch && matchesRole && matchesAvailability
    })
  }, [staffSearchTerm, roleFilter, availabilityFilter])

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
          </div>
        </div>
      </div>
    )
  }

  if (!project) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Projekt hittades inte</h1>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center space-x-4">
            <Link href="/dashboard/projects">
              <Button variant="outline" size="sm">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Tillbaka
              </Button>
            </Link>
            <div>
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                {project.name}
              </h1>
              <p className="text-gray-500 dark:text-gray-400">
                {project.description || 'Ingen beskrivning tillgänglig'}
              </p>
            </div>
          </div>
          <div className="flex items-center space-x-3">
            <Link href={`/dashboard/projects/${project.id}/edit`}>
              <Button variant="outline">
                <Edit className="w-4 h-4 mr-2" />
                Redigera
              </Button>
            </Link>
            <Button>
              <Plus className="w-4 h-4 mr-2" />
              Ny uppgift
            </Button>
          </div>
        </div>

        {/* Tabbed Interface */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
          {/* Tab Headers */}
          <div className="border-b border-gray-200">
            <nav className="flex space-x-8 px-6" aria-label="Tabs">
              <button
                onClick={() => setActiveTab('overview')}
                className={`py-4 px-1 border-b-2 font-medium text-sm whitespace-nowrap transition-colors duration-200 ${
                  activeTab === 'overview'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <FileText className="w-4 h-4 inline mr-2" />
                Översikt
              </button>
              <button
                onClick={() => setActiveTab('timeline')}
                className={`py-4 px-1 border-b-2 font-medium text-sm whitespace-nowrap transition-colors duration-200 ${
                  activeTab === 'timeline'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <Calendar className="w-4 h-4 inline mr-2" />
                Tidslinje
              </button>
              <button
                onClick={() => setActiveTab('staff')}
                className={`py-4 px-1 border-b-2 font-medium text-sm whitespace-nowrap transition-colors duration-200 ${
                  activeTab === 'staff'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <Users className="w-4 h-4 inline mr-2" />
                Personal
              </button>
              <button
                onClick={() => setActiveTab('reports')}
                className={`py-4 px-1 border-b-2 font-medium text-sm whitespace-nowrap transition-colors duration-200 ${
                  activeTab === 'reports'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <BarChart3 className="w-4 h-4 inline mr-2" />
                Rapporter
              </button>
              <button
                onClick={() => setActiveTab('settings')}
                className={`py-4 px-1 border-b-2 font-medium text-sm whitespace-nowrap transition-colors duration-200 ${
                  activeTab === 'settings'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <Settings className="w-4 h-4 inline mr-2" />
                Inställningar
              </button>
            </nav>
          </div>

          {/* Tab Content */}
          <div className="p-6">
            {/* Overview Tab */}
            {activeTab === 'overview' && (
              <div>
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                  {/* Project Information */}
                  <div className="lg:col-span-2 space-y-6">
                    <Card>
                      <CardHeader>
                        <CardTitle className="text-lg font-semibold text-gray-900 dark:text-white">
                          Projektinformation
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <label className="text-sm font-medium text-gray-500 dark:text-gray-400">Status</label>
                            <div className="mt-1">
                              <span className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(project.status)}`}>
                                {getStatusDisplayName(project.status)}
                              </span>
                            </div>
                          </div>
                          <div>
                            <label className="text-sm font-medium text-gray-500 dark:text-gray-400">Prioritet</label>
                            <div className="mt-1">
                              <span className={`px-3 py-1 rounded-full text-xs font-medium ${getPriorityColor(project.priority)}`}>
                                {project.priority === 'high' ? 'Hög' : project.priority === 'medium' ? 'Medium' : 'Låg'}
                              </span>
                            </div>
                          </div>
                        </div>

                        {project.customers && (
                          <div>
                            <label className="text-sm font-medium text-gray-500 dark:text-gray-400">Kund</label>
                            <div className="mt-1">
                              <div className="flex items-center">
                                <User className="w-4 h-4 mr-2 text-gray-400" />
                                <div>
                                  <p className="text-gray-900 dark:text-white font-medium">{project.customers.name}</p>
                                  {project.customers.company_name && (
                                    <p className="text-sm text-gray-500 dark:text-gray-400">{project.customers.company_name}</p>
                                  )}
                                </div>
                              </div>
                            </div>
                          </div>
                        )}

                        {(project.budget || project.estimated_hours) && (
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            {project.budget && (
                              <div>
                                <label className="text-sm font-medium text-gray-500 dark:text-gray-400">Budget</label>
                                <div className="mt-1 flex items-center">
                                  <DollarSign className="w-4 h-4 mr-2 text-gray-400" />
                                  <span className="text-gray-900 dark:text-white font-medium">
                                    {formatCurrency(project.budget)}
                                  </span>
                                </div>
                              </div>
                            )}
                            {project.estimated_hours && (
                              <div>
                                <label className="text-sm font-medium text-gray-500 dark:text-gray-400">Uppskattade timmar</label>
                                <div className="mt-1 flex items-center">
                                  <Clock className="w-4 h-4 mr-2 text-gray-400" />
                                  <span className="text-gray-900 dark:text-white">{project.estimated_hours}h</span>
                                </div>
                              </div>
                            )}
                          </div>
                        )}

                        {(project.start_date || project.end_date) && (
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            {project.start_date && (
                              <div>
                                <label className="text-sm font-medium text-gray-500 dark:text-gray-400">Startdatum</label>
                                <div className="mt-1 flex items-center">
                                  <Calendar className="w-4 h-4 mr-2 text-gray-400" />
                                  <span className="text-gray-900 dark:text-white">{formatDate(project.start_date)}</span>
                                </div>
                              </div>
                            )}
                            {project.end_date && (
                              <div>
                                <label className="text-sm font-medium text-gray-500 dark:text-gray-400">Slutdatum</label>
                                <div className="mt-1 flex items-center">
                                  <Calendar className="w-4 h-4 mr-2 text-gray-400" />
                                  <span className="text-gray-900 dark:text-white">{formatDate(project.end_date)}</span>
                                </div>
                              </div>
                            )}
                          </div>
                        )}

                        {project.notes && (
                          <div>
                            <label className="text-sm font-medium text-gray-500 dark:text-gray-400">Anteckningar</label>
                            <div className="mt-1">
                              <p className="text-gray-900 dark:text-white">{project.notes}</p>
                            </div>
                          </div>
                        )}
                      </CardContent>
                    </Card>

                    {/* Contact Information */}
                    {project.customers && (
                      <Card>
                        <CardHeader>
                          <CardTitle className="text-lg font-semibold text-gray-900 dark:text-white">
                            Kontaktinformation
                          </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                          {project.customers.email && (
                            <div>
                              <label className="text-sm font-medium text-gray-500 dark:text-gray-400">E-post</label>
                              <p className="mt-1 text-gray-900 dark:text-white">{project.customers.email}</p>
                            </div>
                          )}
                          {project.customers.phone && (
                            <div>
                              <label className="text-sm font-medium text-gray-500 dark:text-gray-400">Telefon</label>
                              <p className="mt-1 text-gray-900 dark:text-white">{project.customers.phone}</p>
                            </div>
                          )}
                          {project.customers.address && (
                            <div>
                              <label className="text-sm font-medium text-gray-500 dark:text-gray-400">Adress</label>
                              <div className="mt-1 flex items-start">
                                <MapPin className="w-4 h-4 mr-2 text-gray-400 mt-1" />
                                <p className="text-gray-900 dark:text-white">{project.customers.address}</p>
                              </div>
                            </div>
                          )}
                        </CardContent>
                      </Card>
                    )}
                  </div>

                  {/* Quick Actions */}
                  <div className="space-y-6">
                    <Card>
                      <CardHeader>
                        <CardTitle className="text-lg font-semibold text-gray-900 dark:text-white">
                          Snabbåtgärder
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-3">
                        <Link href={`/dashboard/projects/${project.id}/edit`} className="block">
                          <Button variant="outline" className="w-full justify-start">
                            <Edit className="w-4 h-4 mr-2" />
                            Redigera projekt
                          </Button>
                        </Link>
                        <Button 
                          variant="outline" 
                          className="w-full justify-start"
                          onClick={() => setActiveTab('timeline')}
                        >
                          <Calendar className="w-4 h-4 mr-2" />
                          Hantera tidslinje
                        </Button>
                        <Button 
                          variant="outline" 
                          className="w-full justify-start"
                          onClick={() => setActiveTab('staff')}
                        >
                          <Users className="w-4 h-4 mr-2" />
                          Hantera personal
                        </Button>
                        <Link href={`/dashboard/estimates?project=${project.name}`} className="block">
                          <Button variant="outline" className="w-full justify-start">
                            <FileText className="w-4 h-4 mr-2" />
                            Relaterade offerter
                          </Button>
                        </Link>
                      </CardContent>
                    </Card>

                    {/* Project Details */}
                    <Card>
                      <CardHeader>
                        <CardTitle className="text-lg font-semibold text-gray-900 dark:text-white">
                          Projektdetaljer
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-3">
                        <div>
                          <label className="text-sm font-medium text-gray-500 dark:text-gray-400">Skapat</label>
                          <p className="mt-1 text-gray-900 dark:text-white">{formatDate(project.created_at)}</p>
                        </div>
                        <div>
                          <label className="text-sm font-medium text-gray-500 dark:text-gray-400">Senast uppdaterat</label>
                          <p className="mt-1 text-gray-900 dark:text-white">{formatDate(project.updated_at)}</p>
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                </div>
              </div>
            )}

            {/* Timeline Tab */}
            {activeTab === 'timeline' && (
              <div className="space-y-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-medium text-gray-900">Projekttidslinje</h3>
                  <div className="flex items-center space-x-3">
                    <Button>
                      <Plus className="w-4 h-4 mr-2" />
                      Ny uppgift
                    </Button>
                  </div>
                </div>

                {/* Filters */}
                <div className="flex items-center justify-between gap-4 p-4 bg-gray-50 rounded-lg border">
                  <div className="flex items-center space-x-4 flex-1">
                    <div className="relative flex-1 max-w-md">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                      <Input
                        placeholder="Sök uppgifter eller team members..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="pl-10"
                      />
                    </div>
                    
                    <Select value={statusFilter} onValueChange={setStatusFilter}>
                      <SelectTrigger className="w-[150px]">
                        <SelectValue placeholder="Status" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">Alla status</SelectItem>
                        <SelectItem value="completed">Klar</SelectItem>
                        <SelectItem value="in-progress">Pågående</SelectItem>
                        <SelectItem value="pending">Väntande</SelectItem>
                        <SelectItem value="blocked">Blockerad</SelectItem>
                      </SelectContent>
                    </Select>
                    
                    <Select value={priorityFilter} onValueChange={setPriorityFilter}>
                      <SelectTrigger className="w-[150px]">
                        <SelectValue placeholder="Prioritet" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">Alla prioriteter</SelectItem>
                        <SelectItem value="high">Hög</SelectItem>
                        <SelectItem value="medium">Medium</SelectItem>
                        <SelectItem value="low">Låg</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <Select value={timeRange} onValueChange={setTimeRange}>
                      <SelectTrigger className="w-[120px]">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="week">Vecka</SelectItem>
                        <SelectItem value="month">Månad</SelectItem>
                        <SelectItem value="quarter">Kvartal</SelectItem>
                      </SelectContent>
                    </Select>
                    
                    <div className="flex rounded-lg border bg-white">
                      <button
                        onClick={() => setViewMode('gantt')}
                        className={`px-3 py-2 text-sm font-medium rounded-l-lg transition-colors ${
                          viewMode === 'gantt'
                            ? 'bg-blue-500 text-white'
                            : 'text-gray-700 hover:bg-gray-50'
                        }`}
                      >
                        Gantt
                      </button>
                      <button
                        onClick={() => setViewMode('list')}
                        className={`px-3 py-2 text-sm font-medium rounded-r-lg transition-colors ${
                          viewMode === 'list'
                            ? 'bg-blue-500 text-white'
                            : 'text-gray-700 hover:bg-gray-50'
                        }`}
                      >
                        Lista
                      </button>
                    </div>
                  </div>
                </div>

                {/* Content based on view mode */}
                {viewMode === 'gantt' ? (
                  <Card>
                    <CardContent className="p-6">
                      <div className="space-y-4">
                        <div className="text-center py-8">
                          <Calendar className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                          <h3 className="text-lg font-medium text-gray-900 mb-2">Gantt-schema</h3>
                          <p className="text-gray-500">Visar projektets tidslinje och uppgifter i kronologisk ordning.</p>
                        </div>
                        
                        {/* Task list with timeline bars */}
                        <div className="space-y-3">
                          {filteredTasks.map((task) => (
                            <div key={task.id} className="border rounded-lg p-4 hover:bg-gray-50 transition-colors">
                              <div className="flex items-center justify-between">
                                <div className="flex items-center space-x-3">
                                  <div className={`w-3 h-3 rounded-full ${
                                    task.status === 'completed' ? 'bg-green-500' :
                                    task.status === 'in-progress' ? 'bg-blue-500' :
                                    task.status === 'pending' ? 'bg-yellow-500' : 'bg-red-500'
                                  }`} />
                                  <div>
                                    <h4 className="font-medium text-gray-900">{task.title}</h4>
                                    <p className="text-sm text-gray-500">{task.assignee} • {task.duration}</p>
                                  </div>
                                </div>
                                <div className="flex items-center space-x-3">
                                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                                    task.priority === 'high' ? 'bg-red-100 text-red-800' :
                                    task.priority === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                                    'bg-green-100 text-green-800'
                                  }`}>
                                    {task.priority === 'high' ? 'Hög' : task.priority === 'medium' ? 'Medium' : 'Låg'}
                                  </span>
                                  <div className="w-24 h-2 bg-gray-200 rounded-full">
                                    <div 
                                      className="h-2 bg-blue-500 rounded-full"
                                      style={{ width: `${task.progress}%` }}
                                    />
                                  </div>
                                  <span className="text-sm text-gray-500">{task.progress}%</span>
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ) : (
                  <Card>
                    <CardContent className="p-6">
                      <div className="space-y-4">
                        {filteredTasks.map((task) => (
                          <div key={task.id} className="border rounded-lg p-4 hover:bg-gray-50 transition-colors">
                            <div className="flex items-start justify-between">
                              <div className="flex-1">
                                <div className="flex items-center space-x-3 mb-2">
                                  <h4 className="font-medium text-gray-900">{task.title}</h4>
                                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                                    task.status === 'completed' ? 'bg-green-100 text-green-800' :
                                    task.status === 'in-progress' ? 'bg-blue-100 text-blue-800' :
                                    task.status === 'pending' ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800'
                                  }`}>
                                    {task.status === 'completed' ? 'Klar' :
                                     task.status === 'in-progress' ? 'Pågående' :
                                     task.status === 'pending' ? 'Väntande' : 'Blockerad'}
                                  </span>
                                </div>
                                <p className="text-gray-600 mb-3">{task.description}</p>
                                <div className="flex items-center space-x-4 text-sm text-gray-500">
                                  <div className="flex items-center">
                                    <User className="w-4 h-4 mr-1" />
                                    {task.assignee}
                                  </div>
                                  <div className="flex items-center">
                                    <Calendar className="w-4 h-4 mr-1" />
                                    {task.startDate} - {task.endDate}
                                  </div>
                                  <div className="flex items-center">
                                    <Clock className="w-4 h-4 mr-1" />
                                    {task.duration}
                                  </div>
                                </div>
                              </div>
                              <div className="flex items-center space-x-3">
                                <div className="text-right">
                                  <div className="w-16 h-2 bg-gray-200 rounded-full mb-1">
                                    <div 
                                      className="h-2 bg-blue-500 rounded-full"
                                      style={{ width: `${task.progress}%` }}
                                    />
                                  </div>
                                  <span className="text-xs text-gray-500">{task.progress}%</span>
                                </div>
                                <Button variant="ghost" size="sm">
                                  <MoreVertical className="w-4 h-4" />
                                </Button>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                )}
              </div>
            )}

            {/* Staff Tab */}
            {activeTab === 'staff' && (
              <div className="space-y-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-medium text-gray-900">Projektteam</h3>
                  <Button>
                    <Plus className="w-4 h-4 mr-2" />
                    Lägg till medlem
                  </Button>
                </div>

                {/* Team Statistics */}
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <Card>
                    <CardContent className="p-4">
                      <div className="flex items-center">
                        <Users className="h-5 w-5 text-blue-500 mr-2" />
                        <div>
                          <p className="text-sm font-medium text-gray-600">Teamstorlek</p>
                          <p className="text-xl font-bold text-gray-900">{mockStaff.length}</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardContent className="p-4">
                      <div className="flex items-center">
                        <Clock className="h-5 w-5 text-green-500 mr-2" />
                        <div>
                          <p className="text-sm font-medium text-gray-600">Totala timmar</p>
                          <p className="text-xl font-bold text-gray-900">
                            {mockStaff.reduce((sum, member) => sum + member.totalHours, 0)}h
                          </p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardContent className="p-4">
                      <div className="flex items-center">
                        <Target className="h-5 w-5 text-purple-500 mr-2" />
                        <div>
                          <p className="text-sm font-medium text-gray-600">Snitt tillgänglighet</p>
                          <p className="text-xl font-bold text-gray-900">87%</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardContent className="p-4">
                      <div className="flex items-center">
                        <Briefcase className="h-5 w-5 text-orange-500 mr-2" />
                        <div>
                          <p className="text-sm font-medium text-gray-600">Aktiva uppgifter</p>
                          <p className="text-xl font-bold text-gray-900">
                            {mockStaff.reduce((sum, member) => sum + member.activeTasksCount, 0)}
                          </p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>

                {/* Filters */}
                <div className="flex items-center justify-between gap-4 p-4 bg-gray-50 rounded-lg border">
                  <div className="flex items-center space-x-4 flex-1">
                    <div className="relative flex-1 max-w-md">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                      <Input
                        placeholder="Sök efter namn, e-post eller kompetens..."
                        value={staffSearchTerm}
                        onChange={(e) => setStaffSearchTerm(e.target.value)}
                        className="pl-10"
                      />
                    </div>
                    
                    <Select value={roleFilter} onValueChange={setRoleFilter}>
                      <SelectTrigger className="w-[180px]">
                        <SelectValue placeholder="Roll" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">Alla roller</SelectItem>
                        <SelectItem value="Projektledare">Projektledare</SelectItem>
                        <SelectItem value="Utvecklare">Utvecklare</SelectItem>
                        <SelectItem value="Designer">Designer</SelectItem>
                        <SelectItem value="Testare">Testare</SelectItem>
                      </SelectContent>
                    </Select>
                    
                    <Select value={availabilityFilter} onValueChange={setAvailabilityFilter}>
                      <SelectTrigger className="w-[150px]">
                        <SelectValue placeholder="Tillgänglighet" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">Alla</SelectItem>
                        <SelectItem value="available">Tillgänglig</SelectItem>
                        <SelectItem value="busy">Upptagen</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                {/* Team Members */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {filteredStaff.map((member) => (
                    <Card key={member.id}>
                      <CardContent className="p-4">
                        <div className="flex items-start space-x-3">
                          <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                            <User className="w-6 h-6 text-blue-600" />
                          </div>
                          <div className="flex-1">
                            <h4 className="font-medium text-gray-900">{member.name}</h4>
                            <p className="text-sm text-blue-600 font-medium">{member.role}</p>
                            <div className="mt-2 space-y-1">
                              <div className="flex items-center text-sm text-gray-500">
                                <Mail className="w-3 h-3 mr-1" />
                                {member.email}
                              </div>
                              <div className="flex items-center text-sm text-gray-500">
                                <Phone className="w-3 h-3 mr-1" />
                                {member.phone}
                              </div>
                            </div>
                            <div className="mt-3">
                              <div className="flex flex-wrap gap-1">
                                {member.skills.slice(0, 2).map((skill, index) => (
                                  <span key={index} className="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded">
                                    {skill}
                                  </span>
                                ))}
                                {member.skills.length > 2 && (
                                  <span className="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded">
                                    +{member.skills.length - 2}
                                  </span>
                                )}
                              </div>
                            </div>
                            <div className="mt-3 flex items-center justify-between text-sm">
                              <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                                member.availability === 'Tillgänglig' 
                                  ? 'bg-green-100 text-green-800' 
                                  : 'bg-red-100 text-red-800'
                              }`}>
                                {member.availability}
                              </span>
                              <div className="text-right">
                                <p className="font-medium">{member.hourlyRate} kr/h</p>
                                <p className="text-gray-500">{member.totalHours}h total</p>
                              </div>
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>
            )}

            {/* Reports Tab */}
            {activeTab === 'reports' && (
              <div>
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-lg font-medium text-gray-900">Projektrapporter</h3>
                  <Button>
                    <FileText className="w-4 h-4 mr-2" />
                    Generera rapport
                  </Button>
                </div>
                
                <div className="text-center py-8">
                  <BarChart3 className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">Rapporter kommer snart</h3>
                  <p className="text-gray-500">Projektrapporter och analyser är under utveckling.</p>
                </div>
              </div>
            )}

            {/* Settings Tab */}
            {activeTab === 'settings' && (
              <div>
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-lg font-medium text-gray-900">Projektinställningar</h3>
                </div>
                
                <div className="text-center py-8">
                  <Settings className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">Inställningar kommer snart</h3>
                  <p className="text-gray-500">Projektinställningar är under utveckling.</p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
} 