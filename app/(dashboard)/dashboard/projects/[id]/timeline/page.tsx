'use client'

import { useEffect, useState, useMemo } from 'react'
import { useRouter, useParams } from 'next/navigation'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { 
  ArrowLeft, 
  Plus, 
  Calendar, 
  Search, 
  Filter, 
  Clock,
  CheckCircle,
  AlertCircle,
  PlayCircle,
  PauseCircle,
  MoreVertical,
  Edit,
  Trash2,
  Users,
  Target,
  Settings,
  FileText,
  BarChart3
} from 'lucide-react'
import Link from 'next/link'
import { supabase } from '@/lib/supabase/supabase'
import { toast } from 'sonner'

interface Project {
  id: string
  name: string
  description?: string
  status: 'planning' | 'active' | 'on-hold' | 'completed' | 'cancelled'
  start_date?: string
  end_date?: string
  customer_id?: string
  budget?: number
  estimated_hours?: number
  customers?: {
    name: string
    company_name?: string
  }
}

interface Task {
  id: string
  name: string
  description?: string
  status: 'todo' | 'in-progress' | 'completed' | 'blocked'
  priority: 'low' | 'medium' | 'high'
  start_date: string
  end_date: string
  assignee?: string
  progress: number
  dependencies?: string[]
  estimated_hours?: number
  actual_hours?: number
}

interface TeamMember {
  id: string
  name: string
  email: string
  role: string
  hourly_rate: number
  avatar?: string
  availability: 'available' | 'busy' | 'unavailable'
  skills: string[]
  phone?: string
}

export default function ProjectManagementPage() {
  const router = useRouter()
  const params = useParams()
  const projectId = params.id as string
  
  const [loading, setLoading] = useState(true)
  const [tabLoading, setTabLoading] = useState(false)
  const [project, setProject] = useState<Project | null>(null)
  const [tasks, setTasks] = useState<Task[]>([])
  const [teamMembers, setTeamMembers] = useState<TeamMember[]>([])
  
  // Active tab state
  const [activeTab, setActiveTab] = useState<'timeline' | 'staff' | 'settings' | 'reports'>('timeline')
  
  // Filter and view states
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('all')
  const [priorityFilter, setPriorityFilter] = useState('all')
  const [viewMode, setViewMode] = useState<'gantt' | 'list'>('gantt')
  const [timeRange, setTimeRange] = useState<'week' | 'month' | 'quarter'>('month')

  // Mock tasks data
  const mockTasks: Task[] = [
    {
      id: '1',
      name: 'Projektplanering',
      description: 'Initial projektplanering och kravanalys',
      status: 'completed',
      priority: 'high',
      start_date: '2025-01-01',
      end_date: '2025-01-07',
      assignee: 'Youssef Mekidiche',
      progress: 100,
      estimated_hours: 20,
      actual_hours: 18
    },
    {
      id: '2',
      name: 'Design och arkitektur',
      description: 'Systemdesign och teknisk arkitektur',
      status: 'completed',
      priority: 'high',
      start_date: '2025-01-08',
      end_date: '2025-01-21',
      assignee: 'Lisa Andersson',
      progress: 100,
      estimated_hours: 40,
      actual_hours: 42,
      dependencies: ['1']
    },
    {
      id: '3',
      name: 'Frontend utveckling',
      description: 'React komponentutveckling',
      status: 'in-progress',
      priority: 'high',
      start_date: '2025-01-22',
      end_date: '2025-02-15',
      assignee: 'Erik Johansson',
      progress: 65,
      estimated_hours: 80,
      actual_hours: 50,
      dependencies: ['2']
    },
    {
      id: '4',
      name: 'Backend API',
      description: 'REST API utveckling',
      status: 'in-progress',
      priority: 'high',
      start_date: '2025-01-22',
      end_date: '2025-02-20',
      assignee: 'Anna Petersson',
      progress: 45,
      estimated_hours: 60,
      actual_hours: 30,
      dependencies: ['2']
    },
    {
      id: '5',
      name: 'Databasimplementering',
      description: 'Databas schema och migrationer',
      status: 'todo',
      priority: 'medium',
      start_date: '2025-02-16',
      end_date: '2025-02-28',
      assignee: 'Marcus Lindberg',
      progress: 0,
      estimated_hours: 30,
      dependencies: ['3', '4']
    },
    {
      id: '6',
      name: 'Testning',
      description: 'Automatiserade tester och kvalitetssäkring',
      status: 'todo',
      priority: 'medium',
      start_date: '2025-03-01',
      end_date: '2025-03-15',
      assignee: 'Sara Nilsson',
      progress: 0,
      estimated_hours: 40,
      dependencies: ['5']
    },
    {
      id: '7',
      name: 'Deployment',
      description: 'Produktionsrelease',
      status: 'todo',
      priority: 'high',
      start_date: '2025-03-16',
      end_date: '2025-03-20',
      assignee: 'Youssef Mekidiche',
      progress: 0,
      estimated_hours: 16,
      dependencies: ['6']
    }
  ]

  // Mock team members data
  const mockTeamMembers: TeamMember[] = [
    {
      id: '1',
      name: 'Youssef Mekidiche',
      email: '<EMAIL>',
      role: 'Projektledare',
      hourly_rate: 850,
      avatar: '/avatars/youssef.jpg',
      availability: 'available',
      skills: ['Projektledning', 'React', 'TypeScript', 'UI/UX'],
      phone: '+46 70 774 22 16'
    },
    {
      id: '2',
      name: 'Lisa Andersson',
      email: '<EMAIL>',
      role: 'Senior Designer',
      hourly_rate: 750,
      avatar: '/avatars/lisa.jpg',
      availability: 'busy',
      skills: ['UI/UX Design', 'Figma', 'Prototyping', 'Design Systems'],
      phone: '+46 70 123 45 67'
    },
    {
      id: '3',
      name: 'Erik Johansson',
      email: '<EMAIL>',
      role: 'Frontend Utvecklare',
      hourly_rate: 700,
      avatar: '/avatars/erik.jpg',
      availability: 'available',
      skills: ['React', 'Vue.js', 'JavaScript', 'CSS'],
      phone: '+46 70 234 56 78'
    },
    {
      id: '4',
      name: 'Anna Petersson',
      email: '<EMAIL>',
      role: 'Backend Utvecklare',
      hourly_rate: 720,
      avatar: '/avatars/anna.jpg',
      availability: 'available',
      skills: ['Node.js', 'Python', 'Database Design', 'API Development'],
      phone: '+46 70 345 67 89'
    }
  ]

  useEffect(() => {
    if (projectId) {
      fetchProject()
      setTasks(mockTasks)
      setTeamMembers(mockTeamMembers)
      setLoading(false)
    }
  }, [projectId])

  useEffect(() => {
    if (activeTab !== 'timeline') {
      fetchTabData(activeTab)
    }
  }, [activeTab])

  const fetchProject = async () => {
    try {
      const { data, error } = await supabase
        .from('projects')
        .select(`
          *,
          customers (
            name,
            company_name
          )
        `)
        .eq('id', projectId)
        .single()

      if (error) {
        console.error('Error fetching project:', error)
        toast.error('Kunde inte hämta projekt')
        return
      }

      setProject(data)
    } catch (error) {
      console.error('Error:', error)
      toast.error('Fel vid hämtning av projekt')
    }
  }

  const fetchTabData = async (tab: string) => {
    setTabLoading(true)
    try {
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 500))
      
      if (tab === 'staff') {
        // In a real app, fetch team members from database
        setTeamMembers(mockTeamMembers)
      }
      // Add other tab data fetching logic here
    } catch (error) {
      console.error('Error fetching tab data:', error)
      toast.error('Error loading data')
    } finally {
      setTabLoading(false)
    }
  }

  // Filter tasks based on search and filters
  const filteredTasks = useMemo(() => {
    return tasks.filter(task => {
      const matchesSearch = task.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        task.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        task.assignee?.toLowerCase().includes(searchTerm.toLowerCase())
      
      const matchesStatus = statusFilter === 'all' || task.status === statusFilter
      const matchesPriority = priorityFilter === 'all' || task.priority === priorityFilter
      
      return matchesSearch && matchesStatus && matchesPriority
    })
  }, [tasks, searchTerm, statusFilter, priorityFilter])

  const getStatusIcon = (status: Task['status']) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="w-4 h-4 text-green-600" />
      case 'in-progress':
        return <PlayCircle className="w-4 h-4 text-blue-600" />
      case 'blocked':
        return <AlertCircle className="w-4 h-4 text-red-600" />
      default:
        return <PauseCircle className="w-4 h-4 text-gray-500" />
    }
  }

  const getStatusColor = (status: Task['status']) => {
    switch (status) {
      case 'completed':
        return 'bg-green-50 text-green-700 border-green-200'
      case 'in-progress':
        return 'bg-blue-50 text-blue-700 border-blue-200'
      case 'blocked':
        return 'bg-red-50 text-red-700 border-red-200'
      default:
        return 'bg-gray-50 text-gray-700 border-gray-200'
    }
  }

  const getPriorityColor = (priority: Task['priority']) => {
    switch (priority) {
      case 'high':
        return 'bg-red-50 text-red-700 border-red-200'
      case 'medium':
        return 'bg-amber-50 text-amber-700 border-amber-200'
      default:
        return 'bg-green-50 text-green-700 border-green-200'
    }
  }

  const getStatusDisplayName = (status: Task['status']) => {
    switch (status) {
      case 'todo':
        return 'Att göra'
      case 'in-progress':
        return 'Pågående'
      case 'completed':
        return 'Klar'
      case 'blocked':
        return 'Blockerad'
      default:
        return status
    }
  }

  const getPriorityDisplayName = (priority: Task['priority']) => {
    switch (priority) {
      case 'high':
        return 'Hög'
      case 'medium':
        return 'Medium'
      case 'low':
        return 'Låg'
      default:
        return priority
    }
  }

  // Generate timeline for Gantt view
  const generateTimeline = () => {
    if (filteredTasks.length === 0) return []
    
    const startDate = new Date(Math.min(...filteredTasks.map(task => new Date(task.start_date).getTime())))
    const endDate = new Date(Math.max(...filteredTasks.map(task => new Date(task.end_date).getTime())))
    
    const timeline = []
    const current = new Date(startDate)
    
    while (current <= endDate) {
      timeline.push(new Date(current))
      current.setDate(current.getDate() + (timeRange === 'week' ? 1 : timeRange === 'month' ? 7 : 30))
    }
    
    return timeline
  }

  const calculateTaskPosition = (task: Task, timeline: Date[]) => {
    if (timeline.length === 0) return { left: 0, width: 0 }
    
    const taskStart = new Date(task.start_date)
    const taskEnd = new Date(task.end_date)
    const timelineStart = timeline[0]
    const timelineEnd = timeline[timeline.length - 1]
    
    const totalDays = (timelineEnd.getTime() - timelineStart.getTime()) / (1000 * 60 * 60 * 24)
    const taskStartDays = (taskStart.getTime() - timelineStart.getTime()) / (1000 * 60 * 60 * 24)
    const taskDuration = (taskEnd.getTime() - taskStart.getTime()) / (1000 * 60 * 60 * 24)
    
    const left = (taskStartDays / totalDays) * 100
    const width = (taskDuration / totalDays) * 100
    
    return { left: Math.max(0, left), width: Math.max(1, width) }
  }

  const getTaskBarColor = (status: Task['status']) => {
    switch (status) {
      case 'completed':
        return '#10b981' // green-500
      case 'in-progress':
        return '#3b82f6' // blue-500
      case 'blocked':
        return '#ef4444' // red-500
      default:
        return '#94a3b8' // slate-400
    }
  }

  const timeline = generateTimeline()

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
          </div>
        </div>
      </div>
    )
  }

  if (!project) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Projekt hittades inte</h1>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center space-x-4">
            <Link href={`/dashboard/projects/${project.id}`}>
              <Button variant="outline" size="sm">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Tillbaka
              </Button>
            </Link>
            <div>
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                {project.name}
              </h1>
              <p className="text-gray-500 dark:text-gray-400">
                Projekthantering och översikt
              </p>
            </div>
          </div>
          <div className="flex items-center space-x-3">
            <Button>
              <Plus className="w-4 h-4 mr-2" />
              Ny uppgift
            </Button>
          </div>
        </div>

        {/* Tabbed Interface */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
          {/* Tab Headers */}
          <div className="border-b border-gray-200">
            <nav className="flex space-x-8 px-6" aria-label="Tabs">
              <button
                onClick={() => setActiveTab('timeline')}
                className={`py-4 px-1 border-b-2 font-medium text-sm whitespace-nowrap transition-colors duration-200 ${
                  activeTab === 'timeline'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <Calendar className="w-4 h-4 inline mr-2" />
                Tidslinje
              </button>
              <button
                onClick={() => setActiveTab('staff')}
                className={`py-4 px-1 border-b-2 font-medium text-sm whitespace-nowrap transition-colors duration-200 ${
                  activeTab === 'staff'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <Users className="w-4 h-4 inline mr-2" />
                Personal
              </button>
              <button
                onClick={() => setActiveTab('reports')}
                className={`py-4 px-1 border-b-2 font-medium text-sm whitespace-nowrap transition-colors duration-200 ${
                  activeTab === 'reports'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <BarChart3 className="w-4 h-4 inline mr-2" />
                Rapporter
              </button>
              <button
                onClick={() => setActiveTab('settings')}
                className={`py-4 px-1 border-b-2 font-medium text-sm whitespace-nowrap transition-colors duration-200 ${
                  activeTab === 'settings'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <Settings className="w-4 h-4 inline mr-2" />
                Inställningar
              </button>
            </nav>
          </div>

          {/* Tab Content */}
          <div className="p-6">
            {tabLoading ? (
              <div className="flex justify-center py-8">
                <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-blue-500"></div>
              </div>
            ) : (
              <>
                {/* Timeline Tab */}
                {activeTab === 'timeline' && (
                  <div>
                    {/* Google-style Filters */}
                    <Card className="mb-6 shadow-sm border-0 bg-white/60 backdrop-blur-sm">
                      <CardContent className="p-6">
                        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0 lg:space-x-6">
                          {/* Search */}
                          <div className="flex-1 max-w-md">
                            <div className="relative">
                              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                              <Input
                                placeholder="Sök uppgifter..."
                                value={searchTerm}
                                onChange={(e) => setSearchTerm(e.target.value)}
                                className="pl-9 bg-white/80 border-gray-200 focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20 transition-all"
                              />
                            </div>
                          </div>

                          {/* Filters */}
                          <div className="flex items-center space-x-4">
                            <div className="flex items-center space-x-2">
                              <Filter className="w-4 h-4 text-gray-500" />
                              <Select value={statusFilter} onValueChange={setStatusFilter}>
                                <SelectTrigger className="w-[140px] bg-white/80 border-gray-200 focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20">
                                  <SelectValue placeholder="Status" />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="all">Alla status</SelectItem>
                                  <SelectItem value="todo">Att göra</SelectItem>
                                  <SelectItem value="in-progress">Pågående</SelectItem>
                                  <SelectItem value="completed">Klar</SelectItem>
                                  <SelectItem value="blocked">Blockerad</SelectItem>
                                </SelectContent>
                              </Select>
                            </div>

                            <Select value={priorityFilter} onValueChange={setPriorityFilter}>
                              <SelectTrigger className="w-[140px] bg-white/80 border-gray-200 focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20">
                                <SelectValue placeholder="Prioritet" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="all">Alla prioriteter</SelectItem>
                                <SelectItem value="high">Hög</SelectItem>
                                <SelectItem value="medium">Medium</SelectItem>
                                <SelectItem value="low">Låg</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>

                          {/* View Mode and Time Range */}
                          <div className="flex items-center space-x-4">
                            <div className="flex items-center bg-gray-100 rounded-lg p-1">
                              <Button
                                variant={viewMode === 'gantt' ? 'default' : 'ghost'}
                                size="sm"
                                onClick={() => setViewMode('gantt')}
                                className="px-3 py-1 text-xs h-8 rounded-md transition-all"
                              >
                                Gantt
                              </Button>
                              <Button
                                variant={viewMode === 'list' ? 'default' : 'ghost'}
                                size="sm"
                                onClick={() => setViewMode('list')}
                                className="px-3 py-1 text-xs h-8 rounded-md transition-all"
                              >
                                Lista
                              </Button>
                            </div>

                            {viewMode === 'gantt' && (
                              <Select value={timeRange} onValueChange={(value: 'week' | 'month' | 'quarter') => setTimeRange(value)}>
                                <SelectTrigger className="w-[120px] bg-white/80 border-gray-200 focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20">
                                  <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="week">Vecka</SelectItem>
                                  <SelectItem value="month">Månad</SelectItem>
                                  <SelectItem value="quarter">Kvartal</SelectItem>
                                </SelectContent>
                              </Select>
                            )}
                          </div>
                        </div>
                      </CardContent>
                    </Card>

                    {/* Timeline Content */}
                    {viewMode === 'gantt' ? (
                      <Card className="shadow-sm border-0 overflow-hidden">
                        <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 border-b border-gray-100 pb-4">
                          <CardTitle className="flex items-center text-lg font-semibold text-gray-900">
                            <Calendar className="w-5 h-5 mr-2 text-blue-600" />
                            Gantt-schema
                          </CardTitle>
                        </CardHeader>
                        <CardContent className="p-0">
                          <div className="overflow-x-auto">
                            <div className="min-w-[1000px]">
                              {/* Timeline Header */}
                              <div className="flex border-b border-gray-200 bg-gray-50/80">
                                <div className="w-80 p-4 border-r border-gray-200 bg-white">
                                  <h3 className="font-medium text-gray-900">Uppgift</h3>
                                </div>
                                <div className="flex-1 p-4">
                                  <div className="flex justify-between text-sm text-gray-600 font-medium">
                                    {timeline.map((date, index) => (
                                      <div key={index} className="text-center min-w-[80px]">
                                        {date.toLocaleDateString('sv-SE', { 
                                          month: 'short', 
                                          day: 'numeric',
                                          ...(timeRange === 'quarter' && { year: 'numeric' })
                                        })}
                                      </div>
                                    ))}
                                  </div>
                                </div>
                              </div>

                              {/* Tasks */}
                              {filteredTasks.map((task, index) => {
                                const position = calculateTaskPosition(task, timeline)
                                const taskColor = getTaskBarColor(task.status)
                                
                                return (
                                  <div key={task.id} className={`flex border-b border-gray-100 hover:bg-blue-50/30 transition-colors ${index % 2 === 0 ? 'bg-white' : 'bg-gray-50/30'}`}>
                                    {/* Task Info */}
                                    <div className="w-80 p-4 border-r border-gray-200 bg-white">
                                      <div className="flex items-center justify-between">
                                        <div className="flex items-center space-x-3">
                                          {getStatusIcon(task.status)}
                                          <div className="min-w-0">
                                            <h4 className="font-medium text-gray-900 text-sm truncate">{task.name}</h4>
                                            <p className="text-xs text-gray-500 truncate">{task.assignee}</p>
                                          </div>
                                        </div>
                                        <div className="flex items-center space-x-1">
                                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getPriorityColor(task.priority)}`}>
                                            {getPriorityDisplayName(task.priority)}
                                          </span>
                                        </div>
                                      </div>
                                    </div>

                                    {/* Timeline Bar */}
                                    <div className="flex-1 p-4 relative">
                                      <div className="relative h-8 flex items-center">
                                        {/* Task Bar */}
                                        <div
                                          className="absolute h-6 rounded-lg shadow-sm flex items-center justify-center overflow-hidden"
                                          style={{
                                            left: `${position.left}%`,
                                            width: `${position.width}%`,
                                            backgroundColor: taskColor,
                                            background: `linear-gradient(90deg, ${taskColor} 0%, ${taskColor}dd ${task.progress}%, ${taskColor}40 ${task.progress}%, ${taskColor}40 100%)`
                                          }}
                                        >
                                          {/* Progress Text */}
                                          <span className="text-white text-xs font-medium px-2 relative z-10">
                                            {task.progress}%
                                          </span>
                                          
                                          {/* Progress Overlay */}
                                          <div 
                                            className="absolute top-0 left-0 h-full bg-black/10 rounded-lg"
                                            style={{ 
                                              left: `${task.progress}%`,
                                              width: `${100 - task.progress}%`
                                            }}
                                          />
                                        </div>

                                        {/* Task Details on Hover */}
                                        <div className="absolute inset-0 opacity-0 hover:opacity-100 transition-opacity bg-gradient-to-r from-blue-500/10 to-transparent rounded-lg flex items-center pl-2">
                                          <span className="text-xs text-gray-600 font-medium">
                                            {new Date(task.start_date).toLocaleDateString('sv-SE')} - {new Date(task.end_date).toLocaleDateString('sv-SE')}
                                          </span>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                )
                              })}
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ) : (
                      /* List View */
                      <div className="space-y-4">
                        {filteredTasks.map((task) => (
                          <Card key={task.id} className="hover:shadow-md transition-all duration-200 border-0 shadow-sm">
                            <CardContent className="p-6">
                              <div className="flex items-center justify-between">
                                <div className="flex items-center space-x-4">
                                  {getStatusIcon(task.status)}
                                  <div className="flex-1">
                                    <h3 className="font-medium text-gray-900">{task.name}</h3>
                                    <p className="text-sm text-gray-500 mt-1">{task.description}</p>
                                    <div className="flex items-center space-x-4 mt-2 text-sm text-gray-500">
                                      <span className="flex items-center">
                                        <Users className="w-4 h-4 mr-1" />
                                        {task.assignee}
                                      </span>
                                      <span className="flex items-center">
                                        <Calendar className="w-4 h-4 mr-1" />
                                        {new Date(task.start_date).toLocaleDateString('sv-SE')} - {new Date(task.end_date).toLocaleDateString('sv-SE')}
                                      </span>
                                      <span className="flex items-center">
                                        <Clock className="w-4 h-4 mr-1" />
                                        {task.actual_hours || 0}/{task.estimated_hours}h
                                      </span>
                                      <span className="flex items-center">
                                        <Target className="w-4 h-4 mr-1" />
                                        {task.progress}%
                                      </span>
                                    </div>
                                  </div>
                                </div>
                                <div className="flex items-center space-x-3">
                                  <span className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(task.status)}`}>
                                    {getStatusDisplayName(task.status)}
                                  </span>
                                  <span className={`px-3 py-1 rounded-full text-xs font-medium ${getPriorityColor(task.priority)}`}>
                                    {getPriorityDisplayName(task.priority)}
                                  </span>
                                  <Button variant="ghost" size="sm">
                                    <MoreVertical className="w-4 h-4" />
                                  </Button>
                                </div>
                              </div>
                              
                              {/* Progress Bar */}
                              <div className="mt-4">
                                <div className="flex items-center justify-between text-sm mb-2">
                                  <span className="text-gray-500">Framsteg</span>
                                  <span className="font-medium">{task.progress}%</span>
                                </div>
                                <div className="w-full bg-gray-100 rounded-full h-2">
                                  <div 
                                    className={`h-2 rounded-full transition-all duration-300 ${
                                      task.status === 'completed' 
                                        ? 'bg-green-500' 
                                        : task.status === 'in-progress'
                                        ? 'bg-blue-500'
                                        : 'bg-gray-400'
                                    }`}
                                    style={{ width: `${task.progress}%` }}
                                  />
                                </div>
                              </div>
                            </CardContent>
                          </Card>
                        ))}
                      </div>
                    )}

                    {/* Empty State */}
                    {filteredTasks.length === 0 && (
                      <Card className="shadow-sm border-0">
                        <CardContent className="p-12 text-center">
                          <Calendar className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                          <h3 className="text-lg font-medium text-gray-900 mb-2">Inga uppgifter hittades</h3>
                          <p className="text-gray-500 mb-6">Skapa din första uppgift för att komma igång med projektplaneringen.</p>
                          <Button>
                            <Plus className="w-4 h-4 mr-2" />
                            Skapa uppgift
                          </Button>
                        </CardContent>
                      </Card>
                    )}
                  </div>
                )}

                {/* Staff Tab */}
                {activeTab === 'staff' && (
                  <div>
                    <div className="flex items-center justify-between mb-6">
                      <h3 className="text-lg font-medium text-gray-900">Team Overview</h3>
                      <Button>
                        <Plus className="w-4 h-4 mr-2" />
                        Lägg till personal
                      </Button>
                    </div>

                    {/* Team Stats */}
                    <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                      <Card className="bg-gradient-to-br from-blue-50 to-indigo-50 border-blue-100">
                        <CardContent className="p-4">
                          <div className="text-2xl font-bold text-gray-900">{teamMembers.length}</div>
                          <div className="text-sm text-blue-600 font-medium">Teammedlemmar</div>
                        </CardContent>
                      </Card>
                      <Card className="bg-gradient-to-br from-green-50 to-emerald-50 border-green-100">
                        <CardContent className="p-4">
                          <div className="text-2xl font-bold text-gray-900">
                            {teamMembers.reduce((sum, member) => sum + member.hourly_rate, 0)}h
                          </div>
                          <div className="text-sm text-green-600 font-medium">Totalt timmar</div>
                        </CardContent>
                      </Card>
                      <Card className="bg-gradient-to-br from-amber-50 to-orange-50 border-amber-100">
                        <CardContent className="p-4">
                          <div className="text-2xl font-bold text-gray-900">
                            {Math.round(teamMembers.reduce((sum, member) => sum + member.hourly_rate, 0) / teamMembers.length)}kr
                          </div>
                          <div className="text-sm text-amber-600 font-medium">Snitt timkostnad</div>
                        </CardContent>
                      </Card>
                      <Card className="bg-gradient-to-br from-purple-50 to-violet-50 border-purple-100">
                        <CardContent className="p-4">
                          <div className="text-2xl font-bold text-gray-900">
                            {teamMembers.filter(m => m.availability === 'available').length}
                          </div>
                          <div className="text-sm text-purple-600 font-medium">Tillgängliga</div>
                        </CardContent>
                      </Card>
                    </div>

                    {/* Team Members */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      {teamMembers.map((member) => (
                        <Card key={member.id} className="hover:shadow-md transition-all duration-200">
                          <CardContent className="p-6">
                            <div className="flex items-start space-x-4">
                              <div className="w-12 h-12 bg-gradient-to-br from-blue-400 to-blue-600 rounded-full flex items-center justify-center text-white font-medium">
                                {member.name.split(' ').map(n => n[0]).join('')}
                              </div>
                              <div className="flex-1">
                                <div className="flex items-center justify-between mb-2">
                                  <h4 className="font-medium text-gray-900">{member.name}</h4>
                                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                                    member.availability === 'available' 
                                      ? 'bg-green-50 text-green-700' 
                                      : member.availability === 'busy'
                                      ? 'bg-amber-50 text-amber-700'
                                      : 'bg-red-50 text-red-700'
                                  }`}>
                                    {member.availability === 'available' ? 'Tillgänglig' : 
                                     member.availability === 'busy' ? 'Upptagen' : 'Otillgänglig'}
                                  </span>
                                </div>
                                <p className="text-sm text-gray-600 mb-2">{member.role}</p>
                                <p className="text-sm text-gray-500 mb-3">{member.email}</p>
                                
                                <div className="flex items-center justify-between text-sm">
                                  <span className="font-medium text-gray-900">{member.hourly_rate}kr/tim</span>
                                  <div className="flex flex-wrap gap-1">
                                    {member.skills.slice(0, 2).map((skill) => (
                                      <span key={skill} className="px-2 py-1 bg-gray-100 text-gray-600 rounded text-xs">
                                        {skill}
                                      </span>
                                    ))}
                                    {member.skills.length > 2 && (
                                      <span className="px-2 py-1 bg-gray-100 text-gray-600 rounded text-xs">
                                        +{member.skills.length - 2}
                                      </span>
                                    )}
                                  </div>
                                </div>
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  </div>
                )}

                {/* Reports Tab */}
                {activeTab === 'reports' && (
                  <div>
                    <div className="flex items-center justify-between mb-6">
                      <h3 className="text-lg font-medium text-gray-900">Projektrapporter</h3>
                      <Button>
                        <FileText className="w-4 h-4 mr-2" />
                        Generera rapport
                      </Button>
                    </div>
                    
                    <div className="text-center py-8">
                      <BarChart3 className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                      <h3 className="text-lg font-medium text-gray-900 mb-2">Rapporter kommer snart</h3>
                      <p className="text-gray-500">Projektrapporter och analyser är under utveckling.</p>
                    </div>
                  </div>
                )}

                {/* Settings Tab */}
                {activeTab === 'settings' && (
                  <div>
                    <div className="flex items-center justify-between mb-6">
                      <h3 className="text-lg font-medium text-gray-900">Projektinställningar</h3>
                    </div>
                    
                    <div className="text-center py-8">
                      <Settings className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                      <h3 className="text-lg font-medium text-gray-900 mb-2">Inställningar kommer snart</h3>
                      <p className="text-gray-500">Projektinställningar är under utveckling.</p>
                    </div>
                  </div>
                )}
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  )
} 