'use client'

import { useEffect, useState } from 'react'
import { useR<PERSON><PERSON>, usePara<PERSON> } from 'next/navigation'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import { 
  ArrowLeft, 
  Plus, 
  Users, 
  Search, 
  Filter, 
  Mail,
  Phone,
  MapPin,
  Star,
  MoreVertical,
  Edit,
  Trash2,
  UserPlus,
  Clock,
  Target
} from 'lucide-react'
import Link from 'next/link'
import { supabase } from '@/lib/supabase/supabase'
import { toast } from 'sonner'

interface Project {
  id: string
  name: string
  description?: string
  status: 'planning' | 'active' | 'on-hold' | 'completed' | 'cancelled'
  customers?: {
    name: string
    company_name?: string
  }
}

interface TeamMember {
  id: string
  name: string
  email: string
  phone?: string
  role: 'project-manager' | 'developer' | 'designer' | 'tester' | 'analyst' | 'consultant'
  department?: string
  hourly_rate?: number
  skills: string[]
  availability: number // percentage
  current_tasks: number
  total_hours: number
  joined_date: string
  avatar?: string
}

export default function ProjectStaffPage() {
  const router = useRouter()
  const params = useParams()
  const projectId = params.id as string
  
  const [loading, setLoading] = useState(true)
  const [project, setProject] = useState<Project | null>(null)
  const [teamMembers, setTeamMembers] = useState<TeamMember[]>([])
  
  // Filter states
  const [searchTerm, setSearchTerm] = useState('')
  const [roleFilter, setRoleFilter] = useState('all')
  const [availabilityFilter, setAvailabilityFilter] = useState('all')

  // Mock team members data (in a real app, this would come from the database)
  const mockTeamMembers: TeamMember[] = [
    {
      id: '1',
      name: 'Youssef Mekidiche',
      email: '<EMAIL>',
      phone: '+46 70 123 4567',
      role: 'project-manager',
      department: 'Management',
      hourly_rate: 850,
      skills: ['Project Management', 'Agile', 'Leadership', 'Strategy'],
      availability: 90,
      current_tasks: 3,
      total_hours: 160,
      joined_date: '2025-01-01',
      avatar: '/avatars/youssef.jpg'
    },
    {
      id: '2',
      name: 'Lisa Andersson',
      email: '<EMAIL>',
      phone: '+46 70 234 5678',
      role: 'designer',
      department: 'Design',
      hourly_rate: 650,
      skills: ['UI/UX Design', 'Figma', 'Adobe Creative Suite', 'Prototyping'],
      availability: 75,
      current_tasks: 2,
      total_hours: 120,
      joined_date: '2025-01-08',
      avatar: '/avatars/lisa.jpg'
    },
    {
      id: '3',
      name: 'Erik Johansson',
      email: '<EMAIL>',
      phone: '+46 70 345 6789',
      role: 'developer',
      department: 'Development',
      hourly_rate: 750,
      skills: ['React', 'TypeScript', 'Node.js', 'PostgreSQL'],
      availability: 100,
      current_tasks: 4,
      total_hours: 200,
      joined_date: '2025-01-15',
      avatar: '/avatars/erik.jpg'
    },
    {
      id: '4',
      name: 'Anna Petersson',
      email: '<EMAIL>',
      phone: '+46 70 456 7890',
      role: 'developer',
      department: 'Development',
      hourly_rate: 720,
      skills: ['Python', 'Django', 'REST API', 'Docker'],
      availability: 80,
      current_tasks: 3,
      total_hours: 145,
      joined_date: '2025-01-20',
      avatar: '/avatars/anna.jpg'
    },
    {
      id: '5',
      name: 'Marcus Lindberg',
      email: '<EMAIL>',
      phone: '+46 70 567 8901',
      role: 'developer',
      department: 'Development',
      hourly_rate: 700,
      skills: ['Database Design', 'SQL', 'Redis', 'Performance Optimization'],
      availability: 60,
      current_tasks: 2,
      total_hours: 80,
      joined_date: '2025-02-01'
    },
    {
      id: '6',
      name: 'Sara Nilsson',
      email: '<EMAIL>',
      role: 'tester',
      department: 'Quality Assurance',
      hourly_rate: 550,
      skills: ['Test Automation', 'Selenium', 'Jest', 'Quality Assurance'],
      availability: 95,
      current_tasks: 1,
      total_hours: 60,
      joined_date: '2025-02-10'
    }
  ]

  useEffect(() => {
    if (projectId) {
      fetchProject()
      setTeamMembers(mockTeamMembers) // In real app, fetch team members from database
      setLoading(false)
    }
  }, [projectId])

  const fetchProject = async () => {
    try {
      const { data, error } = await supabase
        .from('projects')
        .select(`
          *,
          customers (
            name,
            company_name
          )
        `)
        .eq('id', projectId)
        .single()

      if (error) {
        console.error('Error fetching project:', error)
        toast.error('Kunde inte hämta projekt')
        return
      }

      setProject(data)
    } catch (error) {
      console.error('Error:', error)
      toast.error('Fel vid hämtning av projekt')
    }
  }

  // Filter team members
  const filteredMembers = teamMembers.filter(member => {
    const matchesSearch = member.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      member.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      member.skills.some(skill => skill.toLowerCase().includes(searchTerm.toLowerCase()))
    
    const matchesRole = roleFilter === 'all' || member.role === roleFilter
    const matchesAvailability = availabilityFilter === 'all' || 
      (availabilityFilter === 'high' && member.availability >= 80) ||
      (availabilityFilter === 'medium' && member.availability >= 50 && member.availability < 80) ||
      (availabilityFilter === 'low' && member.availability < 50)
    
    return matchesSearch && matchesRole && matchesAvailability
  })

  const getRoleDisplayName = (role: TeamMember['role']) => {
    switch (role) {
      case 'project-manager':
        return 'Projektledare'
      case 'developer':
        return 'Utvecklare'
      case 'designer':
        return 'Designer'
      case 'tester':
        return 'Testare'
      case 'analyst':
        return 'Analytiker'
      case 'consultant':
        return 'Konsult'
      default:
        return role
    }
  }

  const getRoleColor = (role: TeamMember['role']) => {
    switch (role) {
      case 'project-manager':
        return 'bg-purple-100 text-purple-700 border-purple-200'
      case 'developer':
        return 'bg-blue-100 text-blue-700 border-blue-200'
      case 'designer':
        return 'bg-pink-100 text-pink-700 border-pink-200'
      case 'tester':
        return 'bg-green-100 text-green-700 border-green-200'
      case 'analyst':
        return 'bg-yellow-100 text-yellow-700 border-yellow-200'
      case 'consultant':
        return 'bg-gray-100 text-gray-700 border-gray-200'
      default:
        return 'bg-gray-100 text-gray-700 border-gray-200'
    }
  }

  const getAvailabilityColor = (availability: number) => {
    if (availability >= 80) return 'text-green-600'
    if (availability >= 50) return 'text-yellow-600'
    return 'text-red-600'
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('sv-SE', {
      style: 'currency',
      currency: 'SEK',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount)
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
          </div>
        </div>
      </div>
    )
  }

  if (!project) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Projekt hittades inte</h1>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center space-x-4">
            <Link href={`/dashboard/projects/${project.id}`}>
              <Button variant="outline" size="sm">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Tillbaka
              </Button>
            </Link>
            <div>
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                Personal - {project.name}
              </h1>
              <p className="text-gray-500 dark:text-gray-400">
                Hantera projektteam och tilldelningar
              </p>
            </div>
          </div>
          <div className="flex items-center space-x-3">
            <Button>
              <UserPlus className="w-4 h-4 mr-2" />
              Lägg till medlem
            </Button>
          </div>
        </div>

        {/* Team Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Users className="w-8 h-8 text-blue-500" />
                <div className="ml-4">
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">{teamMembers.length}</p>
                  <p className="text-gray-500 dark:text-gray-400">Teammedlemmar</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Clock className="w-8 h-8 text-green-500" />
                <div className="ml-4">
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {teamMembers.reduce((sum, member) => sum + member.total_hours, 0)}h
                  </p>
                  <p className="text-gray-500 dark:text-gray-400">Totala timmar</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Target className="w-8 h-8 text-yellow-500" />
                <div className="ml-4">
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {Math.round(teamMembers.reduce((sum, member) => sum + member.availability, 0) / teamMembers.length)}%
                  </p>
                  <p className="text-gray-500 dark:text-gray-400">Snitt tillgänglighet</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Star className="w-8 h-8 text-purple-500" />
                <div className="ml-4">
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {teamMembers.reduce((sum, member) => sum + member.current_tasks, 0)}
                  </p>
                  <p className="text-gray-500 dark:text-gray-400">Aktiva uppgifter</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Apple-style Filters */}
        <Card className="mb-6">
          <CardContent className="p-6">
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0 lg:space-x-6">
              {/* Search */}
              <div className="flex-1 max-w-md">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <Input
                    placeholder="Sök team medlemmar..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-9 bg-white border-gray-200 focus:border-blue-500 focus:ring-blue-500"
                  />
                </div>
              </div>

              {/* Filters */}
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-2">
                  <Filter className="w-4 h-4 text-gray-500" />
                  <Select value={roleFilter} onValueChange={setRoleFilter}>
                    <SelectTrigger className="w-[160px] bg-white border-gray-200">
                      <SelectValue placeholder="Roll" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">Alla roller</SelectItem>
                      <SelectItem value="project-manager">Projektledare</SelectItem>
                      <SelectItem value="developer">Utvecklare</SelectItem>
                      <SelectItem value="designer">Designer</SelectItem>
                      <SelectItem value="tester">Testare</SelectItem>
                      <SelectItem value="analyst">Analytiker</SelectItem>
                      <SelectItem value="consultant">Konsult</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <Select value={availabilityFilter} onValueChange={setAvailabilityFilter}>
                  <SelectTrigger className="w-[160px] bg-white border-gray-200">
                    <SelectValue placeholder="Tillgänglighet" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All tillgänglighet</SelectItem>
                    <SelectItem value="high">Hög (80%+)</SelectItem>
                    <SelectItem value="medium">Medium (50-79%)</SelectItem>
                    <SelectItem value="low">Låg (&lt;50%)</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Team Members Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
          {filteredMembers.map((member) => (
            <Card key={member.id} className="hover:shadow-lg transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center space-x-3">
                    <Avatar className="w-12 h-12">
                      <AvatarImage src={member.avatar} alt={member.name} />
                      <AvatarFallback className="bg-blue-100 text-blue-600 font-semibold">
                        {member.name.split(' ').map(n => n[0]).join('')}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <h3 className="font-semibold text-gray-900">{member.name}</h3>
                      <p className="text-sm text-gray-500">{member.department}</p>
                    </div>
                  </div>
                  <Button variant="ghost" size="sm">
                    <MoreVertical className="w-4 h-4" />
                  </Button>
                </div>

                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className={`px-3 py-1 rounded-full text-xs font-medium ${getRoleColor(member.role)}`}>
                      {getRoleDisplayName(member.role)}
                    </span>
                    <span className={`font-medium ${getAvailabilityColor(member.availability)}`}>
                      {member.availability}% tillgänglig
                    </span>
                  </div>

                  <div className="flex items-center space-x-2 text-sm text-gray-500">
                    <Mail className="w-4 h-4" />
                    <span>{member.email}</span>
                  </div>

                  {member.phone && (
                    <div className="flex items-center space-x-2 text-sm text-gray-500">
                      <Phone className="w-4 h-4" />
                      <span>{member.phone}</span>
                    </div>
                  )}

                  <div className="grid grid-cols-2 gap-4 pt-2">
                    <div className="text-center">
                      <p className="text-lg font-semibold text-gray-900">{member.current_tasks}</p>
                      <p className="text-xs text-gray-500">Uppgifter</p>
                    </div>
                    <div className="text-center">
                      <p className="text-lg font-semibold text-gray-900">{member.total_hours}h</p>
                      <p className="text-xs text-gray-500">Timmar</p>
                    </div>
                  </div>

                  {member.hourly_rate && (
                    <div className="text-center pt-2 border-t border-gray-100">
                      <p className="text-sm font-medium text-gray-900">
                        {formatCurrency(member.hourly_rate)}/h
                      </p>
                    </div>
                  )}

                  <div className="pt-2">
                    <p className="text-xs text-gray-500 mb-2">Kompetenser:</p>
                    <div className="flex flex-wrap gap-1">
                      {member.skills.slice(0, 3).map((skill, index) => (
                        <Badge key={index} variant="secondary" className="text-xs">
                          {skill}
                        </Badge>
                      ))}
                      {member.skills.length > 3 && (
                        <Badge variant="outline" className="text-xs">
                          +{member.skills.length - 3}
                        </Badge>
                      )}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Empty State */}
        {filteredMembers.length === 0 && (
          <Card>
            <CardContent className="p-12 text-center">
              <Users className="w-16 h-16 text-gray-300 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">Inga teammedlemmar hittades</h3>
              <p className="text-gray-500 mb-6">Lägg till teammedlemmar för att komma igång med projektarbetet.</p>
              <Button>
                <UserPlus className="w-4 h-4 mr-2" />
                Lägg till medlem
              </Button>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  )
} 