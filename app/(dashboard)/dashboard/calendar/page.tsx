'use client';

import React, { useState } from 'react';
import GoogleLayout from '@/components/ui/google-layout';
// Remove the Sidebar import as it's now in the layout
// import Sidebar from '../../components/Sidebar';
// Don't need to import Header either as it's in the layout
// import Header from '../../components/Header';

interface CalendarEvent {
  id: number;
  title: string;
  date: string;
  time?: string;
  category: 'meeting' | 'deadline' | 'reminder' | 'other';
  description?: string;
}

export default function CalendarPage() {
  const [activeTab, setActiveTab] = useState('overview');
  const [currentDate, setCurrentDate] = useState(new Date());
  const [events, setEvents] = useState<CalendarEvent[]>([
    {
      id: 1,
      title: 'Team Meeting',
      date: '2024-01-15',
      time: '10:00',
      category: 'meeting',
      description: 'Weekly team sync to discuss progress and blockers'
    },
    {
      id: 2,
      title: 'Project Deadline',
      date: '2024-01-20',
      category: 'deadline',
      description: 'Final submission for the mobile app redesign'
    },
    {
      id: 3,
      title: 'Client Call',
      date: '2024-01-10',
      time: '14:30',
      category: 'meeting',
      description: 'Discuss requirements for the new project'
    },
    {
      id: 4,
      title: 'Review Pull Requests',
      date: '2024-01-08',
      category: 'reminder',
      description: 'Review and merge outstanding PRs'
    }
  ]);
  
  const [showEventModal, setShowEventModal] = useState(false);
  const [selectedDate, setSelectedDate] = useState('');
  const [newEvent, setNewEvent] = useState<Partial<CalendarEvent>>({
    title: '',
    date: '',
    category: 'meeting'
  });
  
  // Calendar generation functions
  const getMonthData = (date: Date) => {
    const year = date.getFullYear();
    const month = date.getMonth();
    
    // First day of the month
    const firstDay = new Date(year, month, 1);
    // Last day of the month
    const lastDay = new Date(year, month + 1, 0);
    
    const daysInMonth = lastDay.getDate();
    const startingDayOfWeek = firstDay.getDay(); // 0 = Sunday, 1 = Monday, etc.
    
    // Generate grid with empty cells for days from previous and next months
    const calendar = [];
    let day = 1;
    
    // Each calendar might need 6 rows (weeks)
    for (let i = 0; i < 6; i++) {
      const week = [];
      // 7 columns (days of week)
      for (let j = 0; j < 7; j++) {
        if (i === 0 && j < startingDayOfWeek) {
          // Empty cells before the first day of the month
          week.push({
            day: null,
            date: null
          });
        } else if (day > daysInMonth) {
          // Empty cells after the last day of the month
          week.push({
            day: null,
            date: null
          });
        } else {
          // Valid days in the current month
          const currentDate = new Date(year, month, day);
          week.push({
            day,
            date: currentDate
          });
          day++;
        }
      }
      calendar.push(week);
      // If we've already included all days of the month, break
      if (day > daysInMonth) {
        break;
      }
    }
    
    return calendar;
  };
  
  const calendar = getMonthData(currentDate);
  
  const getMonthName = (date: Date) => {
    return date.toLocaleString('default', { month: 'long' });
  };
  
  const nextMonth = () => {
    setCurrentDate(new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 1));
  };
  
  const prevMonth = () => {
    setCurrentDate(new Date(currentDate.getFullYear(), currentDate.getMonth() - 1, 1));
  };
  
  const getEventsForDate = (date: Date | null) => {
    if (!date) return [];
    
    const dateString = date.toISOString().split('T')[0];
    return events.filter(event => event.date === dateString);
  };
  
  const handleDayClick = (date: Date | null) => {
    if (!date) return;
    
    const dateString = date.toISOString().split('T')[0];
    setSelectedDate(dateString);
    setNewEvent(prev => ({ ...prev, date: dateString }));
    setShowEventModal(true);
  };
  
  const handleCreateEvent = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!newEvent.title?.trim() || !newEvent.date) return;
    
    const event: CalendarEvent = {
      id: Math.max(0, ...events.map(e => e.id)) + 1,
      title: newEvent.title,
      date: newEvent.date,
      time: newEvent.time,
      category: newEvent.category || 'other',
      description: newEvent.description
    };
    
    setEvents([...events, event]);
    setNewEvent({
      title: '',
      date: '',
      category: 'meeting'
    });
    setShowEventModal(false);
  };
  
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setNewEvent(prev => ({ ...prev, [name]: value }));
  };
  
  const getCategoryColor = (category: CalendarEvent['category']) => {
    switch (category) {
      case 'meeting':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      case 'deadline':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      case 'reminder':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  const handleTabChange = (tab: string) => {
    setActiveTab(tab);
  };

  const renderTabContent = () => {
    switch (activeTab) {
      case 'overview':
        return renderCalendarContent();
      case 'resources':
        return <div className="p-6">Project Staffing content coming soon...</div>;
      case 'timeline':
        return <div className="p-6">Project Planning content coming soon...</div>;
      default:
        return renderCalendarContent();
    }
  };

  const renderCalendarContent = () => (
    <div className="max-w-7xl mx-auto space-y-6">
      {/* Calendar header */}
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center">
        <div>
          <h1 className="text-2xl font-semibold text-gray-900 dark:text-white">Calendar</h1>
          <p className="text-gray-600 dark:text-gray-400">
            {getMonthName(currentDate)} {currentDate.getFullYear()}
          </p>
        </div>
        
        <div className="mt-4 md:mt-0 flex items-center space-x-4">
          <button 
            onClick={prevMonth} 
            className="p-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-700 rounded-md text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700"
          >
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M15 18L9 12L15 6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
          </button>
          
          <button 
            onClick={() => setCurrentDate(new Date())} 
            className="px-4 py-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-700 rounded-md text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700"
          >
            Today
          </button>
          
          <button 
            onClick={nextMonth} 
            className="p-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-700 rounded-md text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700"
          >
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M9 6L15 12L9 18" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
          </button>
          
          <button 
            onClick={() => {
              setSelectedDate(new Date().toISOString().split('T')[0]);
              setNewEvent(prev => ({ ...prev, date: new Date().toISOString().split('T')[0] }));
              setShowEventModal(true);
            }} 
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            <svg className="w-4 h-4 mr-2" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M12 5V19M5 12H19" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"></path>
            </svg>
            Add Event
          </button>
        </div>
      </div>
      
      {/* Calendar grid */}
      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden shadow">
        {/* Days of week */}
        <div className="grid grid-cols-7 border-b border-gray-200 dark:border-gray-700">
          <div className="py-2 text-center text-sm font-medium text-gray-500 dark:text-gray-400">Sun</div>
          <div className="py-2 text-center text-sm font-medium text-gray-500 dark:text-gray-400">Mon</div>
          <div className="py-2 text-center text-sm font-medium text-gray-500 dark:text-gray-400">Tue</div>
          <div className="py-2 text-center text-sm font-medium text-gray-500 dark:text-gray-400">Wed</div>
          <div className="py-2 text-center text-sm font-medium text-gray-500 dark:text-gray-400">Thu</div>
          <div className="py-2 text-center text-sm font-medium text-gray-500 dark:text-gray-400">Fri</div>
          <div className="py-2 text-center text-sm font-medium text-gray-500 dark:text-gray-400">Sat</div>
        </div>
        
        {/* Calendar days */}
        <div>
          {calendar.map((week, weekIndex) => (
            <div key={weekIndex} className="grid grid-cols-7 border-b border-gray-200 dark:border-gray-700 last:border-b-0">
              {week.map((day, dayIndex) => {
                const isToday = day.date && 
                  day.date.getDate() === new Date().getDate() && 
                  day.date.getMonth() === new Date().getMonth() && 
                  day.date.getFullYear() === new Date().getFullYear();
                
                const dayEvents = getEventsForDate(day.date);
                
                return (
                  <div 
                    key={dayIndex}
                    onClick={() => handleDayClick(day.date)}
                    className={`min-h-[120px] p-2 border-r border-gray-200 dark:border-gray-700 last:border-r-0 ${
                      day.day ? 'cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700' : 'bg-gray-50 dark:bg-gray-900'
                    }`}
                  >
                    {day.day && (
                      <>
                        <div className={`text-sm font-medium ${
                          isToday 
                            ? 'text-white bg-blue-600 rounded-full w-7 h-7 flex items-center justify-center' 
                            : 'text-gray-700 dark:text-gray-300'
                        }`}>
                          {day.day}
                        </div>
                        
                        <div className="mt-2 space-y-1">
                          {dayEvents.slice(0, 3).map(event => (
                            <div 
                              key={event.id}
                              className={`px-2 py-1 text-xs rounded truncate ${getCategoryColor(event.category)}`}
                            >
                              {event.time && `${event.time} - `}{event.title}
                            </div>
                          ))}
                          {dayEvents.length > 3 && (
                            <div className="text-xs text-gray-500 dark:text-gray-400">
                              +{dayEvents.length - 3} more
                            </div>
                          )}
                        </div>
                      </>
                    )}
                  </div>
                );
              })}
            </div>
          ))}
        </div>
      </div>
      
      {/* New Event Modal */}
      {showEventModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg max-w-md w-full p-6">
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">Add New Event</h3>
            
            <form onSubmit={handleCreateEvent}>
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Event Title</label>
                <input
                  type="text"
                  name="title"
                  value={newEvent.title || ''}
                  onChange={handleInputChange}
                  className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Enter event title"
                  required
                />
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Date</label>
                  <input
                    type="date"
                    name="date"
                    value={newEvent.date || ''}
                    onChange={handleInputChange}
                    className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Time (optional)</label>
                  <input
                    type="time"
                    name="time"
                    value={newEvent.time || ''}
                    onChange={handleInputChange}
                    className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
              </div>
              
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Category</label>
                <select
                  name="category"
                  value={newEvent.category || 'meeting'}
                  onChange={handleInputChange}
                  className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="meeting">Meeting</option>
                  <option value="deadline">Deadline</option>
                  <option value="reminder">Reminder</option>
                  <option value="other">Other</option>
                </select>
              </div>
              
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Description (optional)</label>
                <textarea
                  name="description"
                  value={newEvent.description || ''}
                  onChange={handleInputChange}
                  rows={3}
                  className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Add event details"
                ></textarea>
              </div>
              
              <div className="flex justify-end space-x-3 mt-6">
                <button
                  type="button"
                  onClick={() => setShowEventModal(false)}
                  className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                >
                  Add Event
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );

  return (
    <GoogleLayout 
      projectName="Calendar" 
      activeTab={activeTab}
      onTabChange={handleTabChange}
      showTabs={{
        overview: true,
        tasks: false,
        resources: true,
        timeline: true,
        settings: false
      }}
    >
      {renderTabContent()}
    </GoogleLayout>
  );
} 