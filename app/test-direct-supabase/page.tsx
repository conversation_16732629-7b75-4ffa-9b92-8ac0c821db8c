"use client";

import { useState } from 'react';
import { createBrowserClient } from '@supabase/ssr';

export default function TestDirectSupabase() {
  const [message, setMessage] = useState('Direct Supabase import test');

  const testConnection = async () => {
    try {
      const client = createBrowserClient(
        process.env.NEXT_PUBLIC_SUPABASE_URL!,
        process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
      );
      
      const { data, error } = await client.auth.getSession();
      setMessage(`Direct connection test: ${error ? 'Error' : 'Success'}`);
    } catch (err) {
      setMessage(`Error: ${err}`);
    }
  };

  return (
    <div style={{ padding: '20px' }}>
      <h1>Test Direct Supabase Import</h1>
      <p>{message}</p>
      <button 
        onClick={testConnection}
        style={{ padding: '10px', backgroundColor: '#0070f3', color: 'white', border: 'none' }}
      >
        Test Connection
      </button>
    </div>
  );
} 