'use client'
import { <PERSON>, CardHeader, Card<PERSON>itle, CardContent } from '../../components/ui/card'
import { Progress } from '../../components/ui/progress'
import { 
  AlertCircle, 
  Clock, 
  CheckCircle, 
  TrendingUp, 
  TrendingDown, 
  ArrowUpRight, 
  Calendar, 
  CircleCheck, 
  Briefcase, 
  Users, 
  Search, 
  Bell, 
  HelpCircle, 
  Menu,
  Settings,
  MessageCircle,
  Star,
  ChevronRight,
  FileText,
  Edit,
  UserPlus
} from 'lucide-react'
import { getGreeting, getCurrentDate } from '../../lib/utils'
import { useState } from 'react'

export default function Dashboard() {
  const [searchFocused, setSearchFocused] = useState(false);

  // Mock data - ersätt med riktig API-anslutning senare
  const financialData = {
    liquidity: 1250000,
    invoicedThisMonth: 850000,
    overduePayments: 125000,
    grossMargin: 0.32
  }

  const projects = [
    { name: 'Villa Solvik', phase: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>', budget: 3200000, actual: 2850000, deviation: -0.11 },
    { name: '<PERSON><PERSON><PERSON>', phase: 'Planering', budget: 2800000, actual: 0, deviation: 0 },
    { name: '<PERSON>nto<PERSON>renovering', phase: 'Avslutat', budget: 1500000, actual: 1450000, deviation: -0.03 }
  ]

  const staff = [
    { name: 'Anna Andersson', role: 'Senior projektledare', utilization: 0.85, absence: 0, certifications: ['ID06', 'Heta arbeten'], overtimeRisk: true },
    { name: 'Björn Bengtsson', role: 'Byggnadsarbetare', utilization: 0.65, absence: 2, certifications: ['ID06'], overtimeRisk: false }
  ]

  // Helper för att renderera projektets status-indikator
  const getPhaseIndicator = (phase: string) => {
    switch(phase) {
      case 'Pågående':
        return <span className="text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20 px-2 py-1 rounded-full text-xs font-medium">Pågående</span>
      case 'Planering':
        return <span className="text-amber-600 dark:text-amber-400 bg-amber-50 dark:bg-amber-900/20 px-2 py-1 rounded-full text-xs font-medium">Planering</span>
      case 'Avslutat':
        return <span className="text-green-600 dark:text-green-400 bg-green-50 dark:bg-green-900/20 px-2 py-1 rounded-full text-xs font-medium">Avslutat</span>
      default:
        return null;
    }
  }

  return (
    <>
      {/* Välkomsthälsning - Updated with Google Material Design */}
      <div className="px-6 pt-8 pb-4">
        <h1 className="text-2xl font-normal text-gray-800 dark:text-white">{getGreeting()}, Youssef</h1>
        <p className="text-gray-500 dark:text-gray-400 mt-1 text-sm font-normal">Här är din översikt för {getCurrentDate()}</p>
      </div>

      <div className="space-y-8 font-sans px-6 pb-8">
        {/* Finansiell översikt - Updated with Google Material Design */}
        <section>
          <div className="flex justify-between items-center mb-5">
            <div className="flex items-center">
              <Briefcase className="w-5 h-5 text-blue-600 dark:text-blue-400 mr-2" />
              <h2 className="text-xl font-medium text-gray-800 dark:text-white">Finansiell översikt</h2>
            </div>
            <button className="text-blue-600 dark:text-blue-400 text-sm font-medium flex items-center hover:bg-blue-50 dark:hover:bg-blue-900/10 px-3 py-1 rounded-full transition-colors">
              Visa alla <ChevronRight className="ml-0.5 w-4 h-4" />
            </button>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <Card className="shadow-sm border-0 bg-white dark:bg-gray-800 rounded-xl overflow-hidden hover:shadow-md transition-shadow duration-300">
              <CardContent className="p-0">
                <div className="p-5">
                  <div className="flex flex-col">
                    <p className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">Likviditet</p>
                    <span className="text-2xl font-normal text-gray-900 dark:text-white">
                      {new Intl.NumberFormat('sv-SE', { style: 'currency', currency: 'SEK' }).format(financialData.liquidity)}
                    </span>
                    <span className="text-sm text-green-600 dark:text-green-400 mt-2 flex items-center">
                      <TrendingUp className="w-4 h-4 mr-1" /> 12% från föregående månad
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="shadow-sm border-0 bg-white dark:bg-gray-800 rounded-xl overflow-hidden hover:shadow-md transition-shadow duration-300">
              <CardContent className="p-0">
                <div className="p-5">
                  <div className="flex flex-col">
                    <p className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">Fakturerat (månad)</p>
                    <span className="text-2xl font-normal text-gray-900 dark:text-white">
                      {new Intl.NumberFormat('sv-SE', { style: 'currency', currency: 'SEK' }).format(financialData.invoicedThisMonth)}
                    </span>
                    <span className="text-sm text-amber-600 dark:text-amber-400 mt-2 flex items-center">
                      <TrendingDown className="w-4 h-4 mr-1" /> 3% från föregående månad
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="shadow-sm border-0 bg-white dark:bg-gray-800 rounded-xl overflow-hidden hover:shadow-md transition-shadow duration-300">
              <CardContent className="p-0">
                <div className="p-5">
                  <div className="flex flex-col">
                    <p className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2 flex items-center">
                      <AlertCircle className="w-4 h-4 text-red-500 mr-1" /> Försenade betalningar
                    </p>
                    <span className="text-2xl font-normal text-red-600 dark:text-red-400">
                      {new Intl.NumberFormat('sv-SE', { style: 'currency', currency: 'SEK' }).format(financialData.overduePayments)}
                    </span>
                    <span className="text-sm text-gray-500 dark:text-gray-400 mt-2">
                      3 obetalda fakturor
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="shadow-sm border-0 bg-white dark:bg-gray-800 rounded-xl overflow-hidden hover:shadow-md transition-shadow duration-300">
              <CardContent className="p-0">
                <div className="p-5">
                  <div className="flex flex-col">
                    <p className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">Bruttomarginal</p>
                    <span className="text-2xl font-normal text-gray-900 dark:text-white">
                      {(financialData.grossMargin * 100).toFixed(1)}%
                    </span>
                    <div className="mt-2 w-full bg-gray-100 dark:bg-gray-700 rounded-full h-2">
                      <div 
                        className="bg-green-500 h-2 rounded-full transition-all duration-500" 
                        style={{ width: `${financialData.grossMargin * 100}%` }}
                      ></div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </section>

        {/* Kundrelationer / försäljning - Moved here and restyled with Google Material Design */}
        <section>
          <div className="flex justify-between items-center mb-5">
            <div className="flex items-center">
              <Users className="w-5 h-5 text-blue-600 dark:text-blue-400 mr-2" />
              <h2 className="text-xl font-medium text-gray-800 dark:text-white">Kundrelationer / försäljning</h2>
            </div>
            <button className="text-blue-600 dark:text-blue-400 text-sm font-medium flex items-center hover:bg-blue-50 dark:hover:bg-blue-900/10 px-3 py-1 rounded-full transition-colors">
              Visa alla <ChevronRight className="ml-0.5 w-4 h-4" />
            </button>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card className="shadow-sm border-0 bg-white dark:bg-gray-800 rounded-xl overflow-hidden hover:shadow-md transition-shadow duration-300">
              <CardContent className="p-0">
                <div className="p-5 border-b border-gray-100 dark:border-gray-700">
                  <h3 className="text-base font-medium text-gray-800 dark:text-white">Pågående offerter</h3>
                </div>
                
                <div className="px-5 py-4 bg-blue-50 dark:bg-blue-900/10">
                  <div className="flex justify-between mb-1">
                    <span className="font-medium text-gray-700 dark:text-gray-300">Total offertvärde</span>
                    <span className="font-medium text-blue-700 dark:text-blue-400">{new Intl.NumberFormat('sv-SE', { style: 'currency', currency: 'SEK' }).format(5400000)}</span>
                  </div>
                  <div className="flex justify-between text-sm text-gray-500 dark:text-gray-400">
                    <span className="flex items-center"><Calendar className="w-4 h-4 mr-1" /> 4 aktiva offerter</span>
                    <span className="flex items-center"><TrendingUp className="w-4 h-4 mr-1" /> Sannolikhet: 68%</span>
                  </div>
                </div>
                
                <div className="divide-y divide-gray-100 dark:divide-gray-700">
                  <div className="flex items-center justify-between p-5 hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors cursor-pointer">
                    <div className="flex items-center">
                      <div className="w-1.5 h-10 bg-amber-500 rounded-full mr-3 flex-shrink-0"></div>
                      <div>
                        <div className="font-medium text-gray-900 dark:text-white">Nybyggnation Nacka</div>
                        <div className="text-xs text-gray-500 dark:text-gray-400 mt-0.5 flex items-center">
                          <Calendar className="w-3 h-3 mr-1 text-gray-400 dark:text-gray-500" /> Deadline: 15 juni
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center">
                      <span className="font-medium text-gray-900 dark:text-white text-right">{new Intl.NumberFormat('sv-SE', { style: 'currency', currency: 'SEK' }).format(2800000)}</span>
                      <ChevronRight className="w-5 h-5 ml-2 text-gray-400 dark:text-gray-500" />
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-between p-5 hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors cursor-pointer">
                    <div className="flex items-center">
                      <div className="w-1.5 h-10 bg-blue-500 rounded-full mr-3 flex-shrink-0"></div>
                      <div>
                        <div className="font-medium text-gray-900 dark:text-white">Renovering Södertälje</div>
                        <div className="text-xs text-gray-500 dark:text-gray-400 mt-0.5 flex items-center">
                          <Calendar className="w-3 h-3 mr-1 text-gray-400 dark:text-gray-500" /> Deadline: 8 juni
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center">
                      <span className="font-medium text-gray-900 dark:text-white text-right">{new Intl.NumberFormat('sv-SE', { style: 'currency', currency: 'SEK' }).format(1200000)}</span>
                      <ChevronRight className="w-5 h-5 ml-2 text-gray-400 dark:text-gray-500" />
                    </div>
                  </div>
                </div>
                
                <div className="p-3">
                  <button className="w-full py-2.5 text-sm font-medium text-blue-600 dark:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/10 rounded-lg transition-colors text-center">
                    Visa alla offerter
                  </button>
                </div>
              </CardContent>
            </Card>
            
            <Card className="shadow-sm border-0 bg-white dark:bg-gray-800 rounded-xl overflow-hidden hover:shadow-md transition-shadow duration-300">
              <CardContent className="p-0">
                <div className="p-5 border-b border-gray-100 dark:border-gray-700">
                  <h3 className="text-base font-medium text-gray-800 dark:text-white">Konverteringsstatistik</h3>
                </div>
                
                <div className="p-5">
                  <div className="space-y-6">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <div className="w-10 h-10 rounded-full bg-green-50 dark:bg-green-900/10 flex items-center justify-center text-green-600 dark:text-green-400 mr-3 flex-shrink-0">
                          <TrendingUp className="w-5 h-5" />
                        </div>
                        <div>
                          <div className="text-sm font-normal text-gray-500 dark:text-gray-400">Vunna offerter</div>
                          <div className="text-2xl font-normal text-gray-900 dark:text-white">68%</div>
                        </div>
                      </div>
                      <div className="flex flex-col items-end">
                        <div className="text-xs text-gray-400 dark:text-gray-500">senaste 3 mån</div>
                        <div className="mt-1 text-sm text-green-600 dark:text-green-400 flex items-center">
                          <TrendingUp className="w-4 h-4 mr-0.5" /> 5%
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <div className="w-10 h-10 rounded-full bg-blue-50 dark:bg-blue-900/10 flex items-center justify-center text-blue-600 dark:text-blue-400 mr-3 flex-shrink-0">
                          <MessageCircle className="w-5 h-5" />
                        </div>
                        <div>
                          <div className="text-sm font-normal text-gray-500 dark:text-gray-400">Förfrågningar</div>
                          <div className="text-2xl font-normal text-gray-900 dark:text-white">12</div>
                        </div>
                      </div>
                      <div className="flex flex-col items-end">
                        <div className="text-xs text-gray-400 dark:text-gray-500">senaste veckan</div>
                        <div className="mt-1 text-sm text-green-600 dark:text-green-400 flex items-center">
                          <TrendingUp className="w-4 h-4 mr-0.5" /> 2
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <div className="w-10 h-10 rounded-full bg-amber-50 dark:bg-amber-900/10 flex items-center justify-center text-amber-600 dark:text-amber-400 mr-3 flex-shrink-0">
                          <Star className="w-5 h-5" />
                        </div>
                        <div>
                          <div className="text-sm font-normal text-gray-500 dark:text-gray-400">Kundnöjdhet</div>
                          <div className="text-2xl font-normal text-gray-900 dark:text-white">4.6/5</div>
                        </div>
                      </div>
                      <div className="flex flex-col items-end">
                        <div className="text-xs text-gray-400 dark:text-gray-500">senaste månaden</div>
                        <div className="mt-1 text-sm text-amber-600 dark:text-amber-400 flex items-center">
                          <TrendingDown className="w-4 h-4 mr-0.5" /> 0.2
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div className="border-t border-gray-100 dark:border-gray-700 mt-1 p-3">
                  <button className="w-full py-2.5 text-sm font-medium text-blue-600 dark:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/10 rounded-lg transition-colors text-center">
                    Se detaljerad statistik
                  </button>
                </div>
              </CardContent>
            </Card>
          </div>
        </section>

        {/* Projektstatus - Updated with Google Material Design */}
        <section>
          <div className="flex justify-between items-center mb-5">
            <div className="flex items-center">
              <Calendar className="w-5 h-5 text-blue-600 dark:text-blue-400 mr-2" />
              <h2 className="text-xl font-medium text-gray-800 dark:text-white">Projektstatus</h2>
            </div>
            <button className="text-blue-600 dark:text-blue-400 text-sm font-medium flex items-center hover:bg-blue-50 dark:hover:bg-blue-900/10 px-3 py-1 rounded-full transition-colors">
              Visa alla projekt <ChevronRight className="ml-0.5 w-4 h-4" />
            </button>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {projects.map((project) => (
              <Card key={project.name} className="shadow-sm border-0 bg-white dark:bg-gray-800 rounded-xl overflow-hidden hover:shadow-md transition-shadow duration-300">
                <CardContent className="p-0">
                  <div className="p-5">
                    <div className="flex justify-between items-start mb-4">
                      <div>
                        <h3 className="text-base font-medium text-gray-800 dark:text-white">{project.name}</h3>
                        <div className="flex items-center gap-2 mt-1.5">
                          {getPhaseIndicator(project.phase)}
                          <span className="text-xs text-gray-500 dark:text-gray-400 flex items-center">
                            <Calendar className="w-3 h-3 mr-1" /> Deadline: 15 juni
                          </span>
                        </div>
                      </div>
                      {project.phase === 'Avslutat' && (
                        <div className="bg-green-50 dark:bg-green-900/20 h-8 w-8 rounded-full flex items-center justify-center">
                          <CircleCheck className="h-5 w-5 text-green-600 dark:text-green-400" />
                        </div>
                      )}
                    </div>
                    
                    <div className="space-y-4">
                      <div className="flex justify-between items-center">
                        <div className="flex flex-col">
                          <span className="text-xs text-gray-500 dark:text-gray-400">Budget</span>
                          <span className="text-sm font-medium text-gray-900 dark:text-white mt-0.5">
                            {new Intl.NumberFormat('sv-SE', { style: 'currency', currency: 'SEK' }).format(project.budget)}
                          </span>
                        </div>
                        <div className="flex flex-col items-end">
                          <span className="text-xs text-gray-500 dark:text-gray-400">Förbrukat</span>
                          <span className="text-sm font-medium text-gray-900 dark:text-white mt-0.5">
                            {new Intl.NumberFormat('sv-SE', { style: 'currency', currency: 'SEK' }).format(project.actual)}
                          </span>
                        </div>
                      </div>
                      
                      <div>
                        <div className="flex justify-between mb-1.5">
                          <span className="text-xs text-gray-500 dark:text-gray-400">Progressionsgrad</span>
                          <span className={`text-xs font-medium ${
                            project.deviation < -0.1 
                              ? 'text-red-600 dark:text-red-400' 
                              : project.deviation < 0 
                                ? 'text-amber-600 dark:text-amber-400'
                                : 'text-green-600 dark:text-green-400'
                          }`}>
                            {(project.deviation * 100).toFixed(1)}%
                          </span>
                        </div>
                        <div className="w-full bg-gray-100 dark:bg-gray-700 rounded-full h-2">
                          <div 
                            className={`h-2 rounded-full transition-all duration-500 ${
                              project.deviation < -0.1 
                                ? 'bg-red-500' 
                                : project.deviation < 0 
                                  ? 'bg-amber-500'
                                  : 'bg-green-500'
                            }`}
                            style={{ width: `${Math.min(100, (project.actual / project.budget) * 100)}%` }}
                          ></div>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </section>

        {/* Beläggning och personal - Updated with Google Material Design */}
        <section>
          <div className="flex justify-between items-center mb-5">
            <div className="flex items-center">
              <Users className="w-5 h-5 text-blue-600 dark:text-blue-400 mr-2" />
              <h2 className="text-xl font-medium text-gray-800 dark:text-white">Beläggning och personal</h2>
            </div>
            <button className="text-blue-600 dark:text-blue-400 text-sm font-medium flex items-center hover:bg-blue-50 dark:hover:bg-blue-900/10 px-3 py-1 rounded-full transition-colors">
              Hantera schema <ChevronRight className="ml-0.5 w-4 h-4" />
            </button>
          </div>
          
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="lg:col-span-2">
              <Card className="shadow-sm border-0 bg-white dark:bg-gray-800 rounded-xl overflow-hidden hover:shadow-md transition-shadow duration-300">
                <CardContent className="p-0">
                  {staff.map((person, index) => (
                    <div key={person.name} className={`hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors ${index !== staff.length - 1 ? 'border-b border-gray-100 dark:border-gray-700' : ''}`}>
                      <div className="p-4 flex items-center justify-between">
                        <div className="flex items-center">
                          <div className="h-10 w-10 flex-shrink-0 rounded-full bg-gradient-to-r from-blue-500 to-indigo-600 flex items-center justify-center text-white font-medium mr-4">
                            {person.name.split(' ').map(n => n[0]).join('')}
                          </div>
                          <div>
                            <div className="font-medium text-gray-900 dark:text-white">{person.name}</div>
                            <div className="text-xs text-gray-500 dark:text-gray-400 mt-0.5">{person.role}</div>
                          </div>
                        </div>

                        <div className="flex items-center gap-6">
                          <div className="w-32">
                            <div className="flex justify-between mb-1 text-xs">
                              <span className="text-gray-500 dark:text-gray-400">Beläggning</span>
                              <span className={`font-medium ${
                                person.utilization > 0.8 
                                  ? 'text-red-600 dark:text-red-400' 
                                  : person.utilization > 0.6 
                                    ? 'text-amber-600 dark:text-amber-400'
                                    : 'text-green-600 dark:text-green-400'
                              }`}>
                                {(person.utilization * 100).toFixed(0)}%
                              </span>
                            </div>
                            <div className="w-full bg-gray-100 dark:bg-gray-700 rounded-full h-2">
                              <div 
                                className={`h-2 rounded-full transition-all duration-500 ${
                                  person.utilization > 0.8 
                                    ? 'bg-red-500' 
                                    : person.utilization > 0.6 
                                      ? 'bg-amber-500'
                                      : 'bg-green-500'
                                }`}
                                style={{ width: `${person.utilization * 100}%` }}
                              ></div>
                            </div>
                          </div>

                          <div className="w-24 flex flex-col items-end">
                            <div className="flex flex-wrap gap-1 justify-end">
                              {person.certifications.map((cert) => (
                                <span key={cert} className="text-xs bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 px-2 py-0.5 rounded-full">
                                  {cert}
                                </span>
                              ))}
                            </div>
                            {person.overtimeRisk && (
                              <div className="mt-1 text-xs text-red-600 dark:text-red-400 flex items-center">
                                <AlertCircle className="w-3 h-3 mr-1" /> Övertidsrisk
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </CardContent>
              </Card>
            </div>
            
            <div>
              <Card className="shadow-sm border-0 bg-white dark:bg-gray-800 rounded-xl overflow-hidden hover:shadow-md transition-shadow duration-300">
                <CardContent className="p-0">
                  <div className="p-5 border-b border-gray-100 dark:border-gray-700">
                    <h3 className="text-base font-medium text-gray-800 dark:text-white">Beläggningsöversikt</h3>
                  </div>
                  
                  <div className="px-5 py-4">
                    <div className="grid grid-cols-3 gap-2 mb-6">
                      <div className="bg-blue-50 dark:bg-blue-900/10 rounded-lg p-4 text-center">
                        <div className="text-2xl font-normal text-gray-900 dark:text-white">
                          {Math.round(staff.reduce((sum, s) => sum + s.utilization, 0) / staff.length * 100)}%
                        </div>
                        <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">Genomsnitt</div>
                      </div>
                      <div className="bg-blue-50 dark:bg-blue-900/10 rounded-lg p-4 text-center">
                        <div className="text-2xl font-normal text-gray-900 dark:text-white">
                          {staff.filter(s => s.utilization >= 0.9).length}
                        </div>
                        <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">Fullt uppbokade</div>
                      </div>
                      <div className="bg-blue-50 dark:bg-blue-900/10 rounded-lg p-4 text-center">
                        <div className="text-2xl font-normal text-gray-900 dark:text-white">
                          {staff.filter(s => s.utilization < 0.7).length}
                        </div>
                        <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">Tillgängliga</div>
                      </div>
                    </div>
                    
                    <div className="space-y-5">
                      <div>
                        <div className="flex justify-between items-center mb-1.5">
                          <div className="flex items-center">
                            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Denna vecka</span>
                            {/* Google-style info tooltip */}
                            <div className="ml-1 w-4 h-4 rounded-full bg-gray-100 dark:bg-gray-700 flex items-center justify-center cursor-help">
                              <span className="text-xs text-gray-500 dark:text-gray-400">?</span>
                            </div>
                          </div>
                          <div className="flex items-center">
                            <span className="font-medium text-gray-900 dark:text-white mr-2">83%</span>
                            <span className="text-xs text-green-600 dark:text-green-400 flex items-center">
                              <TrendingUp className="w-3 h-3 mr-0.5" /> 2%
                            </span>
                          </div>
                        </div>
                        <div className="relative w-full h-2 bg-gray-100 dark:bg-gray-700 rounded-full overflow-hidden">
                          <div className="absolute top-0 left-0 h-full bg-blue-600 dark:bg-blue-500 rounded-full transition-all duration-500" style={{ width: '83%' }}></div>
                        </div>
                      </div>
                      
                      <div>
                        <div className="flex justify-between items-center mb-1.5">
                          <div className="flex items-center">
                            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Nästa vecka</span>
                          </div>
                          <div className="flex items-center">
                            <span className="font-medium text-gray-900 dark:text-white mr-2">76%</span>
                            <span className="text-xs text-red-600 dark:text-red-400 flex items-center">
                              <TrendingDown className="w-3 h-3 mr-0.5" /> 7%
                            </span>
                          </div>
                        </div>
                        <div className="relative w-full h-2 bg-gray-100 dark:bg-gray-700 rounded-full overflow-hidden">
                          <div className="absolute top-0 left-0 h-full bg-blue-600 dark:bg-blue-500 rounded-full transition-all duration-500" style={{ width: '76%' }}></div>
                        </div>
                      </div>
                      
                      <div>
                        <div className="flex justify-between items-center mb-1.5">
                          <div className="flex items-center">
                            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Om två veckor</span>
                          </div>
                          <div className="flex items-center">
                            <span className="font-medium text-gray-900 dark:text-white mr-2">62%</span>
                            <span className="text-xs text-amber-600 dark:text-amber-400 flex items-center">
                              <TrendingDown className="w-3 h-3 mr-0.5" /> 14%
                            </span>
                          </div>
                        </div>
                        <div className="relative w-full h-2 bg-gray-100 dark:bg-gray-700 rounded-full overflow-hidden">
                          <div className="absolute top-0 left-0 h-full bg-blue-600 dark:bg-blue-500 rounded-full transition-all duration-500" style={{ width: '62%' }}></div>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <div className="border-t border-gray-100 dark:border-gray-700 p-5">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center">
                        <AlertCircle className="w-4 h-4 text-red-500 dark:text-red-400 mr-2" />
                        <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Övertidsvarningar</span>
                      </div>
                      <div className="flex items-center">
                        <span className={`text-xs font-medium px-2 py-0.5 rounded-full ${staff.filter(s => s.overtimeRisk).length > 0 ? 'bg-red-50 text-red-600 dark:bg-red-900/20 dark:text-red-400' : 'bg-green-50 text-green-600 dark:bg-green-900/20 dark:text-green-400'}`}>
                          {staff.filter(s => s.overtimeRisk).length}
                        </span>
                      </div>
                    </div>
                    
                    {staff.filter(s => s.overtimeRisk).length > 0 && (
                      <div className="text-xs text-gray-500 dark:text-gray-400 mt-1 flex items-start">
                        <span className="bg-red-50 dark:bg-red-900/10 text-red-600 dark:text-red-400 w-5 h-5 rounded-full flex items-center justify-center flex-shrink-0 mr-2 mt-0.5">!</span>
                        <span>Anna Andersson har jobbat mer än 40 timmar denna vecka och riskerar övertid enligt arbetstidslagen.</span>
                      </div>
                    )}
                  </div>
                  
                  <div className="border-t border-gray-100 dark:border-gray-700 p-3">
                    <button className="w-full py-2.5 text-sm font-medium text-blue-600 dark:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/10 rounded-lg transition-colors text-center">
                      Visa detaljerad beläggning
                    </button>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>

        {/* Tidslinje / händelselog */}
        <section className="mt-6">
          <div className="flex justify-between items-center mb-5">
            <div className="flex items-center">
              <Clock className="w-5 h-5 text-blue-600 dark:text-blue-400 mr-2" />
              <h2 className="text-xl font-medium text-gray-800 dark:text-white">Senaste händelser</h2>
            </div>
            <button className="text-blue-600 dark:text-blue-400 text-sm font-medium flex items-center hover:bg-blue-50 dark:hover:bg-blue-900/10 px-3 py-1 rounded-full transition-colors">
              Visa alla <ChevronRight className="ml-0.5 w-4 h-4" />
            </button>
          </div>
          <Card className="shadow-sm border-0 bg-white dark:bg-gray-800 rounded-xl overflow-hidden hover:shadow-md transition-shadow duration-300">
            <CardContent className="p-0">
              <div className="divide-y divide-gray-100 dark:divide-gray-700">
                <div className="p-5 hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors cursor-pointer">
                  <div className="flex">
                    <div className="mr-4 flex-shrink-0">
                      <div className="w-10 h-10 rounded-full bg-green-50 dark:bg-green-900/10 flex items-center justify-center text-green-600 dark:text-green-400">
                        <CheckCircle className="w-5 h-5" />
                      </div>
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between mb-1">
                        <h3 className="text-base font-medium text-gray-900 dark:text-white truncate">Projekt avslutat: Kontorsrenovering</h3>
                        <span className="text-xs text-gray-500 dark:text-gray-400 ml-4">Idag 09:45</span>
                      </div>
                      <p className="text-sm text-gray-500 dark:text-gray-400">Slutfördes inom budget (3% under budgeterad kostnad)</p>
                      <div className="mt-2">
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-50 text-green-700 dark:bg-green-900/20 dark:text-green-400">
                          Slutförd
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div className="p-5 hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors cursor-pointer">
                  <div className="flex">
                    <div className="mr-4 flex-shrink-0">
                      <div className="w-10 h-10 rounded-full bg-blue-50 dark:bg-blue-900/10 flex items-center justify-center text-blue-600 dark:text-blue-400">
                        <FileText className="w-5 h-5" />
                      </div>
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between mb-1">
                        <h3 className="text-base font-medium text-gray-900 dark:text-white truncate">Ny offert skickad: Nybyggnation Nacka</h3>
                        <span className="text-xs text-gray-500 dark:text-gray-400 ml-4">Igår 16:32</span>
                      </div>
                      <p className="text-sm text-gray-500 dark:text-gray-400">Värde: 2,8 MSEK, svarstid 14 dagar</p>
                      <div className="mt-2">
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-50 text-blue-700 dark:bg-blue-900/20 dark:text-blue-400">
                          Offert
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div className="p-5 hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors cursor-pointer">
                  <div className="flex">
                    <div className="mr-4 flex-shrink-0">
                      <div className="w-10 h-10 rounded-full bg-amber-50 dark:bg-amber-900/10 flex items-center justify-center text-amber-600 dark:text-amber-400">
                        <Edit className="w-5 h-5" />
                      </div>
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between mb-1">
                        <h3 className="text-base font-medium text-gray-900 dark:text-white truncate">Ändring i kontrakt: Villa Solvik</h3>
                        <span className="text-xs text-gray-500 dark:text-gray-400 ml-4">2 dagar sedan 11:20</span>
                      </div>
                      <p className="text-sm text-gray-500 dark:text-gray-400">Tilläggsarbeten godkända, +320 000 SEK</p>
                      <div className="mt-2">
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-amber-50 text-amber-700 dark:bg-amber-900/20 dark:text-amber-400">
                          Ändring
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div className="p-5 hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors cursor-pointer">
                  <div className="flex">
                    <div className="mr-4 flex-shrink-0">
                      <div className="w-10 h-10 rounded-full bg-purple-50 dark:bg-purple-900/10 flex items-center justify-center text-purple-600 dark:text-purple-400">
                        <UserPlus className="w-5 h-5" />
                      </div>
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between mb-1">
                        <h3 className="text-base font-medium text-gray-900 dark:text-white truncate">Ny anställd påbörjade tjänst</h3>
                        <span className="text-xs text-gray-500 dark:text-gray-400 ml-4">5 dagar sedan 08:00</span>
                      </div>
                      <p className="text-sm text-gray-500 dark:text-gray-400">Björn Bengtsson, Designer</p>
                      <div className="mt-2">
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-50 text-purple-700 dark:bg-purple-900/20 dark:text-purple-400">
                          Personal
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="border-t border-gray-100 dark:border-gray-700 p-3">
                <button className="w-full py-2.5 text-sm font-medium text-blue-600 dark:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/10 rounded-lg transition-colors text-center">
                  Visa alla händelser
                </button>
              </div>
            </CardContent>
          </Card>
        </section>
      </div>
    </>
  )
} 