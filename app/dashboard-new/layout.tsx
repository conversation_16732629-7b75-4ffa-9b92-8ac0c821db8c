'use client';

import React, { useState } from 'react';
import DashboardSidebar from '../components/DashboardSidebar';
import { usePathname } from 'next/navigation';

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const [collapsed, setCollapsed] = useState(false);
  const pathname = usePathname();
  
  return (
    <div className="flex h-screen bg-gray-50 dark:bg-gray-900">
      {/* Sidebar */}
      <DashboardSidebar collapsed={collapsed} setCollapsed={setCollapsed} />
      
      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* No header component is used by design */}
        
        {/* Main Content Area */}
        <main 
          className="
            flex-1 overflow-y-auto 
            transition-all duration-300
            bg-white dark:bg-gray-900
          "
        >
          {children}
        </main>
      </div>
    </div>
  );
} 