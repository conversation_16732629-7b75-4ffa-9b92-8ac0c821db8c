import { NextRequest, NextResponse } from 'next/server';
import { createAppRouterSupabaseClient } from '@/lib/supabase/supabase-server';

export async function GET(request: NextRequest) {
  const requestUrl = new URL(request.url);
  const code = requestUrl.searchParams.get('code');
  const redirectPath = requestUrl.searchParams.get('redirect') || '/dashboard';

  if (code) {
    const supabase = await createAppRouterSupabaseClient();

    // Byt ut auth-koden från magic link eller OAuth mot en sessioncookie
    await supabase.auth.exchangeCodeForSession(code);
  }

  // Omdirigera till dashboard eller tillbaka till den ursprungliga sidan
  return NextResponse.redirect(new URL(redirectPath, requestUrl.origin));
} 