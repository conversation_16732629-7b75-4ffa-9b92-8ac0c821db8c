'use client'

import { createContext, useContext, useEffect, useState, useCallback } from 'react'
import { Session, User } from '@supabase/supabase-js'
import { supabase } from '@/lib/supabase/supabase'
import { useRouter } from 'next/navigation'

interface AuthContextType {
  user: User | null
  session: Session | null
  isLoading: boolean
  signIn: (email: string, password: string) => Promise<{
    error: Error | null
    data: Session | null
  }>
  signUp: (email: string, password: string) => Promise<{
    error: Error | null
    data: { user: User | null; session: Session | null }
  }>
  signOut: () => Promise<void>
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [session, setSession] = useState<Session | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const router = useRouter()

  // Initialize auth and set up auth state listener
  useEffect(() => {
    console.log('[AuthProvider] Initializing auth state')
    
    // Get the initial session
    const getInitialSession = async () => {
      try {
        const { data: { session }, error } = await supabase.auth.getSession()
        
        if (error) {
          console.error('[AuthProvider] Error getting session:', error)
        }
        
        console.log('[AuthProvider] Initial session:', !!session)
        setSession(session)
        setUser(session?.user ?? null)
      } catch (error) {
        console.error('[AuthProvider] Error during session initialization:', error)
        setSession(null)
        setUser(null)
      } finally {
        setIsLoading(false)
      }
    }

    getInitialSession()

    // Set up the listener for auth state changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      (event, newSession) => {
        console.log(`[AuthProvider] Auth state change: ${event}`, !!newSession)
        
        setSession(newSession)
        setUser(newSession?.user ?? null)
        setIsLoading(false)

        // Handle navigation based on auth state changes
        if (event === 'SIGNED_OUT') {
          console.log('[AuthProvider] User signed out - navigating to login')
          router.push('/login')
        }
      }
    )

    // Clean up the listener
    return () => {
      console.log('[AuthProvider] Cleaning up auth subscription')
      subscription.unsubscribe()
    }
  }, [router])

  // Sign in function
  const signIn = useCallback(async (email: string, password: string) => {
    console.log('[AuthProvider] Signing in:', email)
    
    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      })

      console.log('[AuthProvider] Sign in result:', {
        success: !!data.session,
        hasError: !!error,
      })

      return { data: data.session, error }
    } catch (err) {
      console.error('[AuthProvider] Sign in error:', err)
      return { data: null, error: err as Error }
    }
  }, [])

  // Sign up function
  const signUp = useCallback(async (email: string, password: string) => {
    console.log('[AuthProvider] Signing up:', email)
    
    try {
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
      })

      console.log('[AuthProvider] Sign up result:', {
        success: !!data.user,
        error: !!error,
      })

      return { data, error }
    } catch (err) {
      console.error('[AuthProvider] Sign up error:', err)
      return { data: { user: null, session: null }, error: err as Error }
    }
  }, [])

  // Sign out function
  const signOut = useCallback(async () => {
    console.log('[AuthProvider] Signing out')
    
    try {
      const { error } = await supabase.auth.signOut()
      
      if (error) {
        console.error('[AuthProvider] Sign out error:', error)
        throw error
      }
      
      console.log('[AuthProvider] Sign out successful')
    } catch (err) {
      console.error('[AuthProvider] Sign out exception:', err)
      // Still clear the state even if there's an error
      setSession(null)
      setUser(null)
      throw err
    }
  }, [])

  // Create the context value
  const value = {
    user,
    session,
    isLoading,
    signIn,
    signUp,
    signOut,
  }

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
} 