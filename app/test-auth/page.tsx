'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { useRouter, useSearchParams } from 'next/navigation';
import { supabase } from '@/lib/supabase/supabase';

export default function TestAuthPage() {
  const [authStatus, setAuthStatus] = useState<any>(null);
  const [loginResult, setLoginResult] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const router = useRouter();
  const searchParams = useSearchParams();

  const redirectedFrom = searchParams.get('redirectedFrom');

  useEffect(() => {
    checkAuthStatus();
  }, []);

  const checkAuthStatus = async () => {
    try {
      const response = await fetch('/api/auth/status');
      const data = await response.json();
      setAuthStatus(data);
    } catch (error) {
      console.error('Error checking auth status:', error);
    }
  };

  const testLogin = async () => {
    setLoading(true);
    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email: '<EMAIL>',
        password: 'password123'
      });

      setLoginResult({ data, error });
      
      if (data.session) {
        console.log('Login successful, redirecting...');
        // Wait a moment for auth state to update
        setTimeout(() => {
          if (redirectedFrom) {
            window.location.href = redirectedFrom;
          } else {
            window.location.href = '/dashboard';
          }
        }, 1000);
      }
    } catch (error) {
      console.error('Login error:', error);
      setLoginResult({ error });
    } finally {
      setLoading(false);
    }
  };

  const testLogout = async () => {
    try {
      await supabase.auth.signOut();
      setAuthStatus(null);
      setLoginResult(null);
      await checkAuthStatus();
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4">
      <div className="max-w-4xl mx-auto">
        <div className="bg-white rounded-lg shadow p-6">
          <h1 className="text-2xl font-bold mb-6">Authentication Flow Test</h1>
          
          {redirectedFrom && (
            <div className="bg-blue-50 border border-blue-200 rounded p-4 mb-6">
              <h3 className="font-medium text-blue-900">Redirected From:</h3>
              <p className="text-blue-700">{redirectedFrom}</p>
            </div>
          )}

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Auth Status */}
            <div className="border rounded p-4">
              <h2 className="text-lg font-semibold mb-3">Current Auth Status</h2>
              <button 
                onClick={checkAuthStatus}
                className="mb-3 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
              >
                Refresh Status
              </button>
              <pre className="bg-gray-100 p-3 rounded text-sm overflow-auto">
                {JSON.stringify(authStatus, null, 2)}
              </pre>
            </div>

            {/* Login Test */}
            <div className="border rounded p-4">
              <h2 className="text-lg font-semibold mb-3">Login Test</h2>
              <div className="space-y-3">
                <button 
                  onClick={testLogin}
                  disabled={loading}
                  className="w-full px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 disabled:opacity-50"
                >
                  {loading ? 'Logging in...' : 'Test Login (<EMAIL>)'}
                </button>
                
                <button 
                  onClick={testLogout}
                  className="w-full px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600"
                >
                  Test Logout
                </button>
              </div>
              
              {loginResult && (
                <div className="mt-3">
                  <h3 className="font-medium mb-2">Login Result:</h3>
                  <pre className="bg-gray-100 p-3 rounded text-sm overflow-auto">
                    {JSON.stringify(loginResult, null, 2)}
                  </pre>
                </div>
              )}
            </div>
          </div>

          {/* Test Links */}
          <div className="mt-6 border rounded p-4">
            <h2 className="text-lg font-semibold mb-3">Test Protected Routes</h2>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
              <Link 
                href="/dashboard" 
                className="px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600 text-center"
              >
                Dashboard
              </Link>
              <Link 
                href="/dashboard/fakturor" 
                className="px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600 text-center"
              >
                Fakturor
              </Link>
              <Link 
                href="/dashboard/customers" 
                className="px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600 text-center"
              >
                Customers
              </Link>
              <Link 
                href="/dashboard/settings" 
                className="px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600 text-center"
              >
                Settings
              </Link>
            </div>
          </div>

          {/* Instructions */}
          <div className="mt-6 bg-yellow-50 border border-yellow-200 rounded p-4">
            <h3 className="font-medium text-yellow-900 mb-2">Test Instructions:</h3>
            <ol className="list-decimal list-inside text-yellow-800 space-y-1">
              <li>Check current auth status (should be false if not logged in)</li>
              <li>Click a protected route link above (should redirect to login with redirectedFrom parameter)</li>
              <li>Use the "Test Login" button to authenticate</li>
              <li>Verify you're redirected back to the originally requested page</li>
              <li>Test logout and verify you're redirected to login when accessing protected routes</li>
            </ol>
          </div>
        </div>
      </div>
    </div>
  );
}
