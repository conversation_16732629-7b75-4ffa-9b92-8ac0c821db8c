"use client";

import { useState } from 'react';

export default function TestNoSupabase() {
  const [message, setMessage] = useState('No Supabase imports here!');

  return (
    <div style={{ padding: '20px' }}>
      <h1>Test Page - No Supabase</h1>
      <p>{message}</p>
      <button 
        onClick={() => setMessage('Button clicked!')}
        style={{ padding: '10px', backgroundColor: '#0070f3', color: 'white', border: 'none' }}
      >
        Click me
      </button>
    </div>
  );
} 