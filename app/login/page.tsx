"use client";

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { useRouter, useSearchParams } from 'next/navigation';
import { Eye, EyeOff, ArrowLeft, AlertCircle, CheckCircle2, XCircle, Loader2 } from 'lucide-react';
import { supabase } from '@/lib/supabase';
import { useAuth } from '../auth/auth-provider';

// Möjliga statuskoder för inloggningsförsök
type LoginStatus = 
  | 'idle' 
  | 'loading' 
  | 'success'
  | 'invalid_credentials'
  | 'invalid_email'
  | 'empty_fields'
  | 'network_error'
  | 'server_error'
  | 'too_many_attempts';

/**
 * Handles login and post-login navigation
 * @param email User email
 * @param password User password
 * @returns Object with success status and error if any
 */
async function performLogin(email: string, password: string, redirectTo: string = '/dashboard') {
  try {
    // Login with Supabase directly
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password
    });
    
    if (error) {
      return { success: false, error: error.message };
    }
    
    if (data.session) {
      // Show browser console message for debugging
      console.log('Login successful!', { user: data.user?.email, expiresAt: data.session.expires_at });
      
      // Important: Small delay to let cookies be set properly
      return { success: true, redirectTo };
    } else {
      return { success: false, error: 'No session was created' };
    }
  } catch (err) {
    console.error('Login error:', err);
    return { success: false, error: err instanceof Error ? err.message : 'Unknown error' };
  }
}

export default function Login() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const redirectTo = searchParams.get('redirect') || '/dashboard';
  
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [error, setError] = useState('');
  const [loginStatus, setLoginStatus] = useState<LoginStatus>('idle');
  const [isLoading, setIsLoading] = useState(false);
  const [isMagicLinkSent, setIsMagicLinkSent] = useState(false);
  const [statusMessage, setStatusMessage] = useState('');
  const { signIn } = useAuth();

  // DEV BYPASS - Direct navigation function for development/debugging
  const handleDevBypass = () => {
    console.log('DEV BYPASS: Navigating directly to dashboard...');
    window.location.href = redirectTo;
  };

  // Kolla om det finns ett statusmeddelande i URL:en
  useEffect(() => {
    const status = searchParams.get('status');
    if (status === 'session_expired') {
      setStatusMessage('Din session har gått ut. Vänligen logga in igen.');
    } else if (status === 'logout_success') {
      setStatusMessage('Du har loggats ut.');
    }
  }, [searchParams]);

  // Översättningsfunktion för API-felmeddelanden
  const translateErrorMessage = (message: string): string => {
    if (message.includes('Invalid login credentials')) {
      return 'Fel e-postadress eller lösenord';
    } else if (message.includes('Email not confirmed')) {
      return 'E-postadressen är inte bekräftad. Kontrollera din inkorg och bekräfta din e-post.';
    } else if (message.includes('Too many requests')) {
      return 'För många inloggningsförsök. Vänta en stund och försök igen.';
    } else if (message.includes('Password should be at least')) {
      return 'Lösenordet måste vara minst 6 tecken långt.';
    } else if (message.includes('Email format is invalid')) {
      return 'E-postadressen har fel format.';
    }
    return message;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    console.log("Login form submitted");
    
    if (!email || !password) {
      setError('Both email and password are required');
      setLoginStatus('empty_fields');
      return;
    }
    
    setError('');
    setIsLoading(true);
    setLoginStatus('loading');
    
    try {
      // Use the AuthProvider's signIn method
      const { data: session, error } = await signIn(email, password);
      
      console.log("Auth result:", { 
        hasSession: !!session, 
        hasError: !!error,
        user: session?.user?.email
      });
      
      if (error) {
        console.error("Login error:", error.message);
        setError(error.message);
        setLoginStatus('invalid_credentials');
        setIsLoading(false);
        return;
      }
      
      if (session) {
        console.log("Login successful - session established");
        setLoginStatus('success');
        
        // Show success message
        setError('');
        
        // Let the auth state change handler redirect us
        console.log("Login successful, waiting for auth state change...");
        
        // Small delay to let auth state update, then redirect
        setTimeout(() => {
          router.push(redirectTo);
        }, 100);
      } else {
        setError('Login successful but no session was created');
        setLoginStatus('error');
        setIsLoading(false);
      }
    } catch (err) {
      console.error("Unexpected error during login:", err);
      setError('An unexpected error occurred');
      setLoginStatus('error');
      setIsLoading(false);
    }
  };

  const handleMagicLinkSignIn = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!email) {
      setError('Ange din e-postadress för att få en magisk länk');
      setLoginStatus('empty_fields');
      return;
    }
    
    // Validera e-postformat
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      setError('Ogiltig e-postadress');
      setLoginStatus('invalid_email');
      return;
    }
    
    setError('');
    setIsLoading(true);
    setLoginStatus('loading');
    
    try {
      const { error } = await supabase.auth.signInWithOtp({
        email,
        options: {
          emailRedirectTo: `${window.location.origin}${redirectTo}`,
        },
      });

      if (error) {
        const translatedError = translateErrorMessage(error.message);
        setError(translatedError);
        setLoginStatus('server_error');
        return;
      }

      setIsMagicLinkSent(true);
      setLoginStatus('success');
    } catch (err: any) {
      setError('Ett oväntat fel uppstod. Kontrollera din internetanslutning och försök igen.');
      setLoginStatus('network_error');
    } finally {
      setIsLoading(false);
    }
  };

  // Funktion för att återställa inloggningsformuläret
  const resetForm = () => {
    setEmail('');
    setPassword('');
    setError('');
    setLoginStatus('idle');
    setIsMagicLinkSent(false);
  };

  // Returnera rätt statusikon baserat på loginStatus
  const getStatusIcon = () => {
    switch (loginStatus) {
      case 'loading':
        return <Loader2 className="h-5 w-5 animate-spin text-blue-500" />;
      case 'success':
        return <CheckCircle2 className="h-5 w-5 text-green-500" />;
      case 'invalid_credentials':
      case 'invalid_email':
      case 'empty_fields':
      case 'network_error':
      case 'server_error':
      case 'too_many_attempts':
        return <XCircle className="h-5 w-5 text-red-500" />;
      default:
        return null;
    }
  };

  // Funktion för att få statusfärg för inputfält
  const getInputBorderColor = (field: 'email' | 'password') => {
    if (loginStatus === 'idle') return 'ring-gray-300';
    
    if (field === 'email' && (loginStatus === 'invalid_email' || loginStatus === 'empty_fields')) {
      return 'ring-red-500';
    }
    
    if (field === 'password' && (loginStatus === 'invalid_credentials' || loginStatus === 'empty_fields')) {
      return 'ring-red-500';
    }
    
    return 'ring-gray-300';
  };

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <Link href="/" className="flex items-center text-gray-600 hover:text-gray-900 mb-6 ml-4 sm:ml-0">
          <ArrowLeft className="w-4 h-4 mr-1" />
          <span className="text-sm">Tillbaka till startsidan</span>
        </Link>
        
        <div className="text-center">
          <h2 className="mt-2 text-3xl font-bold leading-9 text-gray-900">
            <span className="text-blue-600">S</span>
            <span className="text-red-500">S</span>
            <span className="text-yellow-500">P</span>
          </h2>
          <h2 className="mt-2 text-2xl font-semibold text-gray-900">Logga in på ditt konto</h2>
          <p className="mt-2 text-sm text-gray-600">
            Svea Service Partner - Intelligent bygghantering
          </p>
        </div>
      </div>

      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <div className="bg-white px-4 py-8 shadow sm:rounded-lg sm:px-10">
          {/* DEV MODE BYPASS BUTTON */}
          <div className="bg-yellow-50 border border-yellow-100 text-yellow-800 px-4 py-3 rounded-md text-sm flex items-center mb-4">
            <AlertCircle className="h-4 w-4 mr-2 flex-shrink-0" />
            <span>Development Mode</span>
          </div>
          
          <div className="mb-6">
            <button
              type="button"
              onClick={handleDevBypass}
              className="flex w-full justify-center items-center rounded-md bg-yellow-600 px-3 py-2.5 text-sm font-semibold text-white shadow-sm hover:bg-yellow-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-yellow-600"
            >
              DEV: Skip Login & Go to Dashboard
            </button>
          </div>
          
          {/* Real Supabase User Info */}
          <div className="bg-blue-50 border border-blue-100 text-blue-800 px-4 py-3 rounded-md text-sm mb-4">
            <div className="font-medium mb-1">Available Supabase User:</div>
            <div><strong>Email:</strong> <EMAIL></div>
            <div><strong>Password:</strong> [Use your password]</div>
            <div className="text-xs mt-1 text-blue-600">This is a real user from your Supabase database</div>
          </div>
          
          {/* Horizontal divider */}
          <div className="relative mb-6">
            <div className="absolute inset-0 flex items-center">
              <div className="w-full border-t border-gray-300" />
            </div>
            <div className="relative flex justify-center text-sm">
              <span className="bg-white px-2 text-gray-500">Or use normal login</span>
            </div>
          </div>
          
          {/* Statusmeddelande från URL-parametrar */}
          {statusMessage && (
            <div className="mb-4 bg-blue-50 border border-blue-100 text-blue-700 px-4 py-3 rounded-md text-sm flex items-center">
              <AlertCircle className="h-4 w-4 mr-2 flex-shrink-0" />
              <span>{statusMessage}</span>
            </div>
          )}

          {isMagicLinkSent ? (
            <div className="text-center">
              <CheckCircle2 className="mx-auto h-12 w-12 text-green-500" />
              <h3 className="mt-2 text-lg font-medium text-gray-900">Magisk länk skickad!</h3>
              <p className="mt-2 text-sm text-gray-600">
                Vi har skickat en inloggningslänk till <span className="font-semibold">{email}</span>. 
                Klicka på länken i e-postmeddelandet för att logga in automatiskt.
              </p>
              <button
                type="button"
                onClick={resetForm}
                className="mt-4 inline-flex items-center rounded-md bg-blue-50 px-3 py-2 text-sm font-medium text-blue-700 hover:bg-blue-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
              >
                Tillbaka till inloggning
              </button>
            </div>
          ) : (
            <>
              <form className="space-y-6" onSubmit={handleSubmit}>
                {error && (
                  <div className="bg-red-50 border border-red-100 text-red-700 px-4 py-3 rounded-md text-sm flex items-start">
                    <AlertCircle className="h-4 w-4 mr-2 mt-0.5 flex-shrink-0" />
                    <span>{error}</span>
                  </div>
                )}
                
                {loginStatus === 'success' && !error && (
                  <div className="bg-green-50 border border-green-100 text-green-700 px-4 py-3 rounded-md text-sm flex items-center">
                    <CheckCircle2 className="h-4 w-4 mr-2 flex-shrink-0" />
                    <span>Inloggningen lyckades!</span>
                  </div>
                )}
                
                <div>
                  <label htmlFor="email" className="block text-sm font-medium leading-6 text-gray-900">
                    E-postadress
                  </label>
                  <div className="mt-2 relative">
                    <input
                      id="email"
                      name="email"
                      type="email"
                      autoComplete="email"
                      required
                      value={email}
                      onChange={e => setEmail(e.target.value)}
                      className={`block w-full rounded-md border-0 px-3 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ${getInputBorderColor('email')} placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-blue-600 sm:text-sm sm:leading-6 ${loginStatus === 'invalid_email' || (loginStatus === 'empty_fields' && !email) ? 'bg-red-50' : ''}`}
                    />
                    {loginStatus === 'invalid_email' && (
                      <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                        <XCircle className="h-4 w-4 text-red-500" />
                      </div>
                    )}
                  </div>
                  {loginStatus === 'invalid_email' && (
                    <p className="mt-1 text-xs text-red-600">Ogiltig e-postadress</p>
                  )}
                </div>

                <div>
                  <label htmlFor="password" className="block text-sm font-medium leading-6 text-gray-900">
                    Lösenord
                  </label>
                  <div className="mt-2 relative">
                    <input
                      id="password"
                      name="password"
                      type={showPassword ? "text" : "password"}
                      autoComplete="current-password"
                      required
                      value={password}
                      onChange={e => setPassword(e.target.value)}
                      className={`block w-full rounded-md border-0 px-3 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ${getInputBorderColor('password')} placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-blue-600 sm:text-sm sm:leading-6 pr-10 ${loginStatus === 'invalid_credentials' || (loginStatus === 'empty_fields' && !password) ? 'bg-red-50' : ''}`}
                    />
                    <button
                      type="button"
                      onClick={() => setShowPassword(!showPassword)}
                      className="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-400 hover:text-gray-600"
                    >
                      {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                    </button>
                  </div>
                  {loginStatus === 'invalid_credentials' && (
                    <p className="mt-1 text-xs text-red-600">Felaktigt lösenord</p>
                  )}
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <input
                      id="remember-me"
                      name="remember-me"
                      type="checkbox"
                      className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-600"
                    />
                    <label htmlFor="remember-me" className="ml-2 block text-sm text-gray-900">
                      Kom ihåg mig
                    </label>
                  </div>

                  <div className="text-sm">
                    <button 
                      type="button"
                      onClick={() => {
                        setEmail('');
                        setPassword('');
                        setLoginStatus('idle');
                        // Här skulle du implementera logik för att återställa lösenord
                        alert('Funktionen för att återställa lösenord är inte implementerad än.');
                      }}
                      className="font-medium text-blue-600 hover:text-blue-500">
                      Glömt lösenord?
                    </button>
                  </div>
                </div>

                <div>
                  <button
                    type="submit"
                    disabled={isLoading}
                    className={`flex w-full justify-center items-center rounded-md bg-blue-600 px-3 py-2.5 text-sm font-semibold text-white shadow-sm hover:bg-blue-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600 ${isLoading ? 'opacity-70 cursor-not-allowed' : ''}`}
                  >
                    {isLoading ? (
                      <>
                        <Loader2 className="h-4 w-4 animate-spin mr-2" />
                        Signing in...
                      </>
                    ) : loginStatus === 'success' ? (
                      <>
                        <CheckCircle2 className="h-4 w-4 mr-2" />
                        Inloggad!
                      </>
                    ) : (
                      'Sign in'
                    )}
                  </button>
                </div>
              </form>

              <div className="mt-6">
                <div className="relative">
                  <div className="absolute inset-0 flex items-center">
                    <div className="w-full border-t border-gray-300" />
                  </div>
                  <div className="relative flex justify-center text-sm">
                    <span className="bg-white px-2 text-gray-500">Eller fortsätt med</span>
                  </div>
                </div>

                <div className="mt-6">
                  <button
                    type="button"
                    onClick={handleMagicLinkSignIn}
                    disabled={isLoading}
                    className={`flex w-full justify-center items-center rounded-md border border-gray-300 bg-white py-2 px-3 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 ${isLoading ? 'opacity-70 cursor-not-allowed' : ''}`}
                  >
                    {isLoading ? (
                      <>
                        <Loader2 className="h-5 w-5 animate-spin mr-2" />
                        Skickar...
                      </>
                    ) : (
                      <>
                        <svg className="h-5 w-5 text-gray-500 mr-2" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path d="M3 8L10.89 13.26C11.2187 13.4793 11.6049 13.5963 12 13.5963C12.3951 13.5963 12.7813 13.4793 13.11 13.26L21 8M5 19H19C19.5304 19 20.0391 18.7893 20.4142 18.4142C20.7893 18.0391 21 17.5304 21 17V7C21 6.46957 20.7893 5.96086 20.4142 5.58579C20.0391 5.21071 19.5304 5 19 5H5C4.46957 5 3.96086 5.21071 3.58579 5.58579C3.21071 5.96086 3 6.46957 3 7V17C3 17.5304 3.21071 18.0391 3.58579 18.4142C3.96086 18.7893 4.46957 19 5 19Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                        </svg>
                        Magisk länk via e-post
                      </>
                    )}
                  </button>
                </div>
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  );
} 