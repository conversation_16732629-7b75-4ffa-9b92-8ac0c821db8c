const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Helper function to parse Swedish dates
function parseSwedishDate(dateStr) {
  if (!dateStr || dateStr.trim() === '') return null;
  
  // Handle various date formats like "2023-04-30", "230430", etc.
  const cleanDate = dateStr.toString().replace(/\s+/g, '');
  
  if (cleanDate.match(/^\d{6}$/)) {
    // Format: YYMMDD (e.g., "230430")
    const year = '20' + cleanDate.substring(0, 2);
    const month = cleanDate.substring(2, 4);
    const day = cleanDate.substring(4, 6);
    return `${year}-${month}-${day}`;
  } else if (cleanDate.match(/^\d{4}-\d{2}-\d{2}$/)) {
    // Already in correct format
    return cleanDate;
  }
  
  return null;
}

// Helper function to determine project stage from status
function getProjectStage(kalkyl, offert, accept, statusNotes) {
  const lowerStatus = statusNotes.toLowerCase();
  
  if (lowerStatus.includes('slutförd') && lowerStatus.includes('inbetald')) {
    return 'completed_paid';
  } else if (accept === 'Slutförd (inbetald)') {
    return 'completed_paid';
  } else if (accept === 'Ja (pågående)') {
    return 'accepted';
  } else if (accept === 'Nej') {
    return 'rejected';
  } else if (offert === 'Skickad till kund' && accept === 'Nej') {
    return 'rejected';
  } else if (offert === 'Skickad till kund') {
    return 'quote_sent';
  } else if (kalkyl === 'Inskickad' || kalkyl === 'Pågående') {
    return 'calculation_in_progress';
  } else {
    return 'inquiry';
  }
}

// Helper function to extract email from status notes
function extractEmail(statusNotes) {
  const emailRegex = /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/;
  const match = statusNotes.match(emailRegex);
  return match ? match[0] : null;
}

// Helper function to parse address
function parseAddress(addressStr) {
  if (!addressStr) return { address: null, city: null, postal_code: null };
  
  const parts = addressStr.split(',').map(p => p.trim());
  if (parts.length >= 2) {
    return {
      address: parts[0],
      city: parts[1],
      postal_code: null
    };
  } else {
    return {
      address: addressStr,
      city: null,
      postal_code: null
    };
  }
}

// Real customer data from the spreadsheet
const customerData = [
  {
    name: "Marjo Aaltonen",
    address: "Rosenhemsvägen 4",
    phone: "0736137771",
    projectDescription: "",
    siteVisitDate: "",
    kalkyl: "Inskickad",
    offert: "Skickad till kund",
    accept: "Slutförd (inbetald)",
    status: "Accepterat alla 5 offerter, orderbekräftelse skickat 2023-03-15. Projektstart 2023-05-06. Ok att lasta material på deras tomt."
  },
  {
    name: "Sam Alaish",
    address: "Jungfrudansen 36",
    phone: "0736173964",
    projectDescription: "",
    siteVisitDate: "2023-04-30",
    kalkyl: "Inskickad",
    offert: "Skickad till kund",
    accept: "Nej",
    status: "Ny besiktning för fortsatt arbete 230430. Offert skickad"
  },
  {
    name: "Sukanta Stradhar",
    address: "Vickervägen 3",
    phone: "0735238533",
    projectDescription: "",
    siteVisitDate: "2023-03-30",
    kalkyl: "Inskickad",
    offert: "Skickad till kund",
    accept: "Nej",
    status: "Accept. Kund önskar projektstart i slutet av november 2023. Ringt kund 230914 - inget svar. Har pratat med kund och han har ändrat sig."
  },
  {
    name: "Philip Tonelid",
    address: "Granitvägen 12A",
    phone: "0709626748",
    projectDescription: "",
    siteVisitDate: "2023-04-14",
    kalkyl: "Inskickad",
    offert: "Skickad till kund",
    accept: "Slutförd (inbetald)",
    status: "Offert skickad. Accept, Projektstart 2023-05-18, nycklar och material ska levereras 2023-05-17"
  },
  {
    name: "Johanna Mansnèrus",
    address: "Vibyåsen1",
    phone: "0704870246",
    projectDescription: "",
    siteVisitDate: "2023-04-16",
    kalkyl: "Inskickad",
    offert: "Skickad till kund",
    accept: "Nej",
    status: "Besiktad, kalkyl skickad till Rami. Offert skickad."
  },
  // Continue with more customers...
  {
    name: "Lovisa Lansing",
    address: "Ålstensgatan 35,37,39,49 Bromma",
    phone: "0736717771",
    projectDescription: "Stensättning, marksten, 4 radhus",
    siteVisitDate: "2024-03-15",
    kalkyl: "Inskickad",
    offert: "Skickad till kund",
    accept: "Slutförd (inbetald)",
    status: "Platsbesök bokad 240315 kl. 12:30. Kalkyler skickad till Rami 230319. Offert skickad till kund 240323. Kontakt 240326 med Lovisa som ville ha bilder på tidiare utförda jobb. Skickar bilder till kund samt förslag på startdatum. Kund tackar JA till projektet (4st radhus). Vi startar projektet 240429. Orderbekräftelse skickad till Semhar 240401. Orderbekräftelse skickad till kund 240403. Faktuor skickade till Semhar 240530. Faktura skickad till 3 kunder 240531"
  },
  {
    name: "Anna Neubeck",
    address: "Sibyllegatan 17b, Uppsala",
    phone: "0737314884",
    projectDescription: "Bygga altan",
    siteVisitDate: "2024-07-29",
    kalkyl: "Inskickad",
    offert: "Skickad till kund",
    accept: "Ja (pågående)",
    status: "Platsbesök bokad till 240729 kl 11.30. Platsbesök gjord. Kalkyl skickad till Rami 240801. Offert skickad till Semhar 240808. Offert skickad till kund 240809. Kund tackar JA 240813, start för projektet våren -25. Orderbekräftelse skickad till Semhar 240819. Orderbekräftelse skickad till kund 240821"
  },
  {
    name: "Ann Svärdh",
    address: "Hagvägen 31, Huddinge",
    phone: "0730370903",
    projectDescription: "Fasadmålning",
    siteVisitDate: "2024-11-12",
    kalkyl: "Inskickad",
    offert: "Skickad till kund",
    accept: "Ja (pågående)",
    status: "Platsbesök bokad tiil 241112 kl 16.30. Kalkyl skickad till Rami 241114. Offert skickad till Semhar 241118. Offert skickad till kund 241119. Reviderad offert skickad till kund 241127. Kund tackar JA och orderbekräftelse med start datum 1 april är skickad till kund"
  }
];

// Extended customer data - let me create a more comprehensive dataset
const extendedCustomerData = [
  // ... (Previous customers) ...
  {
    name: "Sofia Kleban",
    address: "Beckomberga Allé 6",
    phone: "",
    projectDescription: "Köksrenovering, lagning av tak",
    siteVisitDate: "",
    kalkyl: "Inskickad",
    offert: "Skickad till kund",
    accept: "Ja (pågående)",
    status: "Kristoffer sköter denna kontakt"
  },
  {
    name: "Ann Burton Gerhardt",
    address: "Tomtebogatan 5B, Stockholm",
    phone: "0733983872",
    projectDescription: "Bygga in paxgarderober samt målning av väggar i sovrum",
    siteVisitDate: "2025-02-11",
    kalkyl: "Inskickad",
    offert: "Skickad till kund",
    accept: "Ja (pågående)",
    status: "Kristoffer sköter denna kontakt. det blir bara bygga in pax garderober. Reviderad offert skickad till kund 250227"
  }
];

async function importCustomers() {
  console.log('Starting customer data import...');
  
  try {
    // Clear existing test customers (optional - comment out if you want to keep them)
    console.log('Clearing existing test customers...');
    await supabase
      .from('customers')
      .delete()
      .like('name', '%Test%');

    let imported = 0;
    let skipped = 0;

    for (const customerData of customerData) {
      try {
        // Parse address
        const addressInfo = parseAddress(customerData.address);
        
        // Extract email from status notes
        const email = extractEmail(customerData.status || '');
        
        // Determine project stage
        const projectStage = getProjectStage(
          customerData.kalkyl,
          customerData.offert, 
          customerData.accept,
          customerData.status
        );

        // Parse site visit date
        const siteVisitDate = parseSwedishDate(customerData.siteVisitDate);

        // Create customer record
        const customerRecord = {
          name: customerData.name,
          is_company: false,
          email: email,
          phone: customerData.phone || null,
          address: addressInfo.address,
          city: addressInfo.city,
          country: 'Sweden',
          project_description: customerData.projectDescription || null,
          site_visit_date: siteVisitDate,
          calculation_status: customerData.kalkyl || 'not_started',
          quote_status: customerData.offert || 'not_sent',
          acceptance_status: customerData.accept || 'pending',
          project_status_notes: customerData.status || '',
          project_stage: projectStage,
          status: 'active',
          source: 'imported_from_excel'
        };

        // Insert customer
        const { data: customer, error: customerError } = await supabase
          .from('customers')
          .insert(customerRecord)
          .select()
          .single();

        if (customerError) {
          console.error(`Error inserting customer ${customerData.name}:`, customerError);
          skipped++;
          continue;
        }

        console.log(`✅ Imported customer: ${customerData.name}`);
        imported++;

        // If there's a project description and the project stage indicates work, create a project
        if (customerData.projectDescription && ['accepted', 'completed_paid', 'quote_sent'].includes(projectStage)) {
          const projectRecord = {
            name: customerData.projectDescription,
            description: customerData.status,
            customer_id: customer.id,
            start_date: siteVisitDate,
            status: projectStage === 'completed_paid' ? 'completed' : 
                   projectStage === 'accepted' ? 'in_progress' : 'planning',
            priority: 'medium',
            notes: customerData.status
          };

          const { error: projectError } = await supabase
            .from('projects')
            .insert(projectRecord);

          if (projectError) {
            console.error(`Error creating project for ${customerData.name}:`, projectError);
          } else {
            console.log(`  📋 Created project for ${customerData.name}`);
          }
        }

        // Small delay to avoid rate limiting
        await new Promise(resolve => setTimeout(resolve, 100));

      } catch (error) {
        console.error(`Error processing customer ${customerData.name}:`, error);
        skipped++;
      }
    }

    console.log(`\n🎉 Import completed!`);
    console.log(`✅ Successfully imported: ${imported} customers`);
    console.log(`⚠️  Skipped: ${skipped} customers`);
    
  } catch (error) {
    console.error('Fatal error during import:', error);
  }
}

// Run the import
if (require.main === module) {
  importCustomers();
}

module.exports = { importCustomers }; 