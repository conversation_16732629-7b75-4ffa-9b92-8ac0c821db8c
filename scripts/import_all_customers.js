const { createClient } = require('@supabase/supabase-js');
const { allCustomersData } = require('./complete_customer_data');

// Initialize Supabase client - using anon key for now
const supabaseUrl = 'https://cmclcknwvktlecjwxqrb.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImNtY2xja253dmt0bGVjand4cXJiIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDcyMzYyMTIsImV4cCI6MjA2MjgxMjIxMn0.5E73uhToiQIl8qnjt6Pv22425epuVK6P3QZ5Hiu00_0';

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('Missing required Supabase configuration');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Helper function to parse Swedish dates
function parseSwedishDate(dateStr) {
  if (!dateStr || dateStr.trim() === '') return null;
  
  // Handle various date formats like "2023-04-30", "230430", etc.
  const cleanDate = dateStr.toString().replace(/\s+/g, '');
  
  if (cleanDate.match(/^\d{6}$/)) {
    // Format: YYMMDD (e.g., "230430")
    const year = '20' + cleanDate.substring(0, 2);
    const month = cleanDate.substring(2, 4);
    const day = cleanDate.substring(4, 6);
    return `${year}-${month}-${day}`;
  } else if (cleanDate.match(/^\d{4}-\d{2}-\d{2}$/)) {
    // Already in correct format
    return cleanDate;
  }
  
  return null;
}

// Helper function to determine project stage from status
function getProjectStage(kalkyl, offert, accept, statusNotes) {
  const lowerStatus = statusNotes.toLowerCase();
  
  if (lowerStatus.includes('slutförd') && lowerStatus.includes('inbetald')) {
    return 'completed_paid';
  } else if (accept === 'Slutförd (inbetald)') {
    return 'completed_paid';
  } else if (accept === 'Ja (pågående)') {
    return 'accepted';
  } else if (accept === 'Nej') {
    return 'rejected';
  } else if (offert === 'Skickad till kund' && accept === 'Nej') {
    return 'rejected';
  } else if (offert === 'Skickad till kund') {
    return 'quote_sent';
  } else if (kalkyl === 'Inskickad' || kalkyl === 'Pågående') {
    return 'calculation_in_progress';
  } else {
    return 'inquiry';
  }
}

// Helper function to extract email from status notes
function extractEmail(statusNotes) {
  const emailRegex = /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/;
  const match = statusNotes.match(emailRegex);
  return match ? match[0] : null;
}

// Helper function to parse address
function parseAddress(addressStr) {
  if (!addressStr) return { address: null, city: null, postal_code: null };
  
  const parts = addressStr.split(',').map(p => p.trim());
  if (parts.length >= 2) {
    return {
      address: parts[0],
      city: parts[1],
      postal_code: null
    };
  } else {
    return {
      address: addressStr,
      city: null,
      postal_code: null
    };
  }
}

async function importAllCustomers() {
  console.log('🚀 Starting comprehensive customer data import...');
  console.log(`📊 Total customers to import: ${allCustomersData.length}`);
  
  try {
    // Clear existing imported customers (optional - comment out if you want to keep them)
    console.log('🧹 Clearing existing imported customers...');
    const { error: deleteError } = await supabase
      .from('customers')
      .delete()
      .eq('source', 'imported_from_excel');

    if (deleteError) {
      console.warn('Warning: Could not clear existing customers:', deleteError.message);
    }

    let imported = 0;
    let skipped = 0;
    let errors = [];

    for (let i = 0; i < allCustomersData.length; i++) {
      const customerData = allCustomersData[i];
      
      try {
        // Parse address
        const addressInfo = parseAddress(customerData.address);
        
        // Extract email from status notes
        const email = extractEmail(customerData.status || '');
        
        // Determine project stage
        const projectStage = getProjectStage(
          customerData.kalkyl,
          customerData.offert, 
          customerData.accept,
          customerData.status
        );

        // Parse site visit date
        const siteVisitDate = parseSwedishDate(customerData.siteVisitDate);

        // Create customer record
        const customerRecord = {
          name: customerData.name,
          is_company: false,
          email: email,
          phone: customerData.phone || null,
          address: addressInfo.address,
          city: addressInfo.city,
          country: 'Sweden',
          project_description: customerData.projectDescription || null,
          site_visit_date: siteVisitDate,
          calculation_status: customerData.kalkyl || 'not_started',
          quote_status: customerData.offert || 'not_sent',
          acceptance_status: customerData.accept || 'pending',
          project_status_notes: customerData.status || '',
          project_stage: projectStage,
          status: 'active',
          source: 'imported_from_excel'
        };

        // Insert customer
        const { data: customer, error: customerError } = await supabase
          .from('customers')
          .insert(customerRecord)
          .select()
          .single();

        if (customerError) {
          console.error(`❌ Error inserting customer ${customerData.name}:`, customerError.message);
          errors.push({ name: customerData.name, error: customerError.message });
          skipped++;
          continue;
        }

        console.log(`✅ (${i + 1}/${allCustomersData.length}) Imported: ${customerData.name} [${projectStage}]`);
        imported++;

        // If there's a project description and the project stage indicates work, create a project
        if (customerData.projectDescription && ['accepted', 'completed_paid', 'quote_sent'].includes(projectStage)) {
          const projectRecord = {
            name: customerData.projectDescription,
            description: customerData.status,
            customer_id: customer.id,
            start_date: siteVisitDate,
            status: projectStage === 'completed_paid' ? 'completed' : 
                   projectStage === 'accepted' ? 'in_progress' : 'planning',
            priority: 'medium',
            notes: customerData.status
          };

          const { error: projectError } = await supabase
            .from('projects')
            .insert(projectRecord);

          if (projectError) {
            console.warn(`⚠️  Project creation failed for ${customerData.name}:`, projectError.message);
          } else {
            console.log(`  📋 Created project: ${customerData.projectDescription}`);
          }
        }

        // Small delay to avoid overwhelming the database
        if (i % 10 === 0) {
          await new Promise(resolve => setTimeout(resolve, 200));
        }

      } catch (error) {
        console.error(`💥 Fatal error processing customer ${customerData.name}:`, error);
        errors.push({ name: customerData.name, error: error.message });
        skipped++;
      }
    }

    console.log('\n🎉 Import completed!');
    console.log(`✅ Successfully imported: ${imported} customers`);
    console.log(`⚠️  Skipped: ${skipped} customers`);

    if (errors.length > 0) {
      console.log('\n❌ Errors encountered:');
      errors.forEach(err => {
        console.log(`  - ${err.name}: ${err.error}`);
      });
    }

    // Generate summary statistics
    const { data: completedCount } = await supabase
      .from('customers')
      .select('id', { count: 'exact' })
      .eq('project_stage', 'completed_paid')
      .eq('source', 'imported_from_excel');

    const { data: activeCount } = await supabase
      .from('customers')
      .select('id', { count: 'exact' })
      .eq('project_stage', 'accepted')
      .eq('source', 'imported_from_excel');

    const { data: quoteSentCount } = await supabase
      .from('customers')
      .select('id', { count: 'exact' })
      .eq('project_stage', 'quote_sent')
      .eq('source', 'imported_from_excel');

    console.log('\n📊 Import Summary:');
    console.log(`🎯 Completed Projects: ${completedCount?.length || 0}`);
    console.log(`🔄 Active Projects: ${activeCount?.length || 0}`);
    console.log(`📄 Quotes Sent: ${quoteSentCount?.length || 0}`);
    
  } catch (error) {
    console.error('💥 Fatal error during import:', error);
    process.exit(1);
  }
}

// Run the import
if (require.main === module) {
  importAllCustomers()
    .then(() => {
      console.log('\n✨ Customer import process completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Import process failed:', error);
      process.exit(1);
    });
}

module.exports = { importAllCustomers }; 