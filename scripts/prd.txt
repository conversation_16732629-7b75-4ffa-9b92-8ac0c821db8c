# Product Requirements Document: Supabase Integration for SSP (Svea Service Partner)

## 1. Projektbeskrivning

Svea Service Partner (SSP) är en applikation för byggföretag som behöver en backend-lösning för att hantera offerter, projekt, fakturor och anställda. Detta projekt handlar om att implementera Supabase som backend-tjänst för att lagra, hantera och synkronisera applikationsdata. Vi kommer att använda Supabase CLI för att hantera databasscheman, migrationer och autentisering.

## 2. Bakgrund och Mål

SSP har idag en fungerande frontend i Next.js med olika moduler för offerter, fakturor, projekthantering och anställda. Applikationen använder för närvarande hårdkodad data och saknar persistent datalagring, autentisering och användarhantering. 

Målet med detta projekt är att:
1. Implementera en robust databaslösning med Supabase
2. Etablera en säker autentiseringslösning med superuser-access
3. Skapa backend-tjänster för alla nuvarande frontend-moduler
4. Förbereda strukturen för framtida funktioner och skalning

## 3. Målgrupp och Användare

SSP riktar sig primärt till små- och medelstora byggföretag i Sverige som behöver digitalisera och effektivisera sina administrativa processer. Användarna inkluderar:

- Projektledare och förmän som behöver skapa och hantera offerter
- Administratörer som hanterar fakturering och ekonomi
- Verksamhetschefer som behöver översikt över resurser och projekt
- Anställda som rapporterar tid och arbetsmoment

## 4. Funktionella Krav

### 4.1 Autentisering och Användarhantering
- Implementera inloggningssystem via Supabase Auth
- Alla användare har superuser-behörighet med full åtkomst till systemet
- Hantering av användaruppgifter och profilinformation
- Lösenordsåterställning och säker autentisering
- Magic link-inloggning för enkel åtkomst

### 4.2 Databasmodeller och Relationer
Följande datamodeller ska implementeras:

**Användare (auth.users tillägg):**
- Profil (namn, kontaktinformation, avdelning)
- Kompetensprofil och tillgänglighet

**Kunder:**
- Kunduppgifter (företag/privatperson)
- Kontaktpersoner
- Adressinformation
- Faktureringsuppgifter
- Projekthistorik

**Projekt:**
- Grundinformation (namn, beskrivning, deadline)
- Status och framsteg
- Budget och kostnadsinformation
- Koppling till kunder och offerter
- Tilldelade resurser/anställda

**Offerter:**
- Versionshantering
- Status (utkast, skickad, accepterad, avböjd)
- Arbetsmoment med kostnadsuppskattning
- Material och tillval
- ROT-avdragshantering
- Koppling till kunder och eventuella projekt

**Fakturor:**
- Fakturainformation (nummer, datum, förfallodag)
- Status (utestående, förfallen, betald)
- Kopplade fakturaposter
- Betalningshistorik
- Koppling till projekt och kunder

**Tidrapportering:**
- Anställdas arbetstid per projekt
- Typ av arbete
- Godkännande av tid

### 4.3 API och Dataåtkomst
- Implementera säkra Supabase CRUD-operationer för alla modeller
- Alla användare har full tillgång till all data (superuser-behörighet)
- Implementera realtidsprenumerationer för kritiska datauppdateringar

### 4.4 Integritets- och Datavalidering
- Databas-constraints för datavalidering
- Säker hantering av personuppgifter enligt GDPR
- Schemavalidering på klient- och serversidan

## 5. Icke-funktionella Krav

### 5.1 Prestanda
- Optimerade databasindex för snabba sökningar
- Begränsad datahämtning för att minimera laddningstider

### 5.2 Säkerhet
- Enkel säkerhetsmodell där alla användare har superuser-behörighet
- Grundläggande autentisering för att skydda systemet från obehörig åtkomst
- Säker hantering av API-nycklar och miljövariabler

### 5.3 Skalbarhet
- Designa databasschema för framtida tillväxt
- Strukturera kod för enkel utökning av funktionalitet

### 5.4 Tillförlitlighet
- Implementera felhantering och loggning
- Automatiska uppdateringar via Supabase hooks

## 6. Tekniska Specifikationer

### 6.1 Teknologier
- **Supabase**: Primär backend-tjänst för databas, autentisering och fillagring
- **Supabase CLI**: För migrationer, schemahantering och lokal utveckling
- **Next.js**: Frontend-ramverk, redan implementerat
- **TypeScript**: För typdefinierade datamodeller
- **Prisma/Supabase-js**: För databasåtkomst från applikationen

### 6.2 Arkitektur
- **Server-side rendering**: För primär datahantering och rendering
- **Realtidsuppdateringar**: För kollaborativa funktioner
- **Progressive Web App (PWA)**: För mobilanpassning

## 7. Leveranser

### 7.1 Utvecklingsfaser
1. **Fas 1: Setup och Konfiguration**
   - Konfigurera Supabase-projekt
   - Installera och konfigurera Supabase CLI
   - Upprätta lokal utvecklingsmiljö

2. **Fas 2: Databasdesign och Migrationer**
   - Definiera tabeller, relationer och constraints
   - Skapa och testa migrationer

3. **Fas 3: Autentisering och Användarhantering**
   - Implementera inloggning/utloggning
   - Skapa profilhantering med superuser-åtkomst

4. **Fas 4: Backend-tjänster**
   - Implementera API-endpoints för alla moduler
   - Skapa CRUD-operationer
   - Testa dataflöden

5. **Fas 5: Frontend-integration**
   - Anpassa frontend för att arbeta mot Supabase
   - Implementera autentiseringsflöden
   - Konvertera hårdkodad data till databasutfrågningar

6. **Fas 6: Testning och Kvalitetssäkring**
   - Utför omfattande testning
   - Åtgärda buggar och problem
   - Optimera prestanda

### 7.2 Milstolpar
1. Supabase-projekt konfigurerat och anslutet till utvecklingsmiljö
2. Databasschema definierat och migrerat
3. Autentisering implementerad och fungerande
4. Alla huvudmoduler (offerter, fakturor, projekt, anställda) anslutna till backend
5. Testning och dokumentation slutförd

## 8. Begränsningar och Antaganden

### 8.1 Begränsningar
- Supabase Free/Pro-plan kommer att användas, med dess begränsningar i datalagring och bandbredd
- Initialt stöd endast för svenska språket
- Fokus på webbgränssnitt, mobiloptimering kommer i senare fas

### 8.2 Antaganden
- Existerande frontend-kod kan anpassas utan större omskrivningar
- Utvecklingsteamet har grundläggande kunskaper om Supabase och PostgreSQL
- Slutanvändare har stabil internetuppkoppling

## 9. Acceptanskriterier

1. Användare kan registrera sig, logga in och hantera sina profiler med superuser-behörighet
2. Data persisterar korrekt i Supabase och är tillgänglig mellan sessioner
3. Alla funktioner i nuvarande frontend är fullt operativa med databasen
4. Prestandan är jämförbar med eller bättre än den nuvarande lösningen
5. Systemet uppfyller GDPR-krav för datalagring och hantering

## 10. Riskanalys och Åtgärder

| Risk | Sannolikhet | Påverkan | Åtgärd |
|------|-------------|----------|--------|
| Datamigrationsutmaningar | Medel | Hög | Testa migrationer i förväg, ha backup-strategi |
| Prestandaproblem med komplexa frågor | Låg | Medel | Optimera databasschema, använda index |
| Säkerhetsrisker med superuser-modellen | Medel | Hög | Implementera stark autentisering och logging |
| Kompabilitetsproblem med befintlig kod | Medel | Medel | Stegvis integration och omfattande testning |

## 11. Framtida Möjligheter

- Integration med ekonomisystem (Fortnox, Visma)
- Mobil app för fältanvändning
- Avancerad analys och rapportering
- AI-assisterad offertgenerering
- Integrerad fakturahantering med betalningslösning
- Implementering av rollbaserad behörighetskontroll vid behov i framtiden

---

Detta dokument fungerar som en övergripande guide för implementering av Supabase som backend för SSP-applikationen. Detaljerade tekniska specifikationer och designdokument kommer att utvecklas under projektets gång.

# Customer Management Module PRD

## Background
Modus needs a customer management system to store and manage customer information, including both companies and individuals. The system will allow users to create, view, edit, and delete customers and their associated contacts.

## Requirements

### Database Schema
- We already have a migration file (20240512000003_customer_contact_schema.sql) that creates the necessary database tables:
  - `customers` table with fields for name, company details, contact info, and status
  - `customer_contacts` table for individual contacts at customer companies
- The schema includes row-level security policies for proper access control
- The database error "relation \"public.customers\" does not exist" indicates we need to apply this migration to the production database

### UI Components
- Implement customer listing page at /dashboard/customers
- Create customer detail view at /dashboard/customers/[id]
- Add new customer creation form at /dashboard/customers/new
- Implement contact management for each customer
- Add new contact creation form at /dashboard/customers/[id]/contacts/new

### Features
- Allow filtering and searching customers by name, status, etc.
- Support pagination for customer listings
- Provide forms with validation for creating and editing customers and contacts
- Integrate with Supabase for data storage and retrieval
- Implement proper error handling and loading states

### Technical Considerations
- Use Next.js for all page implementations
- Utilize Supabase client for database operations
- Follow existing design patterns and UI components from the project
- Ensure all forms include proper validation
- Apply appropriate access control based on user roles

## Success Criteria
- Users can create, view, edit, and delete customers and contacts
- The system properly enforces access control based on user roles
- All forms validate user input and provide appropriate feedback
- Data is persisted correctly in the Supabase database
- UI is responsive and follows the existing design system 