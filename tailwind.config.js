/** @type {import('tailwindcss').Config} */
module.exports = {
  darkMode: ["class"],
  content: [
    './pages/**/*.{js,ts,jsx,tsx,mdx}',
    './components/**/*.{js,ts,jsx,tsx,mdx}',
    './app/**/*.{js,ts,jsx,tsx,mdx}',
    './src/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      fontFamily: {
        'roboto': ['var(--font-roboto)', 'Roboto', 'sans-serif'],
      },
      colors: {
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        primary: {
          DEFAULT: "hsl(var(--primary))",
          foreground: "hsl(var(--primary-foreground))",
        },
        secondary: {
          DEFAULT: "hsl(var(--secondary))",
          foreground: "hsl(var(--secondary-foreground))",
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        accent: {
          DEFAULT: "hsl(var(--accent))",
          foreground: "hsl(var(--accent-foreground))",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },
        // Tremor colors (for BadgeDelta component)
        "dark-tremor-background": "#0F172A",
        "tremor-background": "white",
        "dark-tremor-background-subtle": "#1E293B",
        "tremor-background-subtle": "#F9FAFB",
        "dark-tremor-content": "#9CA3AF",
        "tremor-content": "#6B7280",
        "dark-tremor-content-emphasis": "#F9FAFB",
        "tremor-content-emphasis": "#111827",
        "dark-tremor-label": "#F3F4F6",
        "tremor-label": "#374151",
      },
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
        "tremor-small": "0.375rem",
        "tremor-default": "0.5rem",
        "tremor-full": "9999px",
      },
      keyframes: {
        "accordion-down": {
          from: { height: 0 },
          to: { height: "var(--radix-accordion-content-height)" },
        },
        "accordion-up": {
          from: { height: "var(--radix-accordion-content-height)" },
          to: { height: 0 },
        },
      },
      animation: {
        "accordion-down": "accordion-down 0.2s ease-out",
        "accordion-up": "accordion-up 0.2s ease-out",
      },
      backdropFilter: {
        'none': 'none',
        'blur': 'blur(20px)',
      },
    },
  },
  plugins: [
    require("tailwindcss-animate"),
    // Add plugin for backdrop filters
    function ({ addUtilities }) {
      const newUtilities = {
        '.backdrop-blur-sm': {
          backdropFilter: 'blur(4px)',
        },
        '.backdrop-blur': {
          backdropFilter: 'blur(8px)',
        },
        '.backdrop-blur-md': {
          backdropFilter: 'blur(12px)',
        },
        '.backdrop-blur-lg': {
          backdropFilter: 'blur(16px)',
        },
        '.backdrop-blur-xl': {
          backdropFilter: 'blur(24px)',
        },
        '.backdrop-blur-2xl': {
          backdropFilter: 'blur(40px)',
        },
        '.backdrop-blur-3xl': {
          backdropFilter: 'blur(64px)',
        },
        '.backdrop-saturate-0': {
          backdropFilter: 'saturate(0)',
        },
        '.backdrop-saturate-50': {
          backdropFilter: 'saturate(0.5)',
        },
        '.backdrop-saturate-100': {
          backdropFilter: 'saturate(1)',
        },
        '.backdrop-saturate-150': {
          backdropFilter: 'saturate(1.5)',
        },
        '.backdrop-saturate-200': {
          backdropFilter: 'saturate(2)',
        },
      };
      addUtilities(newUtilities, ['responsive', 'hover']);
    },
  ],
} 