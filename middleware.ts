import { createServerClient } from '@supabase/ssr'
import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

/**
 * Handle authentication in middleware
 * This runs on every request to protect routes and manage auth state
 */
export async function middleware(req: NextRequest) {
  // Create a response to modify
  const res = NextResponse.next()

  try {
    // Create a Supabase client specifically for this middleware request
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name) {
            return req.cookies.get(name)?.value
          },
          set(name, value, options) {
            req.cookies.set({
              name,
              value,
              ...options,
            })
            // Update the response cookies
            res.cookies.set({
              name,
              value,
              ...options,
            })
          },
          remove(name, options) {
            const cookieOptions = {
              name,
              value: '',
              ...options,
            }
            req.cookies.set(cookieOptions)
            // Update the response cookies
            res.cookies.set(cookieOptions)
          },
        },
      }
    )
    
    // Get the current session
    const { data: { session } } = await supabase.auth.getSession()
    
    // Only log for non-static resources and important paths
    const shouldLog = !req.nextUrl.pathname.startsWith('/_next') && 
                      !req.nextUrl.pathname.includes('.') &&
                      req.nextUrl.pathname !== '/'
    
    if (shouldLog) {
      console.log(`[Middleware] Path: ${req.nextUrl.pathname}, Session: ${!!session}`)
    }
    
    // Auth routes (login, auth callbacks, etc.)
    const isAuthRoute = req.nextUrl.pathname.startsWith('/login') ||
                        req.nextUrl.pathname.startsWith('/auth') ||
                        req.nextUrl.pathname === '/login'
    
    // Public routes that don't require authentication
    const isPublicRoute = req.nextUrl.pathname === '/' ||
                         req.nextUrl.pathname.startsWith('/api/public') ||
                         req.nextUrl.pathname.startsWith('/api/') ||
                         req.nextUrl.pathname.startsWith('/test-auth')
    
    // Protected routes logic
    if (!session && !isAuthRoute && !isPublicRoute) {
      if (shouldLog) {
        console.log(`[Middleware] No session, redirecting to login from: ${req.nextUrl.pathname}`)
      }
      const redirectUrl = new URL('/login', req.url)

      // Save the requested URL to redirect back after login (include search params)
      const originalUrl = req.nextUrl.pathname + req.nextUrl.search
      redirectUrl.searchParams.set('redirectedFrom', originalUrl)

      return NextResponse.redirect(redirectUrl)
    }
    
    // Prevent authenticated users from accessing auth routes
    if (session && isAuthRoute) {
      if (shouldLog) {
        console.log(`[Middleware] Session exists and on auth route, redirecting to dashboard`)
      }
      
      // If there's a specific redirect URL in the query, use that instead
      const redirectTo = req.nextUrl.searchParams.get('redirectedFrom') || '/dashboard'
      const redirectUrl = new URL(redirectTo, req.url)
      
      return NextResponse.redirect(redirectUrl)
    }
    
    // Return the response with the session cookie
    return res
  } catch (error) {
    console.error('[Middleware] Error:', error)
    
    // If anything goes wrong, just let the request through
    // but don't redirect, to avoid infinite redirect loops
    return res
  }
}

// Specify which paths this middleware will run on
export const config = {
  matcher: [
    /*
     * Match all request paths except:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    '/((?!_next/static|_next/image|favicon.ico|public).*)',
  ],
} 