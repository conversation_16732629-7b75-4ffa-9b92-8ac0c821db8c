// Unit tests for service classes
import { userService, customerService, projectService, estimateService, employeeService } from '@/services';

describe('Service Classes', () => {
  describe('UserService', () => {
    it('should be properly instantiated', () => {
      expect(userService).toBeDefined();
      expect(typeof userService.getCurrentUser).toBe('function');
      expect(typeof userService.updateUserProfile).toBe('function');
      expect(typeof userService.signOut).toBe('function');
    });
  });

  describe('CustomerService', () => {
    it('should be properly instantiated', () => {
      expect(customerService).toBeDefined();
      expect(typeof customerService.getAllCustomers).toBe('function');
      expect(typeof customerService.getCustomerById).toBe('function');
      expect(typeof customerService.createCustomer).toBe('function');
      expect(typeof customerService.updateCustomer).toBe('function');
      expect(typeof customerService.deleteCustomer).toBe('function');
    });
  });

  describe('ProjectService', () => {
    it('should be properly instantiated', () => {
      expect(projectService).toBeDefined();
      expect(typeof projectService.getAllProjects).toBe('function');
      expect(typeof projectService.getProjectById).toBe('function');
      expect(typeof projectService.createProject).toBe('function');
      expect(typeof projectService.updateProject).toBe('function');
      expect(typeof projectService.deleteProject).toBe('function');
      expect(typeof projectService.getProjectsByStatus).toBe('function');
    });
  });

  describe('EstimateService', () => {
    it('should be properly instantiated', () => {
      expect(estimateService).toBeDefined();
      expect(typeof estimateService.getAllEstimates).toBe('function');
      expect(typeof estimateService.getEstimateById).toBe('function');
      expect(typeof estimateService.createEstimate).toBe('function');
      expect(typeof estimateService.updateEstimate).toBe('function');
      expect(typeof estimateService.deleteEstimate).toBe('function');
      expect(typeof estimateService.getEstimateLineItems).toBe('function');
    });
  });

  describe('EmployeeService', () => {
    it('should be properly instantiated', () => {
      expect(employeeService).toBeDefined();
      expect(typeof employeeService.getAllEmployees).toBe('function');
      expect(typeof employeeService.getEmployeeById).toBe('function');
      expect(typeof employeeService.createEmployee).toBe('function');
      expect(typeof employeeService.updateEmployee).toBe('function');
      expect(typeof employeeService.deleteEmployee).toBe('function');
      expect(typeof employeeService.getEmployeesByDepartment).toBe('function');
    });
  });
}); 