# Testing Structure

This directory contains all tests for the application, organized by test type:

## Directory Structure

```
tests/
├── unit/          # Unit tests for individual components and functions
├── integration/   # Integration tests for API routes and database operations
├── e2e/          # End-to-end tests for complete user workflows
└── README.md     # This file
```

## Testing Guidelines

### Unit Tests (`/unit`)
- Test individual components in isolation
- Test utility functions and hooks
- Use Jest and React Testing Library
- Mock external dependencies

### Integration Tests (`/integration`)
- Test API routes and database operations
- Test component interactions
- Use real database connections (test database)
- Test authentication flows

### End-to-End Tests (`/e2e`)
- Test complete user workflows
- Use Playwright or Cypress
- Test critical user paths
- Test across different browsers

## Running Tests

```bash
# Run all tests
npm test

# Run unit tests only
npm run test:unit

# Run integration tests only
npm run test:integration

# Run e2e tests only
npm run test:e2e

# Run tests in watch mode
npm run test:watch

# Generate coverage report
npm run test:coverage
```

## Test Naming Conventions

- Unit tests: `ComponentName.test.tsx` or `functionName.test.ts`
- Integration tests: `api-route-name.integration.test.ts`
- E2E tests: `user-workflow.e2e.test.ts`

## Best Practices

1. **Arrange, Act, Assert**: Structure tests clearly
2. **Descriptive test names**: Use clear, descriptive test names
3. **Test behavior, not implementation**: Focus on what the code does
4. **Mock external dependencies**: Keep tests isolated
5. **Clean up after tests**: Ensure tests don't affect each other
6. **Test edge cases**: Include error scenarios and edge cases 