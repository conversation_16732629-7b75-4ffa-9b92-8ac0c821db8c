# Modus - Business Management Platform

A modern business management platform built with Next.js 13+, TypeScript, and Supabase.

## 🏗️ Project Structure

```
modus-v0-design/
├── app/                          # Next.js 13+ App Router
│   ├── (auth)/                   # Authentication route group
│   │   ├── auth/                 # Auth provider and callbacks
│   │   └── login/                # Login page
│   ├── (dashboard)/              # Dashboard route group
│   │   └── dashboard/            # Main dashboard and sub-pages
│   ├── api/                      # API routes
│   ├── globals/                  # Global styles and assets
│   ├── layout.tsx                # Root layout
│   └── page.tsx                  # Home page
├── components/                   # Reusable UI components (consolidated)
│   ├── ui/                       # Base UI components
│   ├── estimates/                # Estimate-specific components
│   ├── projects/                 # Project-specific components
│   ├── employees/                # Employee-specific components
│   └── theme-provider.tsx        # Theme provider
├── lib/                          # Utility libraries
│   ├── supabase/                 # Supabase client and utilities
│   ├── hooks/                    # Custom React hooks
│   ├── types/                    # TypeScript type definitions
│   ├── constants/                # Application constants
│   ├── data/                     # Data utilities and helpers
│   └── utils.ts                  # General utility functions
├── models/                       # Data models and interfaces
│   └── index.ts                  # Centralized model exports
├── services/                     # API services and business logic
│   ├── userService.ts            # User-related API calls
│   ├── customerService.ts        # Customer-related API calls
│   ├── projectService.ts         # Project-related API calls
│   ├── estimateService.ts        # Estimate-related API calls
│   ├── employeeService.ts        # Employee-related API calls
│   └── index.ts                  # Service exports
├── context/                      # React Context providers
│   ├── AuthContext.tsx           # Authentication context
│   ├── ThemeContext.tsx          # Theme context
│   ├── EstimatesContext.tsx      # Estimates context
│   └── index.ts                  # Context exports
├── app/globals/                  # Global styles and assets (consolidated)
│   ├── globals.css               # Main stylesheet with Tailwind, toast styles, and CSS variables
│   └── google-theme.css          # Google-style component themes
├── tests/                        # Test files organized by type
│   ├── unit/                     # Unit tests
│   ├── integration/              # Integration tests
│   ├── e2e/                      # End-to-end tests
│   └── README.md                 # Testing guidelines
├── public/                       # Static assets
└── supabase/                     # Supabase configuration
```

## 🚀 Getting Started

### Prerequisites

- Node.js 18+ 
- npm or yarn
- Supabase account

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd modus-v0-design
```

2. Install dependencies:
```bash
npm install
```

3. Set up environment variables:
```bash
cp .env.example .env.local
```

Fill in your Supabase credentials:
```env
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
```

4. Run the development server:
```bash
npm run dev
```

5. Open [http://localhost:3000](http://localhost:3000) in your browser.

## 📁 Architecture Decisions

### Route Groups
- `(auth)`: Contains all authentication-related pages
- `(dashboard)`: Contains the main application dashboard and features

### Component Organization
- **UI Components**: Base components in `components/ui/`
- **Feature Components**: Organized by domain (estimates, projects, employees)
- **Shared Components**: Common components used across features

### Service Layer Architecture
- **Services**: Business logic and API calls organized by domain
- **Models**: TypeScript interfaces and data models
- **Context**: React Context providers for global state management

### Library Structure
- **Supabase**: All database-related code in `lib/supabase/`
- **Types**: Centralized TypeScript definitions in `lib/types/`
- **Hooks**: Custom React hooks in `lib/hooks/`
- **Constants**: Application constants in `lib/constants/`

### Testing Strategy
- **Unit Tests**: Individual component and function testing
- **Integration Tests**: API routes and database operations
- **E2E Tests**: Complete user workflow testing

## 🛠️ Development Guidelines

### File Naming
- Use kebab-case for files and directories
- Use PascalCase for React components
- Use camelCase for functions and variables

### Import Organization
1. React and Next.js imports
2. Third-party library imports
3. Internal component imports
4. Service and model imports
5. Utility and type imports

### Component Structure
```typescript
// External imports
import React from 'react'
import { NextPage } from 'next'

// Internal imports
import { Button } from '@/components/ui/button'
import { useAuth } from '@/context/AuthContext'
import { customerService } from '@/services/customerService'
import { Customer } from '@/models'

// Component definition
export const CustomerList: React.FC<Props> = ({ customers }) => {
  // Component logic
}
```

### Service Usage
```typescript
// Using services for API calls
import { customerService } from '@/services'

const customers = await customerService.getAllCustomers()
const customer = await customerService.getCustomerById(id)
```

## 🔧 Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint
- `npm test` - Run all tests
- `npm run test:unit` - Run unit tests
- `npm run test:integration` - Run integration tests
- `npm run test:e2e` - Run end-to-end tests

## 📦 Key Dependencies

- **Next.js 15** - React framework
- **TypeScript** - Type safety
- **Tailwind CSS** - Styling
- **Supabase** - Backend and authentication
- **Radix UI** - Accessible UI components
- **Framer Motion** - Animations
- **React Hook Form** - Form handling

## 🎨 UI Components

The project uses a custom design system built on top of Radix UI primitives:

- **Google-style Components**: Modern, clean design language
- **Dark Mode Support**: Built-in theme switching
- **Responsive Design**: Mobile-first approach
- **Accessibility**: WCAG compliant components

## 🔐 Authentication

Authentication is handled through Supabase Auth with:
- Email/password authentication
- Protected routes via middleware
- Session management
- Role-based access control

## 📊 Features

- **Dashboard**: Overview of business metrics
- **Customer Management**: CRM functionality
- **Project Management**: Project tracking and planning
- **Employee Management**: HR functionality
- **Estimates**: Quote and proposal management
- **Calendar**: Scheduling and events

## 🏛️ Best Practices Implemented

### Separation of Concerns
- **Models**: Data structure definitions
- **Services**: Business logic and API interactions
- **Components**: UI presentation logic
- **Context**: Global state management
- **Hooks**: Reusable stateful logic

### Type Safety
- Comprehensive TypeScript coverage
- Centralized type definitions
- Strict type checking enabled

### Code Organization
- Domain-driven folder structure
- Consistent naming conventions
- Clear import/export patterns
- Modular architecture

### Testing
- Comprehensive test coverage
- Multiple testing strategies
- Clear testing guidelines

## 🤝 Contributing

1. Follow the established folder structure
2. Use TypeScript for all new code
3. Follow the component naming conventions
4. Add proper type definitions
5. Use services for API calls
6. Write tests for new features
7. Test your changes thoroughly

## 📄 License

This project is proprietary software. All rights reserved.
