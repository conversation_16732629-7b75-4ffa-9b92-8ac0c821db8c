import { createBrowserClient } from '@supabase/ssr'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables')
}

export const supabase = createBrowserClient(supabaseUrl, supabaseAnonKey)

// Export function for creating client instances in services
export const createClientSupabaseClient = () => {
  return createBrowserClient(supabaseUrl, supabaseAnonKey)
}

// Type definitions for the application
export interface Customer {
  id: string;
  name: string;
  email?: string;
  phone?: string;
  address?: string;
  notes?: string;
  company_name?: string;
  is_company: boolean;
  city?: string;
  postal_code?: string;
  country?: string;
  vat_number?: string;
  status?: string;
  website?: string;
  industry?: string;
  annual_revenue?: number;
  source?: string;
  assigned_to?: string;
  created_at: string;
  updated_at: string;
}

export interface Project {
  id: string;
  name: string;
  description?: string;
  customer_id?: string;
  start_date?: string;
  end_date?: string;
  status?: string;
  budget?: number;
  budget_spent?: number;
  estimated_hours?: number;
  project_manager?: string;
  priority?: string;
  notes?: string;
  created_at?: string;
  updated_at?: string;
}

export interface CustomerContact {
  id: string;
  customer_id: string;
  first_name: string;
  last_name: string;
  email?: string;
  phone?: string;
  position?: string;
  is_primary?: boolean;
  notes?: string;
  created_at?: string;
  updated_at?: string;
}

export interface Employee {
  id: string;
  name: string;
  email: string;
  phone?: string;
  position: string;
  department: string;
  status: 'active' | 'sick_leave' | 'vacation' | 'inactive';
  hourly_rate: number;
  utilization_percentage: number;
  location: string;
  hire_date: string;
  avatar_url?: string;
  vacation_days_entitled: number;
  vacation_days_used: number;
  vacation_days_pending: number;
  vacation_days_available: number;
  created_at: string;
  updated_at: string;
}

export interface VacationRequest {
  id: string;
  employee_id: string;
  type: 'vacation' | 'sick_leave' | 'personal' | 'parental';
  start_date: string;
  end_date: string;
  days_count: number;
  status: 'pending' | 'approved' | 'rejected';
  notes?: string;
  requested_at: string;
  reviewed_at?: string;
  reviewed_by?: string;
}

export interface EmployeeContract {
  id: string;
  employee_id: string;
  contract_type: 'employment' | 'option' | 'nda' | 'other';
  title: string;
  file_url: string;
  signed_date?: string;
  expiry_date?: string;
  status: 'active' | 'expired' | 'terminated';
  created_at: string;
  updated_at: string;
} 