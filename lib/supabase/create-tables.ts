import { supabase } from './supabase'

export async function ensureProjectTablesExist() {
  try {
    
    // Check if projects table exists
    const { data: projects, error: projectsError } = await supabase
      .from('projects')
      .select('id')
      .limit(1)
    
    // Check if project_staff table exists
    const { data: staff, error: staffError } = await supabase
      .from('project_staff')
      .select('id')
      .limit(1)
    
    // Check if project_resources table exists
    const { data: resources, error: resourcesError } = await supabase
      .from('project_resources')
      .select('id')
      .limit(1)
    
    // Check if project_timeline table exists
    const { data: timeline, error: timelineError } = await supabase
      .from('project_timeline')
      .select('id')
      .limit(1)

    console.log('Table check results:', {
      projects: !projectsError,
      staff: !staffError,
      resources: !resourcesError,
      timeline: !timelineError
    })

    return {
      projects: !projectsError,
      staff: !staffError,
      resources: !resourcesError,
      timeline: !timelineError
    }
  } catch (error) {
    console.error('Error checking project tables:', error)
    return {
      projects: false,
      staff: false,
      resources: false,
      timeline: false
    }
  }
} 