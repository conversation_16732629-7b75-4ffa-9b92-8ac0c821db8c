import { toast } from "sonner";

// Define the ChangelogItem type
export interface ChangelogItem {
  id: string;
  timestamp: string;
  action: string;
  user: string;
  description: string;
}

/**
 * Logs an action to the changelog and shows a toast notification
 */
export function logAction(action: string, description: string, user: string = "<PERSON><PERSON><PERSON>", showToast: boolean = true) {
  // Create the log entry
  const newLog: ChangelogItem = {
    id: Date.now().toString(),
    timestamp: new Date().toISOString(),
    action,
    user,
    description
  };
  
  // Get existing logs from localStorage
  let logs: ChangelogItem[] = [];
  try {
    const storedLogs = localStorage.getItem('changelog');
    if (storedLogs) {
      logs = JSON.parse(storedLogs);
    }
  } catch (error) {
    console.error('Failed to parse existing changelog', error);
  }
  
  // Add the new log and save back to localStorage
  const updatedLogs = [newLog, ...logs];
  localStorage.setItem('changelog', JSON.stringify(updatedLogs));
  
  // Show toast notification if requested
  if (showToast) {
    toast.success(`${action}`, {
      description,
    });
  }
  
  return newLog;
}

/**
 * Retrieves all logs from localStorage
 */
export function getLogs(): ChangelogItem[] {
  try {
    const storedLogs = localStorage.getItem('changelog');
    if (storedLogs) {
      return JSON.parse(storedLogs);
    }
  } catch (error) {
    console.error('Failed to retrieve changelog', error);
  }
  
  return [];
}

/**
 * Clears all logs from localStorage
 */
export function clearLogs(): void {
  localStorage.removeItem('changelog');
} 