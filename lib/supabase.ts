import { createBrowserClient } from '@supabase/ssr'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables')
}

export const supabase = createBrowserClient(supabaseUrl, supabaseAnonKey)

// Type definitions for the application
export interface Customer {
  id: string;
  name: string;
  email?: string;
  phone?: string;
  address?: string;
  notes?: string;
  company_name?: string;
  is_company?: boolean;
  city?: string;
  postal_code?: string;
  country?: string;
  vat_number?: string;
  status?: string;
  website?: string;
  industry?: string;
  annual_revenue?: number;
  source?: string;
  assigned_to?: string;
  created_at?: string;
  updated_at?: string;
}

export interface Project {
  id: string;
  name: string;
  description?: string;
  customer_id?: string;
  start_date?: string;
  end_date?: string;
  status?: string;
  budget?: number;
  budget_spent?: number;
  estimated_hours?: number;
  project_manager?: string;
  priority?: string;
  notes?: string;
  created_at?: string;
  updated_at?: string;
} 