// Database Types
export interface Customer {
  id: string;
  name: string;
  company_name?: string;
  is_company?: boolean;
  email?: string;
  phone?: string;
  address?: string;
  city?: string;
  postal_code?: string;
  country?: string;
  vat_number?: string;
  notes?: string;
  status?: string;
  website?: string;
  industry?: string;
  annual_revenue?: number;
  source?: string;
  assigned_to?: string;
  created_at?: string;
  updated_at?: string;
}

export interface Project {
  id: string;
  name: string;
  description?: string;
  customer_id?: string;
  start_date?: string;
  end_date?: string;
  status?: string;
  budget?: number;
  budget_spent?: number;
  estimated_hours?: number;
  project_manager?: string;
  priority?: string;
  notes?: string;
  created_at?: string;
  updated_at?: string;
}

export interface Employee {
  id: string;
  first_name: string;
  last_name: string;
  email: string;
  phone?: string;
  position?: string;
  department?: string;
  hire_date?: string;
  salary?: number;
  status?: string;
  created_at?: string;
  updated_at?: string;
}

export interface Estimate {
  id: number;
  estimate_number: string;
  customer_id: number;
  description: string;
  valid_until: string;
  vat_rate: number;
  line_items: LineItem[];
  total_amount?: number;
  status?: string;
  created_at?: string;
  updated_at?: string;
}

export interface LineItem {
  id?: number;
  description: string;
  quantity: number;
  unit_price: number;
  amount: number;
}

export interface Invoice {
  id: string;
  invoice_number: string;
  user_id?: string;
  customer_id?: string;
  customer_name: string;
  customer_email?: string;
  customer_phone?: string;
  customer_address?: string;
  project_name?: string;
  description?: string;
  line_items: LineItem[];
  subtotal: number;
  vat_amount: number;
  total_amount: number;
  vat_rate: number;
  currency: string;
  status: 'draft' | 'sent' | 'paid' | 'overdue' | 'cancelled';
  due_date?: string;
  issue_date: string;
  payment_date?: string;
  estimate_id?: string;
  notes?: string;
  payment_terms: string;
  payment_reference?: string;
  created_at: string;
  updated_at: string;
}

export interface ProjectTask {
  id: string;
  project_id: string;
  title: string;
  description?: string;
  assigned_to?: string;
  status: string;
  priority?: string;
  due_date?: string;
  created_at?: string;
  updated_at?: string;
}

export interface UserProfile {
  id: string;
  first_name?: string;
  last_name?: string;
  email?: string;
  full_name?: string;
}

export interface Milestone {
  id: string;
  project_id: string;
  name: string;
  due_date: string;
  status: string;
  is_milestone: boolean;
}

export interface VacationRequest {
  id: string;
  employee_id: string;
  start_date: string;
  end_date: string;
  status: string;
  reason?: string;
  created_at?: string;
}

export interface EmployeeContract {
  id: string;
  employee_id: string;
  contract_type: string;
  start_date: string;
  end_date?: string;
  salary: number;
  created_at?: string;
}

// Extended Types
export interface EmployeeWithRequests extends Employee {
  vacation_requests?: VacationRequest[];
  contracts?: EmployeeContract[];
}

// Component Props Types
export interface EstimateFormProps {
  customers: Customer[];
  estimate?: Estimate;
  onSubmit: (data: any) => void;
  isLoading?: boolean;
}

export interface EstimateListProps {
  estimates: Estimate[];
}

export interface ProjectTasksTabProps {
  projectId: string;
  tasks: ProjectTask[];
  userProfiles: Record<string, UserProfile>;
}

// UI Component Types
export interface Column {
  key: string;
  label: string;
  sortable?: boolean;
}

export interface ExpandableListProps<T> {
  data: T[];
  columns: Column[];
  renderCell: (item: T, columnKey: string) => React.ReactNode;
  renderExpandedContent: (item: T) => React.ReactNode;
  getRowKey: (item: T) => string;
  onRowClick?: (item: T) => void;
  className?: string;
}

// Google Components Types
export interface GoogleCardHeaderProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode;
}

export interface GoogleCardTitleProps extends React.HTMLAttributes<HTMLHeadingElement> {
  children: React.ReactNode;
}

export interface GoogleCardContentProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode;
}

export interface GoogleButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  children: React.ReactNode;
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost';
  size?: 'small' | 'medium' | 'large';
}

export interface GoogleBadgeProps extends React.HTMLAttributes<HTMLSpanElement> {
  children: React.ReactNode;
  variant?: 'default' | 'success' | 'warning' | 'error' | 'info';
}

export interface GoogleChipProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode;
  selected?: boolean;
}

export interface GoogleProgressProps extends React.HTMLAttributes<HTMLDivElement> {
  value: number;
}

// Auth Types
export interface AuthUser {
  id: string;
  email: string;
  user_metadata?: {
    first_name?: string;
    last_name?: string;
    full_name?: string;
  };
}

// API Response Types
export interface ApiResponse<T> {
  data?: T;
  error?: string;
  message?: string;
}

// Form Types
export interface FormField {
  name: string;
  label: string;
  type: 'text' | 'email' | 'number' | 'date' | 'textarea' | 'select';
  required?: boolean;
  options?: { value: string; label: string; }[];
} 