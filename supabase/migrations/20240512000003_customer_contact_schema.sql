-- Customer and Contact Schema enhancements
-- The initial schema already includes basic customers and customer_contacts tables
-- This migration adds additional fields, constraints, and indexes

-- Create customers table if it doesn't exist
CREATE TABLE IF NOT EXISTS customers (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  company_name TEXT,
  is_company BOOLEAN NOT NULL DEFAULT true,
  email TEXT,
  phone TEXT,
  address TEXT,
  city TEXT,
  postal_code TEXT,
  country TEXT,
  vat_number TEXT,
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Create customer_contacts table if it doesn't exist
CREATE TABLE IF NOT EXISTS customer_contacts (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  customer_id UUID NOT NULL REFERENCES customers(id) ON DELETE CASCADE,
  first_name TEXT NOT NULL,
  last_name TEXT NOT NULL,
  email TEXT,
  phone TEXT,
  position TEXT,
  is_primary BOOLEAN NOT NULL DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Add additional fields to customers table if they don't exist
ALTER TABLE IF EXISTS customers 
  ADD COLUMN IF NOT EXISTS status TEXT DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'prospect')),
  ADD COLUMN IF NOT EXISTS website TEXT,
  ADD COLUMN IF NOT EXISTS industry TEXT,
  ADD COLUMN IF NOT EXISTS annual_revenue NUMERIC,
  ADD COLUMN IF NOT EXISTS source TEXT,
  ADD COLUMN IF NOT EXISTS assigned_to UUID REFERENCES auth.users(id) ON DELETE SET NULL;

-- Add indexes for efficient searching and filtering
CREATE INDEX IF NOT EXISTS idx_customers_name ON customers(name);
CREATE INDEX IF NOT EXISTS idx_customers_email ON customers(email) WHERE email IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_customers_status ON customers(status);
CREATE INDEX IF NOT EXISTS idx_customers_country ON customers(country) WHERE country IS NOT NULL;

-- Add additional fields to customer_contacts if they don't exist
ALTER TABLE IF EXISTS customer_contacts
  ADD COLUMN IF NOT EXISTS title TEXT,
  ADD COLUMN IF NOT EXISTS department TEXT,
  ADD COLUMN IF NOT EXISTS linkedin TEXT,
  ADD COLUMN IF NOT EXISTS preferred_contact_method TEXT DEFAULT 'email' CHECK (preferred_contact_method IN ('email', 'phone', 'mail')),
  ADD COLUMN IF NOT EXISTS notes TEXT;

-- Create index on customer_contacts for efficient filtering
CREATE INDEX IF NOT EXISTS idx_customer_contacts_customer_id ON customer_contacts(customer_id);
CREATE INDEX IF NOT EXISTS idx_customer_contacts_email ON customer_contacts(email) WHERE email IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_customer_contacts_is_primary ON customer_contacts(is_primary);

-- Ensure RLS is enabled
ALTER TABLE IF EXISTS customers ENABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS customer_contacts ENABLE ROW LEVEL SECURITY;

-- Update or create RLS policies for customers
DO $$
BEGIN
    -- Drop existing policies if they exist
    DROP POLICY IF EXISTS "Authenticated users can view customers" ON customers;
    DROP POLICY IF EXISTS "Admin and managers can insert customers" ON customers;
    DROP POLICY IF EXISTS "Admin and managers can update customers" ON customers;
    DROP POLICY IF EXISTS "Admin can delete customers" ON customers;
    
    -- Create new policies
    CREATE POLICY "Authenticated users can view customers" 
      ON customers FOR SELECT 
      USING (auth.role() = 'authenticated');
    
    CREATE POLICY "Admin and managers can insert customers" 
      ON customers FOR INSERT 
      WITH CHECK (
        EXISTS (
          SELECT 1 FROM profiles
          WHERE profiles.id = auth.uid() AND profiles.role IN ('admin', 'project_manager')
        )
      );
    
    CREATE POLICY "Admin and managers can update customers" 
      ON customers FOR UPDATE 
      USING (
        EXISTS (
          SELECT 1 FROM profiles
          WHERE profiles.id = auth.uid() AND profiles.role IN ('admin', 'project_manager')
        )
      );
    
    CREATE POLICY "Admin can delete customers" 
      ON customers FOR DELETE 
      USING (
        EXISTS (
          SELECT 1 FROM profiles
          WHERE profiles.id = auth.uid() AND profiles.role = 'admin'
        )
      );
EXCEPTION WHEN OTHERS THEN
    -- If the policies couldn't be created (probably due to role not existing yet)
    -- Just continue without failing the migration
    RAISE NOTICE 'Could not create customer policies: %', SQLERRM;
END $$;

-- Update or create RLS policies for customer_contacts
DO $$
BEGIN
    -- Drop existing policies if they exist
    DROP POLICY IF EXISTS "Authenticated users can view customer contacts" ON customer_contacts;
    DROP POLICY IF EXISTS "Admin and managers can insert customer contacts" ON customer_contacts;
    DROP POLICY IF EXISTS "Admin and managers can update customer contacts" ON customer_contacts;
    DROP POLICY IF EXISTS "Admin can delete customer contacts" ON customer_contacts;
    
    -- Create new policies
    CREATE POLICY "Authenticated users can view customer contacts" 
      ON customer_contacts FOR SELECT 
      USING (auth.role() = 'authenticated');
    
    CREATE POLICY "Admin and managers can insert customer contacts" 
      ON customer_contacts FOR INSERT 
      WITH CHECK (
        EXISTS (
          SELECT 1 FROM profiles
          WHERE profiles.id = auth.uid() AND profiles.role IN ('admin', 'project_manager')
        )
      );
    
    CREATE POLICY "Admin and managers can update customer contacts" 
      ON customer_contacts FOR UPDATE 
      USING (
        EXISTS (
          SELECT 1 FROM profiles
          WHERE profiles.id = auth.uid() AND profiles.role IN ('admin', 'project_manager')
        )
      );
    
    CREATE POLICY "Admin can delete customer contacts" 
      ON customer_contacts FOR DELETE 
      USING (
        EXISTS (
          SELECT 1 FROM profiles
          WHERE profiles.id = auth.uid() AND profiles.role = 'admin'
        )
      );
EXCEPTION WHEN OTHERS THEN
    -- If the policies couldn't be created (probably due to role not existing yet)
    -- Just continue without failing the migration
    RAISE NOTICE 'Could not create customer contact policies: %', SQLERRM;
END $$;

-- Create a function to ensure only one primary contact per customer
CREATE OR REPLACE FUNCTION check_primary_contact()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.is_primary THEN
        UPDATE customer_contacts
        SET is_primary = FALSE
        WHERE customer_id = NEW.customer_id
          AND id != NEW.id
          AND is_primary = TRUE;
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Add a trigger to enforce the primary contact constraint
DROP TRIGGER IF EXISTS ensure_one_primary_contact ON customer_contacts;
CREATE TRIGGER ensure_one_primary_contact
BEFORE INSERT OR UPDATE ON customer_contacts
FOR EACH ROW
EXECUTE FUNCTION check_primary_contact(); 