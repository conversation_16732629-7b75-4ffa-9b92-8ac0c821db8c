-- Fix projects table RLS policies and create missing tables
-- This migration ensures proper permissions for project creation

-- First, create the projects table if it doesn't exist (with proper structure)
CREATE TABLE IF NOT EXISTS projects (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  description TEXT,
  status TEXT DEFAULT 'planning' CHECK (status IN ('planning', 'active', 'on-hold', 'completed', 'cancelled')),
  start_date DATE,
  end_date DATE,
  customer_id UUID REFERENCES customers(id) ON DELETE SET NULL,
  budget NUMERIC,
  estimated_hours NUMERIC,
  notes TEXT,
  priority TEXT DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS on projects table
ALTER TABLE projects ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DO $$
BEGIN
    DROP POLICY IF EXISTS "Authenticated users can view projects" ON projects;
    DROP POLICY IF EXISTS "Authenticated users can insert projects" ON projects;
    DROP POLICY IF EXISTS "Authenticated users can update projects" ON projects;
    DROP POLICY IF EXISTS "Admin and managers can delete projects" ON projects;
EXCEPTION WHEN OTHERS THEN
    -- Ignore errors if policies don't exist
END $$;

-- Create comprehensive RLS policies for projects
CREATE POLICY "Authenticated users can view projects" 
  ON projects FOR SELECT 
  USING (auth.role() = 'authenticated');

CREATE POLICY "Authenticated users can insert projects" 
  ON projects FOR INSERT 
  WITH CHECK (auth.role() = 'authenticated');

CREATE POLICY "Authenticated users can update projects" 
  ON projects FOR UPDATE 
  USING (auth.role() = 'authenticated');

CREATE POLICY "Admin and managers can delete projects" 
  ON projects FOR DELETE 
  USING (
    auth.role() = 'authenticated' AND
    (
      auth.role() = 'service_role' OR
      EXISTS (
        SELECT 1 FROM profiles
        WHERE profiles.id = auth.uid() AND profiles.role IN ('admin', 'project_manager')
      )
    )
  );

-- Create project_staff table (renamed from project_resources to match your code)
CREATE TABLE IF NOT EXISTS project_staff (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
  user_id UUID NOT NULL,
  role TEXT,
  hourly_rate NUMERIC,
  allocation_percentage INTEGER DEFAULT 100,
  start_date DATE,
  end_date DATE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS on project_staff
ALTER TABLE project_staff ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for project_staff
CREATE POLICY "Authenticated users can view project staff" 
  ON project_staff FOR SELECT 
  USING (auth.role() = 'authenticated');

CREATE POLICY "Authenticated users can manage project staff" 
  ON project_staff FOR ALL 
  USING (auth.role() = 'authenticated')
  WITH CHECK (auth.role() = 'authenticated');

-- Create project_timeline table
CREATE TABLE IF NOT EXISTS project_timeline (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  description TEXT,
  start_date DATE,
  end_date DATE,
  milestone BOOLEAN DEFAULT FALSE,
  status TEXT DEFAULT 'planned' CHECK (status IN ('planned', 'in-progress', 'completed', 'cancelled')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS on project_timeline
ALTER TABLE project_timeline ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for project_timeline
CREATE POLICY "Authenticated users can view project timeline" 
  ON project_timeline FOR SELECT 
  USING (auth.role() = 'authenticated');

CREATE POLICY "Authenticated users can manage project timeline" 
  ON project_timeline FOR ALL 
  USING (auth.role() = 'authenticated')
  WITH CHECK (auth.role() = 'authenticated');

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_projects_customer_id ON projects(customer_id);
CREATE INDEX IF NOT EXISTS idx_projects_status ON projects(status);
CREATE INDEX IF NOT EXISTS idx_projects_priority ON projects(priority);
CREATE INDEX IF NOT EXISTS idx_project_staff_project_id ON project_staff(project_id);
CREATE INDEX IF NOT EXISTS idx_project_staff_user_id ON project_staff(user_id);
CREATE INDEX IF NOT EXISTS idx_project_timeline_project_id ON project_timeline(project_id); 