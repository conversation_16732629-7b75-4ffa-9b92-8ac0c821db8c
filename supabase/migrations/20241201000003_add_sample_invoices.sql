-- Add sample invoices for testing
-- Note: This will only work if there are users in the auth.users table

-- First, let's create some sample invoices with a placeholder user_id
-- You'll need to replace the user_id with an actual user ID from your auth.users table

INSERT INTO invoices (
  invoice_number,
  user_id,
  customer_name,
  customer_email,
  customer_phone,
  customer_address,
  project_name,
  description,
  line_items,
  subtotal,
  vat_amount,
  total_amount,
  vat_rate,
  currency,
  status,
  due_date,
  issue_date,
  payment_terms,
  notes
) VALUES 
(
  '2024-0001',
  (SELECT id FROM auth.users LIMIT 1), -- Use the first available user
  'Acme Corporation',
  '<EMAIL>',
  '+46 8 123 456 78',
  'Storgatan 1, 111 22 Stockholm',
  'Website Redesign',
  'Complete website redesign and development',
  '[
    {
      "id": "1",
      "description": "Website Design",
      "quantity": 1,
      "unit_price": 50000,
      "total": 50000,
      "vat_rate": 25
    },
    {
      "id": "2", 
      "description": "Development Hours",
      "quantity": 40,
      "unit_price": 1200,
      "total": 48000,
      "vat_rate": 25
    }
  ]'::jsonb,
  98000.00,
  24500.00,
  122500.00,
  25.00,
  'SEK',
  'sent',
  CURRENT_DATE + INTERVAL '30 days',
  CURRENT_DATE,
  '30 dagar netto',
  'Professional website redesign with modern UI/UX'
),
(
  '2024-0002',
  (SELECT id FROM auth.users LIMIT 1),
  'Tech Solutions AB',
  '<EMAIL>',
  '+46 8 987 654 32',
  'Kungsgatan 45, 111 56 Stockholm',
  'Mobile App Development',
  'iOS and Android mobile application',
  '[
    {
      "id": "1",
      "description": "Mobile App Design",
      "quantity": 1,
      "unit_price": 75000,
      "total": 75000,
      "vat_rate": 25
    },
    {
      "id": "2",
      "description": "Development",
      "quantity": 80,
      "unit_price": 1500,
      "total": 120000,
      "vat_rate": 25
    }
  ]'::jsonb,
  195000.00,
  48750.00,
  243750.00,
  25.00,
  'SEK',
  'paid',
  CURRENT_DATE - INTERVAL '5 days',
  CURRENT_DATE - INTERVAL '35 days',
  '30 dagar netto',
  'Cross-platform mobile application with backend integration'
),
(
  '2024-0003',
  (SELECT id FROM auth.users LIMIT 1),
  'StartupXYZ',
  '<EMAIL>',
  '+46 70 123 456 78',
  'Innovation Hub, 112 34 Stockholm',
  'MVP Development',
  'Minimum viable product development',
  '[
    {
      "id": "1",
      "description": "Product Strategy",
      "quantity": 20,
      "unit_price": 1800,
      "total": 36000,
      "vat_rate": 25
    },
    {
      "id": "2",
      "description": "MVP Development",
      "quantity": 60,
      "unit_price": 1400,
      "total": 84000,
      "vat_rate": 25
    }
  ]'::jsonb,
  120000.00,
  30000.00,
  150000.00,
  25.00,
  'SEK',
  'draft',
  CURRENT_DATE + INTERVAL '45 days',
  CURRENT_DATE,
  '45 dagar netto',
  'MVP development for early-stage startup'
),
(
  '2024-0004',
  (SELECT id FROM auth.users LIMIT 1),
  'Enterprise Corp',
  '<EMAIL>',
  '+46 8 555 123 45',
  'Business District, 114 28 Stockholm',
  'System Integration',
  'Legacy system integration and modernization',
  '[
    {
      "id": "1",
      "description": "System Analysis",
      "quantity": 30,
      "unit_price": 1600,
      "total": 48000,
      "vat_rate": 25
    },
    {
      "id": "2",
      "description": "Integration Development",
      "quantity": 100,
      "unit_price": 1300,
      "total": 130000,
      "vat_rate": 25
    },
    {
      "id": "3",
      "description": "Testing & Deployment",
      "quantity": 20,
      "unit_price": 1500,
      "total": 30000,
      "vat_rate": 25
    }
  ]'::jsonb,
  208000.00,
  52000.00,
  260000.00,
  25.00,
  'SEK',
  'overdue',
  CURRENT_DATE - INTERVAL '10 days',
  CURRENT_DATE - INTERVAL '40 days',
  '30 dagar netto',
  'Complex system integration project with multiple legacy systems'
),
(
  '2024-0005',
  (SELECT id FROM auth.users LIMIT 1),
  'Digital Agency',
  '<EMAIL>',
  '+46 8 777 888 99',
  'Creative Quarter, 118 25 Stockholm',
  'E-commerce Platform',
  'Custom e-commerce solution',
  '[
    {
      "id": "1",
      "description": "Platform Setup",
      "quantity": 1,
      "unit_price": 45000,
      "total": 45000,
      "vat_rate": 25
    },
    {
      "id": "2",
      "description": "Custom Features",
      "quantity": 50,
      "unit_price": 1100,
      "total": 55000,
      "vat_rate": 25
    }
  ]'::jsonb,
  100000.00,
  25000.00,
  125000.00,
  25.00,
  'SEK',
  'sent',
  CURRENT_DATE + INTERVAL '15 days',
  CURRENT_DATE - INTERVAL '5 days',
  '30 dagar netto',
  'Modern e-commerce platform with payment integration'
);
