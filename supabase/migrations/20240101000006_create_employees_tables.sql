-- Create employees table
CREATE TABLE IF NOT EXISTS employees (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL,
  email TEXT UNIQUE NOT NULL,
  phone TEXT,
  position TEXT NOT NULL,
  department TEXT NOT NULL,
  status TEXT NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'sick_leave', 'vacation', 'inactive')),
  hourly_rate DECIMAL(10,2) NOT NULL DEFAULT 0,
  utilization_percentage INTEGER NOT NULL DEFAULT 0 CHECK (utilization_percentage >= 0 AND utilization_percentage <= 100),
  location TEXT NOT NULL DEFAULT 'Stockholm',
  hire_date DATE NOT NULL,
  avatar_url TEXT,
  vacation_days_entitled INTEGER NOT NULL DEFAULT 25,
  vacation_days_used INTEGER NOT NULL DEFAULT 0,
  vacation_days_pending INTEGER NOT NULL DEFAULT 0,
  vacation_days_available INTEGER NOT NULL DEFAULT 25,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- <PERSON><PERSON> vacation_requests table
CREATE TABLE IF NOT EXISTS vacation_requests (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  employee_id UUID NOT NULL REFERENCES employees(id) ON DELETE CASCADE,
  type TEXT NOT NULL DEFAULT 'vacation' CHECK (type IN ('vacation', 'sick_leave', 'personal', 'parental')),
  start_date DATE NOT NULL,
  end_date DATE NOT NULL,
  days_count INTEGER NOT NULL,
  status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected')),
  notes TEXT,
  requested_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  reviewed_at TIMESTAMP WITH TIME ZONE,
  reviewed_by TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create employee_contracts table
CREATE TABLE IF NOT EXISTS employee_contracts (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  employee_id UUID NOT NULL REFERENCES employees(id) ON DELETE CASCADE,
  contract_type TEXT NOT NULL CHECK (contract_type IN ('employment', 'option', 'nda', 'other')),
  title TEXT NOT NULL,
  file_url TEXT NOT NULL,
  signed_date DATE,
  expiry_date DATE,
  status TEXT NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'expired', 'terminated')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_employees_email ON employees(email);
CREATE INDEX IF NOT EXISTS idx_employees_status ON employees(status);
CREATE INDEX IF NOT EXISTS idx_employees_department ON employees(department);
CREATE INDEX IF NOT EXISTS idx_vacation_requests_employee_id ON vacation_requests(employee_id);
CREATE INDEX IF NOT EXISTS idx_vacation_requests_status ON vacation_requests(status);
CREATE INDEX IF NOT EXISTS idx_employee_contracts_employee_id ON employee_contracts(employee_id);

-- Create triggers for updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_employees_updated_at BEFORE UPDATE ON employees
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_vacation_requests_updated_at BEFORE UPDATE ON vacation_requests
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_employee_contracts_updated_at BEFORE UPDATE ON employee_contracts
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert the three specific employees
INSERT INTO employees (name, email, position, department, status, hourly_rate, utilization_percentage, location, hire_date, vacation_days_entitled, vacation_days_used, vacation_days_pending, vacation_days_available) VALUES
('Youssef Mekidiche', '<EMAIL>', 'Vice President & Co-founder', 'Management', 'active', 1200, 95, 'Stockholm', '2020-01-15', 30, 5, 0, 25),
('Rami Mekidiche', '<EMAIL>', 'Vice President & Co-founder', 'Management', 'active', 1200, 90, 'Stockholm', '2020-01-15', 30, 8, 5, 17),
('Kristoffer Andersson', '<EMAIL>', 'Co-founder & COO', 'Operations', 'active', 1100, 88, 'Stockholm', '2020-02-01', 28, 12, 0, 16);

-- Insert some sample vacation requests
INSERT INTO vacation_requests (employee_id, type, start_date, end_date, days_count, status, notes) VALUES
((SELECT id FROM employees WHERE email = '<EMAIL>'), 'vacation', '2025-06-15', '2025-06-19', 5, 'pending', 'Sommarsemester med familjen'),
((SELECT id FROM employees WHERE email = '<EMAIL>'), 'vacation', '2025-07-01', '2025-07-05', 5, 'approved', 'Semester i juli'),
((SELECT id FROM employees WHERE email = '<EMAIL>'), 'vacation', '2025-08-10', '2025-08-20', 10, 'approved', 'Längre sommarsemester');

-- Insert some sample contracts
INSERT INTO employee_contracts (employee_id, contract_type, title, file_url, signed_date, status) VALUES
((SELECT id FROM employees WHERE email = '<EMAIL>'), 'employment', 'Anställningsavtal - Youssef Mekidiche', '/contracts/youssef_employment.pdf', '2020-01-15', 'active'),
((SELECT id FROM employees WHERE email = '<EMAIL>'), 'option', 'Optionsavtal - Youssef Mekidiche', '/contracts/youssef_options.pdf', '2020-01-15', 'active'),
((SELECT id FROM employees WHERE email = '<EMAIL>'), 'employment', 'Anställningsavtal - Rami Mekidiche', '/contracts/rami_employment.pdf', '2020-01-15', 'active'),
((SELECT id FROM employees WHERE email = '<EMAIL>'), 'option', 'Optionsavtal - Rami Mekidiche', '/contracts/rami_options.pdf', '2020-01-15', 'active'),
((SELECT id FROM employees WHERE email = '<EMAIL>'), 'employment', 'Anställningsavtal - Kristoffer Andersson', '/contracts/kristoffer_employment.pdf', '2020-02-01', 'active'),
((SELECT id FROM employees WHERE email = '<EMAIL>'), 'option', 'Optionsavtal - Kristoffer Andersson', '/contracts/kristoffer_options.pdf', '2020-02-01', 'active');

-- Enable RLS
ALTER TABLE employees ENABLE ROW LEVEL SECURITY;
ALTER TABLE vacation_requests ENABLE ROW LEVEL SECURITY;
ALTER TABLE employee_contracts ENABLE ROW LEVEL SECURITY;

-- Create RLS policies (allow all for authenticated users for now)
CREATE POLICY "Allow all operations for authenticated users" ON employees
  FOR ALL USING (auth.role() = 'authenticated');

CREATE POLICY "Allow all operations for authenticated users" ON vacation_requests
  FOR ALL USING (auth.role() = 'authenticated');

CREATE POLICY "Allow all operations for authenticated users" ON employee_contracts
  FOR ALL USING (auth.role() = 'authenticated'); 