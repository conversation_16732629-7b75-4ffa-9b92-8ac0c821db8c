-- Create required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create profiles table (basic version without role yet)
CREATE TABLE IF NOT EXISTS profiles (
  id UUID PRIMARY KEY REFERENCES auth.users ON DELETE CASCADE,
  first_name TEXT,
  last_name TEXT,
  phone TEXT,
  avatar_url TEXT,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  CONSTRAINT profiles_id_key UNIQUE (id)
);

-- Create customers table
CREATE TABLE IF NOT EXISTS customers (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  company_name TEXT,
  is_company BOOLEAN NOT NULL DEFAULT true,
  email TEXT,
  phone TEXT,
  address TEXT,
  city TEXT,
  postal_code TEXT,
  country TEXT NOT NULL,
  vat_number TEXT,
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Create customer_contacts table
CREATE TABLE IF NOT EXISTS customer_contacts (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  customer_id UUID NOT NULL REFERENCES customers(id) ON DELETE CASCADE,
  first_name TEXT NOT NULL,
  last_name TEXT NOT NULL,
  email TEXT,
  phone TEXT,
  position TEXT,
  is_primary BOOLEAN NOT NULL DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Enable row level security if not already enabled
ALTER TABLE IF EXISTS profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS customers ENABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS customer_contacts ENABLE ROW LEVEL SECURITY;

-- Profiles policies - Drop existing policies to recreate them
DO
$$
BEGIN
    -- Try to drop policies if they exist
    BEGIN
        DROP POLICY IF EXISTS "Users can view their own profile" ON profiles;
    EXCEPTION WHEN OTHERS THEN
        -- Do nothing if the policy doesn't exist
    END;
    
    BEGIN
        DROP POLICY IF EXISTS "Users can update their own profile" ON profiles;
    EXCEPTION WHEN OTHERS THEN
        -- Do nothing if the policy doesn't exist
    END;
    
    BEGIN
        DROP POLICY IF EXISTS "Admin users can view all profiles" ON profiles;
    EXCEPTION WHEN OTHERS THEN
        -- Do nothing if the policy doesn't exist
    END;
    
    BEGIN
        DROP POLICY IF EXISTS "Admin users can update any profile" ON profiles;
    EXCEPTION WHEN OTHERS THEN
        -- Do nothing if the policy doesn't exist
    END;

    -- Customers policies
    BEGIN
        DROP POLICY IF EXISTS "All authenticated users can view customers" ON customers;
    EXCEPTION WHEN OTHERS THEN
        -- Do nothing if the policy doesn't exist
    END;
    
    BEGIN
        DROP POLICY IF EXISTS "Admin and project managers can insert customers" ON customers;
    EXCEPTION WHEN OTHERS THEN
        -- Do nothing if the policy doesn't exist
    END;
    
    BEGIN
        DROP POLICY IF EXISTS "Admin and project managers can update customers" ON customers;
    EXCEPTION WHEN OTHERS THEN
        -- Do nothing if the policy doesn't exist
    END;

    -- Customer contacts policies
    BEGIN
        DROP POLICY IF EXISTS "All authenticated users can view customer contacts" ON customer_contacts;
    EXCEPTION WHEN OTHERS THEN
        -- Do nothing if the policy doesn't exist
    END;
    
    BEGIN
        DROP POLICY IF EXISTS "Admin and project managers can insert customer contacts" ON customer_contacts;
    EXCEPTION WHEN OTHERS THEN
        -- Do nothing if the policy doesn't exist
    END;
    
    BEGIN
        DROP POLICY IF EXISTS "Admin and project managers can update customer contacts" ON customer_contacts;
    EXCEPTION WHEN OTHERS THEN
        -- Do nothing if the policy doesn't exist
    END;

    -- Storage policies
    BEGIN
        DROP POLICY IF EXISTS "Avatar images are publicly accessible." ON storage.objects;
    EXCEPTION WHEN OTHERS THEN
        -- Do nothing if the policy doesn't exist
    END;
    
    BEGIN
        DROP POLICY IF EXISTS "Users can upload their own avatar." ON storage.objects;
    EXCEPTION WHEN OTHERS THEN
        -- Do nothing if the policy doesn't exist
    END;
    
    BEGIN
        DROP POLICY IF EXISTS "Users can update their own avatar." ON storage.objects;
    EXCEPTION WHEN OTHERS THEN
        -- Do nothing if the policy doesn't exist
    END;
END;
$$;

-- Create policies after dropping them

-- Profiles policies
CREATE POLICY "Users can view their own profile" 
  ON profiles FOR SELECT 
  USING (auth.uid() = id);

CREATE POLICY "Users can update their own profile" 
  ON profiles FOR UPDATE 
  USING (auth.uid() = id);

CREATE POLICY "Admin users can view all profiles" 
  ON profiles FOR SELECT 
  USING (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE profiles.id = auth.uid() AND profiles.role = 'admin'
    )
  );

CREATE POLICY "Admin users can update any profile" 
  ON profiles FOR UPDATE 
  USING (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE profiles.id = auth.uid() AND profiles.role = 'admin'
    )
  );

-- Customers policies
CREATE POLICY "All authenticated users can view customers" 
  ON customers FOR SELECT 
  USING (auth.role() = 'authenticated');

CREATE POLICY "Admin and project managers can insert customers" 
  ON customers FOR INSERT 
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE profiles.id = auth.uid() AND profiles.role IN ('admin', 'project_manager')
    )
  );

CREATE POLICY "Admin and project managers can update customers" 
  ON customers FOR UPDATE 
  USING (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE profiles.id = auth.uid() AND profiles.role IN ('admin', 'project_manager')
    )
  );

-- Customer contacts policies
CREATE POLICY "All authenticated users can view customer contacts" 
  ON customer_contacts FOR SELECT 
  USING (auth.role() = 'authenticated');

CREATE POLICY "Admin and project managers can insert customer contacts" 
  ON customer_contacts FOR INSERT 
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE profiles.id = auth.uid() AND profiles.role IN ('admin', 'project_manager')
    )
  );

CREATE POLICY "Admin and project managers can update customer contacts" 
  ON customer_contacts FOR UPDATE 
  USING (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE profiles.id = auth.uid() AND profiles.role IN ('admin', 'project_manager')
    )
  );

-- Set up storage buckets for avatars if it doesn't exist
INSERT INTO storage.buckets (id, name, public) 
VALUES ('avatars', 'avatars', true)
ON CONFLICT (id) DO NOTHING;

-- Storage policies for avatars
CREATE POLICY "Avatar images are publicly accessible."
  ON storage.objects FOR SELECT
  USING (bucket_id = 'avatars');

CREATE POLICY "Users can upload their own avatar."
  ON storage.objects FOR INSERT
  WITH CHECK (bucket_id = 'avatars' AND (storage.foldername(name))[1] = auth.uid()::text);

CREATE POLICY "Users can update their own avatar."
  ON storage.objects FOR UPDATE
  USING (bucket_id = 'avatars' AND (storage.foldername(name))[1] = auth.uid()::text); 