-- Add timesheets table for staff allocation tracking
CREATE TABLE IF NOT EXISTS public.timesheets (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  user_id UUID NOT NULL,
  project_id UUID NOT NULL,
  date DATE NOT NULL,
  start_time TIME NOT NULL,
  end_time TIME NOT NULL,
  break_duration INTERVAL DEFAULT '00:00:00',
  description TEXT,
  billable BOOLEAN DEFAULT TRUE,
  approved_at TIMESTAMP WITH TIME ZONE,
  approved_by <PERSON><PERSON><PERSON>,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  organization_id UUID,
  
  CONSTRAINT fk_project_id FOREIGN KEY (project_id)
    REFERENCES public.projects (id) ON DELETE CASCADE,
  CONSTRAINT timesheet_date_range CHECK (start_time < end_time)
);

-- Add an index on project_id and date for faster queries
CREATE INDEX IF NOT EXISTS timesheets_project_date_idx ON public.timesheets (project_id, date);
CREATE INDEX IF NOT EXISTS timesheets_user_date_idx ON public.timesheets (user_id, date);

-- Add a trigger to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_timesheet_timestamp()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS update_timesheet_timestamp ON public.timesheets;
CREATE TRIGGER update_timesheet_timestamp
BEFORE UPDATE ON public.timesheets
FOR EACH ROW
EXECUTE FUNCTION update_timesheet_timestamp();

-- Add project_members table for easier staff management if it doesn't exist
CREATE TABLE IF NOT EXISTS public.project_members (
  project_id UUID NOT NULL,
  user_id UUID NOT NULL,
  role TEXT,
  hourly_rate DECIMAL(10, 2),
  allocation_percentage INTEGER DEFAULT 100,
  start_date DATE,
  end_date DATE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  PRIMARY KEY (project_id, user_id),
  CONSTRAINT fk_project_member_project FOREIGN KEY (project_id)
    REFERENCES public.projects (id) ON DELETE CASCADE
);

-- Create RLS policies for timesheets
ALTER TABLE public.timesheets ENABLE ROW LEVEL SECURITY;

-- Allow read access to all authenticated users
CREATE POLICY timesheet_read_policy ON public.timesheets
FOR SELECT USING (auth.role() IN ('authenticated', 'service_role'));

-- Allow insert/update only for the user's own timesheet or by admins/managers
CREATE POLICY timesheet_insert_policy ON public.timesheets
FOR INSERT WITH CHECK (
  auth.uid() = user_id OR
  auth.role() IN ('service_role') OR
  EXISTS (
    SELECT 1 FROM public.profiles
    WHERE id = auth.uid() AND (role = 'admin' OR role = 'manager')
  )
);

CREATE POLICY timesheet_update_policy ON public.timesheets
FOR UPDATE USING (
  auth.uid() = user_id OR
  auth.role() IN ('service_role') OR
  EXISTS (
    SELECT 1 FROM public.profiles
    WHERE id = auth.uid() AND (role = 'admin' OR role = 'manager')
  )
);

-- Allow delete only for admins/managers
CREATE POLICY timesheet_delete_policy ON public.timesheets
FOR DELETE USING (
  auth.role() IN ('service_role') OR
  EXISTS (
    SELECT 1 FROM public.profiles
    WHERE id = auth.uid() AND (role = 'admin' OR role = 'manager')
  )
);

-- Add RLS policies for project_members
ALTER TABLE public.project_members ENABLE ROW LEVEL SECURITY;

-- Allow read access to all authenticated users
CREATE POLICY project_member_read_policy ON public.project_members
FOR SELECT USING (auth.role() IN ('authenticated', 'service_role'));

-- Allow insert/update/delete for admins/managers
CREATE POLICY project_member_insert_policy ON public.project_members
FOR INSERT WITH CHECK (
  auth.role() IN ('service_role') OR
  EXISTS (
    SELECT 1 FROM public.profiles
    WHERE id = auth.uid() AND (role = 'admin' OR role = 'manager')
  )
);

CREATE POLICY project_member_update_policy ON public.project_members
FOR UPDATE USING (
  auth.role() IN ('service_role') OR
  EXISTS (
    SELECT 1 FROM public.profiles
    WHERE id = auth.uid() AND (role = 'admin' OR role = 'manager')
  )
);

CREATE POLICY project_member_delete_policy ON public.project_members
FOR DELETE USING (
  auth.role() IN ('service_role') OR
  EXISTS (
    SELECT 1 FROM public.profiles
    WHERE id = auth.uid() AND (role = 'admin' OR role = 'manager')
  )
); 