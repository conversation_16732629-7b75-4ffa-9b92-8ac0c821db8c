-- First, add all required columns to profiles if they don't exist
ALTER TABLE IF EXISTS profiles ADD COLUMN IF NOT EXISTS role TEXT;
ALTER TABLE IF EXISTS profiles ALTER COLUMN role SET DEFAULT 'employee';

-- This will run even if the check already exists, but will fail silently
DO $$
BEGIN
    ALTER TABLE profiles ADD CONSTRAINT role_check CHECK (role IN ('admin', 'project_manager', 'employee'));
EXCEPTION WHEN duplicate_object THEN
    -- Do nothing, constraint already exists
END$$;

ALTER TABLE IF EXISTS profiles ADD COLUMN IF NOT EXISTS department TEXT;
ALTER TABLE IF EXISTS profiles ADD COLUMN IF NOT EXISTS skills TEXT[];
ALTER TABLE IF EXISTS profiles ADD COLUMN IF NOT EXISTS created_at TIMESTAMPTZ DEFAULT now(); 