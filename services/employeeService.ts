import { createClientSupabaseClient } from '@/lib/supabase/supabase';
import type { Employee } from '@/models';

export class EmployeeService {
  private supabase = createClientSupabaseClient();

  async getAllEmployees(): Promise<Employee[]> {
    try {
      const { data, error } = await this.supabase
        .from('employees')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data as Employee[];
    } catch (error) {
      console.error('Error fetching employees:', error);
      return [];
    }
  }

  async getEmployeeById(id: string): Promise<Employee | null> {
    try {
      const { data, error } = await this.supabase
        .from('employees')
        .select('*')
        .eq('id', id)
        .single();

      if (error) throw error;
      return data as Employee;
    } catch (error) {
      console.error('Error fetching employee:', error);
      return null;
    }
  }

  async createEmployee(employee: Omit<Employee, 'id' | 'created_at' | 'updated_at'>): Promise<Employee | null> {
    try {
      const { data, error } = await this.supabase
        .from('employees')
        .insert(employee)
        .select()
        .single();

      if (error) throw error;
      return data as Employee;
    } catch (error) {
      console.error('Error creating employee:', error);
      return null;
    }
  }

  async updateEmployee(id: string, updates: Partial<Employee>): Promise<Employee | null> {
    try {
      const { data, error } = await this.supabase
        .from('employees')
        .update(updates)
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      return data as Employee;
    } catch (error) {
      console.error('Error updating employee:', error);
      return null;
    }
  }

  async deleteEmployee(id: string): Promise<boolean> {
    try {
      const { error } = await this.supabase
        .from('employees')
        .delete()
        .eq('id', id);

      if (error) throw error;
      return true;
    } catch (error) {
      console.error('Error deleting employee:', error);
      return false;
    }
  }

  async getEmployeesByDepartment(department: string): Promise<Employee[]> {
    try {
      const { data, error } = await this.supabase
        .from('employees')
        .select('*')
        .eq('department', department)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data as Employee[];
    } catch (error) {
      console.error('Error fetching employees by department:', error);
      return [];
    }
  }
}

// Export singleton instance
export const employeeService = new EmployeeService(); 