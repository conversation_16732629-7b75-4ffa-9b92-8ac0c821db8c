import { createClientSupabaseClient } from '@/lib/supabase/supabase';
import type { Estimate, EstimateLineItem } from '@/models';

export class EstimateService {
  private supabase = createClientSupabaseClient();

  async getAllEstimates(): Promise<Estimate[]> {
    try {
      const { data, error } = await this.supabase
        .from('estimates')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data as Estimate[];
    } catch (error) {
      console.error('Error fetching estimates:', error);
      return [];
    }
  }

  async getEstimateById(id: string): Promise<Estimate | null> {
    try {
      const { data, error } = await this.supabase
        .from('estimates')
        .select('*')
        .eq('id', id)
        .single();

      if (error) throw error;
      return data as Estimate;
    } catch (error) {
      console.error('Error fetching estimate:', error);
      return null;
    }
  }

  async createEstimate(estimate: Omit<Estimate, 'id' | 'created_at' | 'updated_at'>): Promise<Estimate | null> {
    try {
      const { data, error } = await this.supabase
        .from('estimates')
        .insert(estimate)
        .select()
        .single();

      if (error) throw error;
      return data as Estimate;
    } catch (error) {
      console.error('Error creating estimate:', error);
      return null;
    }
  }

  async updateEstimate(id: string, updates: Partial<Estimate>): Promise<Estimate | null> {
    try {
      const { data, error } = await this.supabase
        .from('estimates')
        .update(updates)
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      return data as Estimate;
    } catch (error) {
      console.error('Error updating estimate:', error);
      return null;
    }
  }

  async deleteEstimate(id: string): Promise<boolean> {
    try {
      const { error } = await this.supabase
        .from('estimates')
        .delete()
        .eq('id', id);

      if (error) throw error;
      return true;
    } catch (error) {
      console.error('Error deleting estimate:', error);
      return false;
    }
  }

  async getEstimateLineItems(estimateId: string): Promise<EstimateLineItem[]> {
    try {
      const { data, error } = await this.supabase
        .from('estimate_line_items')
        .select('*')
        .eq('estimate_id', estimateId)
        .order('created_at', { ascending: true });

      if (error) throw error;
      return data as EstimateLineItem[];
    } catch (error) {
      console.error('Error fetching estimate line items:', error);
      return [];
    }
  }
}

// Export singleton instance
export const estimateService = new EstimateService(); 