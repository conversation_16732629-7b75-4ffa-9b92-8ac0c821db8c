import { Invoice } from '@/lib/types';

export class InvoiceService {
  private cache = new Map<string, { data: any; timestamp: number }>();
  private readonly CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

  private getCachedData(key: string) {
    const cached = this.cache.get(key);
    if (cached && Date.now() - cached.timestamp < this.CACHE_DURATION) {
      return cached.data;
    }
    return null;
  }

  private setCachedData(key: string, data: any) {
    this.cache.set(key, { data, timestamp: Date.now() });
  }

  async getAllInvoices(): Promise<Invoice[]> {
    try {
      const cacheKey = 'all-invoices';
      const cached = this.getCachedData(cacheKey);
      if (cached) return cached;

      console.log('Fetching invoices from API...');

      const response = await fetch('/api/invoices', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        console.error('API error:', errorData);
        throw new Error(errorData.error || 'Failed to fetch invoices');
      }

      const result = await response.json();
      console.log('Invoices fetched successfully:', result.data?.length || 0, 'records');

      const invoices = result.data || [];
      this.setCachedData(cacheKey, invoices);
      return invoices;
    } catch (error) {
      console.error('Error fetching invoices:', error);
      return [];
    }
  }

  async getInvoiceById(id: string): Promise<Invoice | null> {
    try {
      const { data, error } = await this.supabase
        .from('invoices')
        .select(`
          *,
          customers (
            name,
            email,
            phone,
            address,
            company_name
          )
        `)
        .eq('id', id)
        .single();

      if (error) throw error;
      return data as Invoice;
    } catch (error) {
      console.error('Error fetching invoice:', error);
      return null;
    }
  }

  async createInvoice(invoice: Omit<Invoice, 'id' | 'created_at' | 'updated_at'>): Promise<Invoice | null> {
    try {
      const { data, error } = await this.supabase
        .from('invoices')
        .insert(invoice)
        .select()
        .single();

      if (error) throw error;
      return data as Invoice;
    } catch (error) {
      console.error('Error creating invoice:', error);
      return null;
    }
  }

  async updateInvoice(id: string, updates: Partial<Invoice>): Promise<Invoice | null> {
    try {
      const { data, error } = await this.supabase
        .from('invoices')
        .update(updates)
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      return data as Invoice;
    } catch (error) {
      console.error('Error updating invoice:', error);
      return null;
    }
  }

  async deleteInvoice(id: string): Promise<boolean> {
    try {
      const { error } = await this.supabase
        .from('invoices')
        .delete()
        .eq('id', id);

      if (error) throw error;
      return true;
    } catch (error) {
      console.error('Error deleting invoice:', error);
      return false;
    }
  }

  async getInvoiceStats(): Promise<{
    totalInvoices: number;
    totalAmount: number;
    paidAmount: number;
    overdueAmount: number;
    draftCount: number;
    sentCount: number;
    paidCount: number;
    overdueCount: number;
  }> {
    try {
      const { data, error } = await this.supabase
        .from('invoices')
        .select('status, total_amount, due_date');

      if (error) throw error;

      const invoices = data || [];
      const now = new Date();

      const stats = {
        totalInvoices: invoices.length,
        totalAmount: 0,
        paidAmount: 0,
        overdueAmount: 0,
        draftCount: 0,
        sentCount: 0,
        paidCount: 0,
        overdueCount: 0,
      };

      invoices.forEach((invoice) => {
        const amount = parseFloat(invoice.total_amount?.toString() || '0');
        stats.totalAmount += amount;

        switch (invoice.status) {
          case 'draft':
            stats.draftCount++;
            break;
          case 'sent':
            stats.sentCount++;
            // Check if overdue
            if (invoice.due_date && new Date(invoice.due_date) < now) {
              stats.overdueCount++;
              stats.overdueAmount += amount;
            }
            break;
          case 'paid':
            stats.paidCount++;
            stats.paidAmount += amount;
            break;
          case 'overdue':
            stats.overdueCount++;
            stats.overdueAmount += amount;
            break;
        }
      });

      return stats;
    } catch (error) {
      console.error('Error fetching invoice stats:', error);
      return {
        totalInvoices: 0,
        totalAmount: 0,
        paidAmount: 0,
        overdueAmount: 0,
        draftCount: 0,
        sentCount: 0,
        paidCount: 0,
        overdueCount: 0,
      };
    }
  }

  async generateInvoiceNumber(): Promise<string> {
    try {
      const { data, error } = await this.supabase
        .from('invoices')
        .select('invoice_number')
        .order('created_at', { ascending: false })
        .limit(1);

      if (error) throw error;

      const year = new Date().getFullYear();
      let nextNumber = 1;

      if (data && data.length > 0) {
        const lastInvoiceNumber = data[0].invoice_number;
        const match = lastInvoiceNumber.match(/(\d{4})-(\d+)/);
        if (match && match[1] === year.toString()) {
          nextNumber = parseInt(match[2]) + 1;
        }
      }

      return `${year}-${nextNumber.toString().padStart(4, '0')}`;
    } catch (error) {
      console.error('Error generating invoice number:', error);
      const year = new Date().getFullYear();
      return `${year}-0001`;
    }
  }
}

// Export singleton instance
export const invoiceService = new InvoiceService();
