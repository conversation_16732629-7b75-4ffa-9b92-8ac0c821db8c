import React from 'react';

interface MetricCardProps {
  title: string;
  value: string | number;
  percentageChange: number;
  icon: React.ReactNode;
  subtitle?: string;
  iconColor?: string;
}

const MetricCard: React.FC<MetricCardProps> = ({ 
  title, 
  value, 
  percentageChange, 
  icon,
  subtitle,
  iconColor = 'text-blue-500'
}) => {
  const isPositive = percentageChange >= 0;
  
  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-5">
      <div className="flex justify-between items-start">
        <div>
          <h3 className="text-sm font-medium text-gray-600 dark:text-gray-400">{title}</h3>
          <div className="mt-2 mb-1">
            <span className="text-3xl font-bold text-gray-900 dark:text-white">{value}</span>
          </div>
          
          <div className="flex items-center">
            <div className={`flex items-center ${isPositive ? 'text-green-500' : 'text-red-500'}`}>
              {isPositive ? (
                <svg className="w-4 h-4" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M12 5L19 12M19 12H5M19 12V5" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                </svg>
              ) : (
                <svg className="w-4 h-4" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M12 19L5 12M5 12H19M5 12V19" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                </svg>
              )}
              <span className="ml-1 text-sm font-medium">
                {Math.abs(percentageChange)}% 
              </span>
              <span className="ml-1 text-sm text-gray-500 dark:text-gray-400">ökning</span>
            </div>
            <div className="ml-2 text-sm text-gray-500 dark:text-gray-400">
              från förra månaden
            </div>
          </div>
          {subtitle && (
            <div className="mt-2 text-sm text-gray-500 dark:text-gray-400">
              {subtitle}
            </div>
          )}
        </div>
        <div className={`rounded-full p-2 ${iconColor} bg-opacity-10`}>
          {icon}
        </div>
      </div>
    </div>
  );
};

const SalesMetrics: React.FC = () => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      <MetricCard 
        title="Aktiva prospekt"
        value="24"
        percentageChange={8}
        iconColor="text-blue-500 bg-blue-100 dark:bg-blue-900/30"
        icon={
          <svg className="w-6 h-6 text-blue-500" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M17 20H22V18C22 16.3431 20.6569 15 19 15C18.0444 15 17.1931 15.4468 16.6438 16.1429M17 20H7M17 20V18C17 17.3438 16.8736 16.717 16.6438 16.1429M16.6438 16.1429C15.6564 14.484 13.9498 13.5 12.0002 13.5C10.0506 13.5 8.34399 14.484 7.35662 16.1429M7 20H2V18C2 16.3431 3.34315 15 5 15C5.95561 15 6.80686 15.4468 7.35662 16.1429M15 7C15 8.65685 13.6569 10 12 10C10.3431 10 9 8.65685 9 7C9 5.34315 10.3431 4 12 4C13.6569 4 15 5.34315 15 7Z" 
            stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
          </svg>
        }
        subtitle="5 nya prospekt denna månad"
      />

      <MetricCard 
        title="Offertvärde"
        value="732 000 kr"
        percentageChange={12}
        iconColor="text-green-500 bg-green-100 dark:bg-green-900/30"
        icon={
          <svg className="w-6 h-6 text-green-500" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M9 5H7C5.89543 5 5 5.89543 5 7V19C5 20.1046 5.89543 21 7 21H17C18.1046 21 19 20.1046 19 19V7C19 5.89543 18.1046 5 17 5H15M9 5C9 6.10457 9.89543 7 11 7H13C14.1046 7 15 6.10457 15 5M9 5C9 3.89543 9.89543 3 11 3H13C14.1046 3 15 3.89543 15 5M12 12H15M12 16H15M9 12H9.01M9 16H9.01" 
            stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
          </svg>
        }
        subtitle="9 godkända offerter"
      />

      <MetricCard 
        title="Väntande offerter"
        value="121 000 kr"
        percentageChange={5}
        iconColor="text-amber-500 bg-amber-100 dark:bg-amber-900/30"
        icon={
          <svg className="w-6 h-6 text-amber-500" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M12 8V12L15 15M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" 
            stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
          </svg>
        }
        subtitle="2 väntande offerter"
      />

      <MetricCard 
        title="Konverteringsgrad"
        value="68%"
        percentageChange={7}
        iconColor="text-indigo-500 bg-indigo-100 dark:bg-indigo-900/30"
        icon={
          <svg className="w-6 h-6 text-indigo-500" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M16 8L8 16M8.00001 8L16 16M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" 
            stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
          </svg>
        }
        subtitle="15 av 22 prospekt konverterade"
      />
    </div>
  );
};

export default SalesMetrics; 