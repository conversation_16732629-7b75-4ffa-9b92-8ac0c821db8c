'use client'

import React from 'react'
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { formatCurrency, formatDate } from '@/lib/utils'
import { LineItem } from '@/lib/types'

interface InvoicePreviewProps {
  invoiceNumber: string
  customerName: string
  customerEmail?: string
  customerAddress?: string
  projectName?: string
  issueDate: string
  dueDate: string
  lineItems: LineItem[]
  subtotal: number
  vatAmount: number
  total: number
  vatRate: number
  currency: string
  paymentTerms: string
}

export const InvoicePreview = React.memo(function InvoicePreview({
  invoiceNumber,
  customerName,
  customerEmail,
  customerAddress,
  projectName,
  issueDate,
  dueDate,
  lineItems,
  subtotal,
  vatAmount,
  total,
  vatRate,
  currency,
  paymentTerms
}: InvoicePreviewProps) {
  const validLineItems = lineItems.filter(item => item.description.trim())

  return (
    <Card>
      <CardHeader>
        <CardTitle>Förhandsvisning</CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Header */}
        <div className="border-b pb-4">
          <div className="flex justify-between items-start">
            <div>
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white">FAKTURA</h2>
              <p className="text-lg font-semibold text-gray-700 dark:text-gray-300">{invoiceNumber}</p>
            </div>
            <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
              Utkast
            </Badge>
          </div>
        </div>

        {/* Customer & Invoice Info */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h3 className="font-semibold text-gray-900 dark:text-white mb-2">Faktureras till:</h3>
            <div className="text-gray-700 dark:text-gray-300">
              <p className="font-medium">{customerName || 'Ingen kund vald'}</p>
              {customerEmail && <p>{customerEmail}</p>}
              {customerAddress && <p>{customerAddress}</p>}
            </div>
          </div>
          <div>
            <h3 className="font-semibold text-gray-900 dark:text-white mb-2">Fakturauppgifter:</h3>
            <div className="text-gray-700 dark:text-gray-300 space-y-1">
              <div className="flex justify-between">
                <span>Fakturanummer:</span>
                <span>{invoiceNumber}</span>
              </div>
              <div className="flex justify-between">
                <span>Utfärdad:</span>
                <span>{issueDate ? formatDate(issueDate) : '-'}</span>
              </div>
              <div className="flex justify-between">
                <span>Förfaller:</span>
                <span>{dueDate ? formatDate(dueDate) : '-'}</span>
              </div>
              <div className="flex justify-between">
                <span>Betalningsvillkor:</span>
                <span>{paymentTerms}</span>
              </div>
              {projectName && (
                <div className="flex justify-between">
                  <span>Projekt:</span>
                  <span>{projectName}</span>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Line Items */}
        <div>
          <h3 className="font-semibold text-gray-900 dark:text-white mb-3">Specifikation:</h3>
          {validLineItems.length > 0 ? (
            <div className="overflow-x-auto">
              <table className="w-full text-sm">
                <thead>
                  <tr className="border-b border-gray-200 dark:border-gray-700">
                    <th className="text-left py-2 text-gray-500 dark:text-gray-400">Beskrivning</th>
                    <th className="text-right py-2 text-gray-500 dark:text-gray-400">Antal</th>
                    <th className="text-right py-2 text-gray-500 dark:text-gray-400">Pris</th>
                    <th className="text-right py-2 text-gray-500 dark:text-gray-400">Summa</th>
                  </tr>
                </thead>
                <tbody>
                  {validLineItems.map((item, index) => (
                    <tr key={index} className="border-b border-gray-100 dark:border-gray-800">
                      <td className="py-2 text-gray-900 dark:text-white">{item.description}</td>
                      <td className="py-2 text-right text-gray-900 dark:text-white">{item.quantity}</td>
                      <td className="py-2 text-right text-gray-900 dark:text-white">
                        {formatCurrency(item.unit_price, currency)}
                      </td>
                      <td className="py-2 text-right font-medium text-gray-900 dark:text-white">
                        {formatCurrency(item.amount, currency)}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500 dark:text-gray-400">
              <p>Inga fakturarader tillagda än</p>
            </div>
          )}
        </div>

        {/* Totals */}
        {validLineItems.length > 0 && (
          <div className="border-t pt-4">
            <div className="flex justify-end">
              <div className="w-64 space-y-2">
                <div className="flex justify-between text-gray-700 dark:text-gray-300">
                  <span>Subtotal:</span>
                  <span>{formatCurrency(subtotal, currency)}</span>
                </div>
                <div className="flex justify-between text-gray-700 dark:text-gray-300">
                  <span>Moms ({vatRate}%):</span>
                  <span>{formatCurrency(vatAmount, currency)}</span>
                </div>
                <div className="flex justify-between text-lg font-bold text-gray-900 dark:text-white border-t pt-2">
                  <span>Totalt att betala:</span>
                  <span>{formatCurrency(total, currency)}</span>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Footer */}
        <div className="border-t pt-4 text-center text-sm text-gray-500 dark:text-gray-400">
          <p>Tack för ditt förtroende!</p>
        </div>
      </CardContent>
    </Card>
  )
})
