'use client'

import React, { useState, useEffect, useMemo, useCallback } from 'react'
import { Save, Plus, Trash2, Calculator, User, Building, Calendar, CreditCard } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { invoiceService } from '@/services/invoiceService'
import { customerService } from '@/services/customerService'
import { Invoice, LineItem } from '@/lib/types'
import { Customer } from '@/models'
import { formatCurrency } from '@/lib/utils'
import { toast } from 'sonner'
import { CustomerSelect } from '@/components/ui/customer-select'
import { InvoicePreview } from '@/components/invoices/invoice-preview'

interface CreateInvoiceFormProps {
  onSuccess: (invoiceId: string) => void
  onCancel: () => void
  loading: boolean
  setLoading: (loading: boolean) => void
}

export const CreateInvoiceForm = React.memo(function CreateInvoiceForm({
  onSuccess,
  onCancel,
  loading,
  setLoading
}: CreateInvoiceFormProps) {
  const [customers, setCustomers] = useState<Customer[]>([])
  const [customersLoading, setCustomersLoading] = useState(true)
  const [invoiceNumber, setInvoiceNumber] = useState('')
  
  // Form state
  const [formData, setFormData] = useState({
    customer_id: '',
    customer_name: '',
    customer_email: '',
    customer_phone: '',
    customer_address: '',
    project_name: '',
    description: '',
    issue_date: new Date().toISOString().split('T')[0],
    due_date: '',
    payment_terms: '30 dagar netto',
    currency: 'SEK',
    vat_rate: 25,
    notes: ''
  })

  const [lineItems, setLineItems] = useState<LineItem[]>([
    { description: '', quantity: 1, unit_price: 0, amount: 0 }
  ])

  const [errors, setErrors] = useState<Record<string, string>>({})

  // Load initial data
  useEffect(() => {
    loadInitialData()
  }, [])

  const loadInitialData = useCallback(async () => {
    try {
      const [customersData, invoiceNum] = await Promise.all([
        customerService.getAllCustomers(),
        invoiceService.generateInvoiceNumber()
      ])
      
      setCustomers(customersData)
      setInvoiceNumber(invoiceNum)
      
      // Set default due date (30 days from now)
      const dueDate = new Date()
      dueDate.setDate(dueDate.getDate() + 30)
      setFormData(prev => ({
        ...prev,
        due_date: dueDate.toISOString().split('T')[0]
      }))
    } catch (error) {
      console.error('Error loading initial data:', error)
      toast.error('Kunde inte ladda initial data')
    } finally {
      setCustomersLoading(false)
    }
  }, [])

  // Calculate totals
  const calculations = useMemo(() => {
    const subtotal = lineItems.reduce((sum, item) => sum + item.amount, 0)
    const vatAmount = subtotal * (formData.vat_rate / 100)
    const total = subtotal + vatAmount

    return {
      subtotal,
      vatAmount,
      total
    }
  }, [lineItems, formData.vat_rate])

  // Handle customer selection
  const handleCustomerSelect = useCallback((customerId: string) => {
    const customer = customers.find(c => c.id === customerId)
    if (customer) {
      setFormData(prev => ({
        ...prev,
        customer_id: customerId,
        customer_name: customer.name,
        customer_email: customer.email || '',
        customer_phone: customer.phone || '',
        customer_address: customer.address || ''
      }))
    }
  }, [customers])

  // Handle line item changes
  const updateLineItem = useCallback((index: number, field: keyof LineItem, value: string | number) => {
    setLineItems(prev => {
      const newItems = [...prev]
      newItems[index] = { ...newItems[index], [field]: value }
      
      // Recalculate amount for this line item
      if (field === 'quantity' || field === 'unit_price') {
        newItems[index].amount = newItems[index].quantity * newItems[index].unit_price
      }
      
      return newItems
    })
  }, [])

  const addLineItem = useCallback(() => {
    setLineItems(prev => [...prev, { description: '', quantity: 1, unit_price: 0, amount: 0 }])
  }, [])

  const removeLineItem = useCallback((index: number) => {
    if (lineItems.length > 1) {
      setLineItems(prev => prev.filter((_, i) => i !== index))
    }
  }, [lineItems.length])

  // Form validation
  const validateForm = useCallback(() => {
    const newErrors: Record<string, string> = {}

    if (!formData.customer_id) {
      newErrors.customer_id = 'Kund måste väljas'
    }

    if (!formData.issue_date) {
      newErrors.issue_date = 'Utfärdandedatum krävs'
    }

    if (!formData.due_date) {
      newErrors.due_date = 'Förfallodatum krävs'
    }

    if (lineItems.length === 0 || lineItems.every(item => !item.description.trim())) {
      newErrors.lineItems = 'Minst en fakturarad krävs'
    }

    lineItems.forEach((item, index) => {
      if (item.description.trim() && (item.quantity <= 0 || item.unit_price < 0)) {
        newErrors[`lineItem_${index}`] = 'Ogiltiga värden för antal eller pris'
      }
    })

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }, [formData, lineItems])

  // Handle form submission
  const handleSubmit = useCallback(async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      toast.error('Vänligen korrigera felen i formuläret')
      return
    }

    try {
      setLoading(true)

      const invoiceData: Omit<Invoice, 'id' | 'created_at' | 'updated_at'> = {
        invoice_number: invoiceNumber,
        customer_id: formData.customer_id,
        customer_name: formData.customer_name,
        customer_email: formData.customer_email,
        customer_phone: formData.customer_phone,
        customer_address: formData.customer_address,
        project_name: formData.project_name || undefined,
        description: formData.description || undefined,
        line_items: lineItems.filter(item => item.description.trim()),
        subtotal: calculations.subtotal,
        vat_amount: calculations.vatAmount,
        total_amount: calculations.total,
        vat_rate: formData.vat_rate,
        currency: formData.currency,
        status: 'draft',
        issue_date: formData.issue_date,
        due_date: formData.due_date,
        payment_terms: formData.payment_terms,
        notes: formData.notes || undefined
      }

      const createdInvoice = await invoiceService.createInvoice(invoiceData)
      
      if (createdInvoice) {
        onSuccess(createdInvoice.id)
      } else {
        throw new Error('Kunde inte skapa faktura')
      }
    } catch (error) {
      console.error('Error creating invoice:', error)
      toast.error('Kunde inte skapa faktura')
    } finally {
      setLoading(false)
    }
  }, [formData, lineItems, invoiceNumber, calculations, validateForm, setLoading, onSuccess])

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Form */}
        <div className="lg:col-span-2 space-y-6">
          {/* Customer Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5" />
                Kundinformation
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="customer">Kund *</Label>
                <CustomerSelect
                  customers={customers}
                  value={formData.customer_id}
                  onValueChange={handleCustomerSelect}
                  placeholder={customersLoading ? "Laddar kunder..." : "Välj kund"}
                  disabled={customersLoading}
                  error={errors.customer_id}
                />
              </div>

              {formData.customer_id && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <div>
                    <Label className="text-sm font-medium text-gray-500">E-post</Label>
                    <p className="text-gray-900 dark:text-white">{formData.customer_email || 'Ej angiven'}</p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium text-gray-500">Telefon</Label>
                    <p className="text-gray-900 dark:text-white">{formData.customer_phone || 'Ej angiven'}</p>
                  </div>
                  <div className="md:col-span-2">
                    <Label className="text-sm font-medium text-gray-500">Adress</Label>
                    <p className="text-gray-900 dark:text-white">{formData.customer_address || 'Ej angiven'}</p>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Invoice Details */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Building className="h-5 w-5" />
                Fakturauppgifter
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="invoice_number">Fakturanummer</Label>
                  <Input
                    id="invoice_number"
                    value={invoiceNumber}
                    disabled
                    className="bg-gray-50 dark:bg-gray-800"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="currency">Valuta</Label>
                  <Select value={formData.currency} onValueChange={(value) => setFormData(prev => ({ ...prev, currency: value }))}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="SEK">SEK (kr)</SelectItem>
                      <SelectItem value="EUR">EUR (€)</SelectItem>
                      <SelectItem value="USD">USD ($)</SelectItem>
                      <SelectItem value="NOK">NOK (kr)</SelectItem>
                      <SelectItem value="DKK">DKK (kr)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="issue_date">Utfärdandedatum *</Label>
                  <Input
                    id="issue_date"
                    type="date"
                    value={formData.issue_date}
                    onChange={(e) => setFormData(prev => ({ ...prev, issue_date: e.target.value }))}
                    className={errors.issue_date ? 'border-red-500' : ''}
                  />
                  {errors.issue_date && (
                    <p className="text-sm text-red-600">{errors.issue_date}</p>
                  )}
                </div>
                <div className="space-y-2">
                  <Label htmlFor="due_date">Förfallodatum *</Label>
                  <Input
                    id="due_date"
                    type="date"
                    value={formData.due_date}
                    onChange={(e) => setFormData(prev => ({ ...prev, due_date: e.target.value }))}
                    className={errors.due_date ? 'border-red-500' : ''}
                  />
                  {errors.due_date && (
                    <p className="text-sm text-red-600">{errors.due_date}</p>
                  )}
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="project_name">Projektnamn</Label>
                  <Input
                    id="project_name"
                    value={formData.project_name}
                    onChange={(e) => setFormData(prev => ({ ...prev, project_name: e.target.value }))}
                    placeholder="Valfritt projektnamn"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="payment_terms">Betalningsvillkor</Label>
                  <Select value={formData.payment_terms} onValueChange={(value) => setFormData(prev => ({ ...prev, payment_terms: value }))}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="30 dagar netto">30 dagar netto</SelectItem>
                      <SelectItem value="14 dagar netto">14 dagar netto</SelectItem>
                      <SelectItem value="7 dagar netto">7 dagar netto</SelectItem>
                      <SelectItem value="Kontant">Kontant</SelectItem>
                      <SelectItem value="Förskottsbetalning">Förskottsbetalning</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Beskrivning</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                  placeholder="Valfri beskrivning av fakturan"
                  rows={3}
                />
              </div>
            </CardContent>
          </Card>

          {/* Line Items */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <CreditCard className="h-5 w-5" />
                  Fakturarader
                </div>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={addLineItem}
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Lägg till rad
                </Button>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {errors.lineItems && (
                <p className="text-sm text-red-600">{errors.lineItems}</p>
              )}

              <div className="space-y-4">
                {lineItems.map((item, index) => (
                  <div key={index} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 space-y-4">
                    <div className="flex items-center justify-between">
                      <h4 className="font-medium text-gray-900 dark:text-white">Rad {index + 1}</h4>
                      {lineItems.length > 1 && (
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => removeLineItem(index)}
                          className="text-red-600 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-900/20"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      )}
                    </div>

                    <div className="space-y-3">
                      <div className="space-y-2">
                        <Label htmlFor={`description_${index}`}>Beskrivning *</Label>
                        <Textarea
                          id={`description_${index}`}
                          value={item.description}
                          onChange={(e) => updateLineItem(index, 'description', e.target.value)}
                          placeholder="Beskrivning av tjänst eller produkt"
                          rows={2}
                          className={errors[`lineItem_${index}`] ? 'border-red-500' : ''}
                        />
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                        <div className="space-y-2">
                          <Label htmlFor={`quantity_${index}`}>Antal</Label>
                          <Input
                            id={`quantity_${index}`}
                            type="number"
                            min="0"
                            step="0.01"
                            value={item.quantity}
                            onChange={(e) => updateLineItem(index, 'quantity', parseFloat(e.target.value) || 0)}
                            className={errors[`lineItem_${index}`] ? 'border-red-500' : ''}
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor={`unit_price_${index}`}>Pris per enhet</Label>
                          <Input
                            id={`unit_price_${index}`}
                            type="number"
                            min="0"
                            step="0.01"
                            value={item.unit_price}
                            onChange={(e) => updateLineItem(index, 'unit_price', parseFloat(e.target.value) || 0)}
                            className={errors[`lineItem_${index}`] ? 'border-red-500' : ''}
                          />
                        </div>
                        <div className="space-y-2">
                          <Label>Summa</Label>
                          <div className="h-10 px-3 py-2 bg-gray-50 dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-md flex items-center">
                            <span className="font-medium">{formatCurrency(item.amount, formData.currency)}</span>
                          </div>
                        </div>
                      </div>

                      {errors[`lineItem_${index}`] && (
                        <p className="text-sm text-red-600">{errors[`lineItem_${index}`]}</p>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Notes */}
          <Card>
            <CardHeader>
              <CardTitle>Anteckningar</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <Label htmlFor="notes">Interna anteckningar</Label>
                <Textarea
                  id="notes"
                  value={formData.notes}
                  onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
                  placeholder="Interna anteckningar som inte visas på fakturan"
                  rows={3}
                />
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  Dessa anteckningar visas endast internt och inte på den utskrivna fakturan.
                </p>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Summary Sidebar */}
        <div className="space-y-6">
          {/* Invoice Summary */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calculator className="h-5 w-5" />
                Sammanfattning
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Subtotal:</span>
                  <span className="font-medium">{formatCurrency(calculations.subtotal, formData.currency)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Moms ({formData.vat_rate}%):</span>
                  <span className="font-medium">{formatCurrency(calculations.vatAmount, formData.currency)}</span>
                </div>
                <div className="border-t pt-3">
                  <div className="flex justify-between text-lg font-bold">
                    <span>Total:</span>
                    <span>{formatCurrency(calculations.total, formData.currency)}</span>
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="vat_rate">Momssats (%)</Label>
                <Select 
                  value={formData.vat_rate.toString()} 
                  onValueChange={(value) => setFormData(prev => ({ ...prev, vat_rate: parseInt(value) }))}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="0">0% (Momsbefriad)</SelectItem>
                    <SelectItem value="6">6% (Böcker, tidningar)</SelectItem>
                    <SelectItem value="12">12% (Livsmedel, hotell)</SelectItem>
                    <SelectItem value="25">25% (Standard)</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {/* Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Åtgärder</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <Button 
                type="submit" 
                className="w-full bg-[#5D5FEF] hover:bg-[#4B4AEF]"
                disabled={loading}
              >
                {loading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2"></div>
                    Skapar...
                  </>
                ) : (
                  <>
                    <Save className="h-4 w-4 mr-2" />
                    Spara faktura
                  </>
                )}
              </Button>
              <Button 
                type="button" 
                variant="outline" 
                className="w-full"
                onClick={onCancel}
                disabled={loading}
              >
                Avbryt
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Invoice Preview */}
      <div className="mt-8">
        <InvoicePreview
          invoiceNumber={invoiceNumber}
          customerName={formData.customer_name}
          customerEmail={formData.customer_email}
          customerAddress={formData.customer_address}
          projectName={formData.project_name}
          issueDate={formData.issue_date}
          dueDate={formData.due_date}
          lineItems={lineItems}
          subtotal={calculations.subtotal}
          vatAmount={calculations.vatAmount}
          total={calculations.total}
          vatRate={formData.vat_rate}
          currency={formData.currency}
          paymentTerms={formData.payment_terms}
        />
      </div>
    </form>
  )
})
