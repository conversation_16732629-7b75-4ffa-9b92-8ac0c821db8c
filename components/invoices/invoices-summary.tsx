'use client'

import React, { useEffect, useState, useMemo } from 'react'
import { DollarSign, FileText, CheckCircle, AlertTriangle, Clock, Send } from 'lucide-react'
import { StatsCards } from '@/components/ui/stats-cards'
import { invoiceService } from '@/services/invoiceService'
import { formatCurrency } from '@/lib/utils'
import { StatsSkeleton } from '@/components/ui/loading-skeleton'

const InvoicesSummaryComponent = React.memo(function InvoicesSummary() {
  const [stats, setStats] = useState({
    totalInvoices: 0,
    totalAmount: 0,
    paidAmount: 0,
    overdueAmount: 0,
    draftCount: 0,
    sentCount: 0,
    paidCount: 0,
    overdueCount: 0,
  })
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchStats()
  }, [])

  const fetchStats = async () => {
    try {
      setLoading(true)
      const data = await invoiceService.getInvoiceStats()
      setStats(data)
    } catch (error) {
      console.error('Error fetching invoice stats:', error)
    } finally {
      setLoading(false)
    }
  }

  const metrics = useMemo(() => [
    {
      label: "Totalt fakturor",
      value: stats.totalInvoices,
      icon: <FileText className="w-5 h-5 text-blue-600 dark:text-blue-400" />,
      change: "+12%",
      changeType: "positive" as const,
      changeLabel: "senaste månaden"
    },
    {
      label: "Total fakturering",
      value: formatCurrency(stats.totalAmount, 'SEK'),
      icon: <DollarSign className="w-5 h-5 text-green-600 dark:text-green-400" />,
      change: "+18%",
      changeType: "positive" as const,
      changeLabel: "senaste månaden"
    },
    {
      label: "Betalt belopp",
      value: formatCurrency(stats.paidAmount, 'SEK'),
      icon: <CheckCircle className="w-5 h-5 text-emerald-600 dark:text-emerald-400" />,
      change: "+22%",
      changeType: "positive" as const,
      changeLabel: "senaste månaden"
    },
    {
      label: "Förfallna fakturor",
      value: stats.overdueCount,
      icon: <AlertTriangle className="w-5 h-5 text-red-600 dark:text-red-400" />,
      change: "-5%",
      changeType: "negative" as const,
      changeLabel: "senaste månaden"
    },
    {
      label: "Utkast",
      value: stats.draftCount,
      icon: <Clock className="w-5 h-5 text-gray-600 dark:text-gray-400" />,
      change: "+3%",
      changeType: "positive" as const,
      changeLabel: "senaste månaden"
    },
    {
      label: "Skickade",
      value: stats.sentCount,
      icon: <Send className="w-5 h-5 text-blue-600 dark:text-blue-400" />,
      change: "+8%",
      changeType: "positive" as const,
      changeLabel: "senaste månaden"
    }
  ], [stats])

  if (loading) {
    return <StatsSkeleton count={6} />
  }

  return <StatsCards metrics={metrics} />
})

InvoicesSummaryComponent.displayName = 'InvoicesSummary'
export const InvoicesSummary = InvoicesSummaryComponent
