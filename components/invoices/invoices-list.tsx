'use client'

import React, { useEffect, useState, useMemo, useCallback } from 'react'
import {
  Download,
  Eye,
  Edit,
  Send,
  FileText,
  AlertTriangle,
  CheckCircle,
  Clock,
  XCircle
} from "lucide-react"
import { formatCurrency, formatDate } from '@/lib/utils'
import { ExpandableList } from '@/components/ui/expandable-list'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import Link from 'next/link'
import { invoiceService } from '@/services/invoiceService'
import { Invoice } from '@/lib/types'
import { toast } from 'sonner'
import { TableSkeleton } from '@/components/ui/loading-skeleton'

const InvoicesListComponent = React.memo(function InvoicesList() {
  const [invoices, setInvoices] = useState<Invoice[]>([])
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')

  const fetchInvoices = useCallback(async () => {
    try {
      setLoading(true)
      const data = await invoiceService.getAllInvoices()
      setInvoices(data)
    } catch (error) {
      console.error('Error fetching invoices:', error)
      toast.error('Kunde inte hämta fakturor')
    } finally {
      setLoading(false)
    }
  }, [])

  useEffect(() => {
    fetchInvoices()
  }, [fetchInvoices])

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'draft':
        return <FileText className="h-4 w-4" />
      case 'sent':
        return <Send className="h-4 w-4" />
      case 'paid':
        return <CheckCircle className="h-4 w-4" />
      case 'overdue':
        return <AlertTriangle className="h-4 w-4" />
      case 'cancelled':
        return <XCircle className="h-4 w-4" />
      default:
        return <Clock className="h-4 w-4" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'draft':
        return 'bg-gray-100 text-gray-700 dark:bg-gray-800 dark:text-gray-300'
      case 'sent':
        return 'bg-blue-100 text-blue-700 dark:bg-blue-900/20 dark:text-blue-400'
      case 'paid':
        return 'bg-green-100 text-green-700 dark:bg-green-900/20 dark:text-green-400'
      case 'overdue':
        return 'bg-red-100 text-red-700 dark:bg-red-900/20 dark:text-red-400'
      case 'cancelled':
        return 'bg-gray-100 text-gray-700 dark:bg-gray-800 dark:text-gray-300'
      default:
        return 'bg-gray-100 text-gray-700 dark:bg-gray-800 dark:text-gray-300'
    }
  }

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'draft':
        return 'Utkast'
      case 'sent':
        return 'Skickad'
      case 'paid':
        return 'Betald'
      case 'overdue':
        return 'Förfallen'
      case 'cancelled':
        return 'Avbruten'
      default:
        return status
    }
  }

  const filteredInvoices = useMemo(() => {
    if (!searchQuery) return invoices
    const query = searchQuery.toLowerCase()
    return invoices.filter(invoice =>
      invoice.invoice_number.toLowerCase().includes(query) ||
      invoice.customer_name.toLowerCase().includes(query) ||
      (invoice.project_name && invoice.project_name.toLowerCase().includes(query))
    )
  }, [invoices, searchQuery])

  const columns = [
    { key: 'invoice_number', label: 'Fakturanummer' },
    { key: 'customer', label: 'Kund' },
    { key: 'amount', label: 'Belopp' },
    { key: 'status', label: 'Status' },
    { key: 'due_date', label: 'Förfallodatum' },
    { key: 'actions', label: 'Åtgärder', className: 'text-right' }
  ]

  const renderCell = (invoice: Invoice, columnKey: string) => {
    switch (columnKey) {
      case 'invoice_number':
        return (
          <div>
            <Link href={`/dashboard/fakturor/${invoice.id}`} className="text-blue-600 dark:text-blue-400 hover:underline font-medium">
              {invoice.invoice_number}
            </Link>
            {invoice.project_name && (
              <p className="text-sm text-gray-500 dark:text-gray-400">{invoice.project_name}</p>
            )}
          </div>
        )
      case 'customer':
        return (
          <div>
            <p className="font-medium text-gray-900 dark:text-white">{invoice.customer_name}</p>
            {invoice.customer_email && (
              <p className="text-sm text-gray-500 dark:text-gray-400">{invoice.customer_email}</p>
            )}
          </div>
        )
      case 'amount':
        return (
          <div className="text-right">
            <p className="font-medium text-gray-900 dark:text-white">
              {formatCurrency(invoice.total_amount, invoice.currency)}
            </p>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              inkl. moms
            </p>
          </div>
        )
      case 'status':
        return (
          <Badge className={`inline-flex items-center gap-1 ${getStatusColor(invoice.status)}`}>
            {getStatusIcon(invoice.status)}
            {getStatusLabel(invoice.status)}
          </Badge>
        )
      case 'due_date':
        return (
          <div>
            {invoice.due_date ? (
              <>
                <p className="text-gray-900 dark:text-white">{formatDate(invoice.due_date)}</p>
                {invoice.status === 'sent' && new Date(invoice.due_date) < new Date() && (
                  <p className="text-sm text-red-600 dark:text-red-400">Förfallen</p>
                )}
              </>
            ) : (
              <span className="text-gray-500 dark:text-gray-400">-</span>
            )}
          </div>
        )
      case 'actions':
        return (
          <div className="flex justify-end space-x-2" onClick={(e) => e.stopPropagation()}>
            <Link href={`/dashboard/fakturor/${invoice.id}`} className="text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-200">
              <Eye className="h-5 w-5" />
            </Link>
            <Link href={`/dashboard/fakturor/${invoice.id}/edit`} className="text-indigo-600 dark:text-indigo-400 hover:text-indigo-900 dark:hover:text-indigo-200">
              <Edit className="h-5 w-5" />
            </Link>
            <button className="text-green-600 dark:text-green-400 hover:text-green-900 dark:hover:text-green-200">
              <Download className="h-5 w-5" />
            </button>
          </div>
        )
      default:
        return null
    }
  }

  const renderExpandedContent = (invoice: Invoice) => (
    <div className="flex flex-col md:flex-row gap-8">
      <div className="flex-1 space-y-3">
        <div className="text-lg font-medium text-gray-900 dark:text-white mb-4">Fakturauppgifter</div>
        <div><span className="font-medium text-gray-700 dark:text-gray-300">Fakturanummer:</span> <span className="text-gray-900 dark:text-white">{invoice.invoice_number}</span></div>
        <div><span className="font-medium text-gray-700 dark:text-gray-300">Utfärdad:</span> <span className="text-gray-900 dark:text-white">{formatDate(invoice.issue_date)}</span></div>
        {invoice.due_date && (
          <div><span className="font-medium text-gray-700 dark:text-gray-300">Förfallodatum:</span> <span className="text-gray-900 dark:text-white">{formatDate(invoice.due_date)}</span></div>
        )}
        {invoice.payment_date && (
          <div><span className="font-medium text-gray-700 dark:text-gray-300">Betald:</span> <span className="text-gray-900 dark:text-white">{formatDate(invoice.payment_date)}</span></div>
        )}
        <div><span className="font-medium text-gray-700 dark:text-gray-300">Betalningsvillkor:</span> <span className="text-gray-900 dark:text-white">{invoice.payment_terms}</span></div>
        {invoice.description && (
          <div><span className="font-medium text-gray-700 dark:text-gray-300">Beskrivning:</span> <span className="text-gray-900 dark:text-white">{invoice.description}</span></div>
        )}
      </div>
      <div className="flex-1">
        <div className="text-lg font-medium text-gray-900 dark:text-white mb-4">Snabbåtgärder</div>
        <div className="space-y-3">
          <Link href={`/dashboard/fakturor/${invoice.id}`} className="block">
            <Button variant="outline" className="w-full justify-start">
              <Eye className="w-4 h-4 mr-2" />
              Visa faktura
            </Button>
          </Link>
          <Link href={`/dashboard/fakturor/${invoice.id}/edit`} className="block">
            <Button variant="outline" className="w-full justify-start">
              <Edit className="w-4 h-4 mr-2" />
              Redigera faktura
            </Button>
          </Link>
          <Button variant="outline" className="w-full justify-start">
            <Download className="w-4 h-4 mr-2" />
            Ladda ner PDF
          </Button>
          {invoice.status === 'draft' && (
            <Button variant="outline" className="w-full justify-start">
              <Send className="w-4 h-4 mr-2" />
              Skicka faktura
            </Button>
          )}
        </div>
      </div>
    </div>
  )

  if (loading) {
    return <TableSkeleton rows={8} />
  }

  return (
    <ExpandableList
      data={filteredInvoices}
      columns={columns}
      renderCell={renderCell}
      renderExpandedContent={renderExpandedContent}
      getRowKey={(invoice) => invoice.id}
      searchQuery={searchQuery}
      onSearchChange={setSearchQuery}
      searchPlaceholder="Sök fakturor..."
    />
  )
})

InvoicesListComponent.displayName = 'InvoicesList'
export const InvoicesList = InvoicesListComponent
