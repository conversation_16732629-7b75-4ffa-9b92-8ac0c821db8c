'use client'

import React, { useState, useEffect } from 'react'
import { Settings, Save, Moon, Sun, Globe, Bell, Shield, Database } from 'lucide-react'
import { But<PERSON> } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card'
import { Switch } from '@/components/ui/switch'
import { useTheme } from 'next-themes'
import { toast } from 'sonner'

interface SystemSettings {
  language: string
  timezone: string
  currency: string
  date_format: string
  time_format: string
  notifications_email: boolean
  notifications_browser: boolean
  auto_backup: boolean
  backup_frequency: string
  data_retention_days: number
}

export function SystemSettings() {
  const { theme, setTheme } = useTheme()
  const [settings, setSettings] = useState<SystemSettings>({
    language: 'sv',
    timezone: 'Europe/Stockholm',
    currency: 'SEK',
    date_format: 'YYYY-MM-DD',
    time_format: '24h',
    notifications_email: true,
    notifications_browser: true,
    auto_backup: true,
    backup_frequency: 'daily',
    data_retention_days: 365
  })
  const [loading, setLoading] = useState(false)
  const [saving, setSaving] = useState(false)

  useEffect(() => {
    // Load settings from localStorage or API
    loadSettings()
  }, [])

  const loadSettings = () => {
    try {
      const savedSettings = localStorage.getItem('systemSettings')
      if (savedSettings) {
        setSettings(JSON.parse(savedSettings))
      }
    } catch (error) {
      console.error('Error loading settings:', error)
    }
  }

  const handleSave = async () => {
    try {
      setSaving(true)
      
      // Save to localStorage (in a real app, this would be saved to the database)
      localStorage.setItem('systemSettings', JSON.stringify(settings))
      
      toast.success('Systeminställningar sparade', {
        description: 'Dina systeminställningar har uppdaterats'
      })
    } catch (error) {
      console.error('Error saving settings:', error)
      toast.error('Kunde inte spara inställningar')
    } finally {
      setSaving(false)
    }
  }

  const handleSettingChange = (field: keyof SystemSettings, value: any) => {
    setSettings(prev => ({ ...prev, [field]: value }))
  }

  return (
    <div className="space-y-6">
      {/* Appearance Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Sun className="h-5 w-5" />
            Utseende
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>Tema</Label>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Välj mellan ljust, mörkt eller systemtema
              </p>
            </div>
            <Select value={theme} onValueChange={setTheme}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="light">Ljust</SelectItem>
                <SelectItem value="dark">Mörkt</SelectItem>
                <SelectItem value="system">System</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Localization Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Globe className="h-5 w-5" />
            Lokalisering
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>Språk</Label>
              <Select value={settings.language} onValueChange={(value) => handleSettingChange('language', value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="sv">Svenska</SelectItem>
                  <SelectItem value="en">English</SelectItem>
                  <SelectItem value="no">Norsk</SelectItem>
                  <SelectItem value="da">Dansk</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label>Tidszon</Label>
              <Select value={settings.timezone} onValueChange={(value) => handleSettingChange('timezone', value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Europe/Stockholm">Stockholm (CET)</SelectItem>
                  <SelectItem value="Europe/Oslo">Oslo (CET)</SelectItem>
                  <SelectItem value="Europe/Copenhagen">Köpenhamn (CET)</SelectItem>
                  <SelectItem value="UTC">UTC</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label>Valuta</Label>
              <Select value={settings.currency} onValueChange={(value) => handleSettingChange('currency', value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="SEK">SEK (kr)</SelectItem>
                  <SelectItem value="EUR">EUR (€)</SelectItem>
                  <SelectItem value="USD">USD ($)</SelectItem>
                  <SelectItem value="NOK">NOK (kr)</SelectItem>
                  <SelectItem value="DKK">DKK (kr)</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label>Datumformat</Label>
              <Select value={settings.date_format} onValueChange={(value) => handleSettingChange('date_format', value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="YYYY-MM-DD">YYYY-MM-DD</SelectItem>
                  <SelectItem value="DD/MM/YYYY">DD/MM/YYYY</SelectItem>
                  <SelectItem value="MM/DD/YYYY">MM/DD/YYYY</SelectItem>
                  <SelectItem value="DD.MM.YYYY">DD.MM.YYYY</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label>Tidsformat</Label>
              <Select value={settings.time_format} onValueChange={(value) => handleSettingChange('time_format', value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="24h">24-timmar</SelectItem>
                  <SelectItem value="12h">12-timmar</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Notification Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Bell className="h-5 w-5" />
            Notifieringar
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>E-postnotifieringar</Label>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Få notifieringar via e-post för viktiga händelser
              </p>
            </div>
            <Switch
              checked={settings.notifications_email}
              onCheckedChange={(checked) => handleSettingChange('notifications_email', checked)}
            />
          </div>
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>Webbläsarnotifieringar</Label>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Visa notifieringar i webbläsaren
              </p>
            </div>
            <Switch
              checked={settings.notifications_browser}
              onCheckedChange={(checked) => handleSettingChange('notifications_browser', checked)}
            />
          </div>
        </CardContent>
      </Card>

      {/* Data & Backup Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Database className="h-5 w-5" />
            Data & Säkerhetskopiering
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>Automatisk säkerhetskopiering</Label>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Skapa automatiska säkerhetskopior av dina data
              </p>
            </div>
            <Switch
              checked={settings.auto_backup}
              onCheckedChange={(checked) => handleSettingChange('auto_backup', checked)}
            />
          </div>
          
          {settings.auto_backup && (
            <div className="space-y-2">
              <Label>Säkerhetskopieringsfrekvens</Label>
              <Select value={settings.backup_frequency} onValueChange={(value) => handleSettingChange('backup_frequency', value)}>
                <SelectTrigger className="w-48">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="daily">Dagligen</SelectItem>
                  <SelectItem value="weekly">Veckovis</SelectItem>
                  <SelectItem value="monthly">Månadsvis</SelectItem>
                </SelectContent>
              </Select>
            </div>
          )}

          <div className="space-y-2">
            <Label>Datalagring (dagar)</Label>
            <Select 
              value={settings.data_retention_days.toString()} 
              onValueChange={(value) => handleSettingChange('data_retention_days', parseInt(value))}
            >
              <SelectTrigger className="w-48">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="90">90 dagar</SelectItem>
                <SelectItem value="180">180 dagar</SelectItem>
                <SelectItem value="365">1 år</SelectItem>
                <SelectItem value="730">2 år</SelectItem>
                <SelectItem value="1825">5 år</SelectItem>
                <SelectItem value="-1">Obegränsat</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      <div className="flex justify-end">
        <Button onClick={handleSave} disabled={saving}>
          {saving ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2"></div>
              Sparar...
            </>
          ) : (
            <>
              <Save className="h-4 w-4 mr-2" />
              Spara inställningar
            </>
          )}
        </Button>
      </div>
    </div>
  )
}
