'use client'

import React, { useState, useEffect } from 'react'
import { User, Mail, Phone, MapPin, Building, Save, Upload } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card'
import { useAuth } from '@/context/AuthContext'
import { supabase } from '@/lib/supabase/supabase'
import { toast } from 'sonner'

interface UserProfile {
  id: string
  email: string
  first_name?: string
  last_name?: string
  phone?: string
  company?: string
  address?: string
  city?: string
  postal_code?: string
  country?: string
  bio?: string
  avatar_url?: string
}

export function UserProfileSettings() {
  const { user } = useAuth()
  const [profile, setProfile] = useState<UserProfile>({
    id: '',
    email: '',
    first_name: '',
    last_name: '',
    phone: '',
    company: '',
    address: '',
    city: '',
    postal_code: '',
    country: 'Sverige',
    bio: '',
    avatar_url: ''
  })
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)

  useEffect(() => {
    if (user) {
      fetchProfile()
    }
  }, [user])

  const fetchProfile = async () => {
    try {
      setLoading(true)
      
      // Get user profile from auth.users metadata
      const { data: authUser, error: authError } = await supabase.auth.getUser()
      
      if (authError) throw authError
      
      if (authUser.user) {
        setProfile({
          id: authUser.user.id,
          email: authUser.user.email || '',
          first_name: authUser.user.user_metadata?.first_name || '',
          last_name: authUser.user.user_metadata?.last_name || '',
          phone: authUser.user.user_metadata?.phone || '',
          company: authUser.user.user_metadata?.company || '',
          address: authUser.user.user_metadata?.address || '',
          city: authUser.user.user_metadata?.city || '',
          postal_code: authUser.user.user_metadata?.postal_code || '',
          country: authUser.user.user_metadata?.country || 'Sverige',
          bio: authUser.user.user_metadata?.bio || '',
          avatar_url: authUser.user.user_metadata?.avatar_url || ''
        })
      }
    } catch (error) {
      console.error('Error fetching profile:', error)
      toast.error('Kunde inte hämta profil')
    } finally {
      setLoading(false)
    }
  }

  const handleSave = async () => {
    try {
      setSaving(true)
      
      const { error } = await supabase.auth.updateUser({
        data: {
          first_name: profile.first_name,
          last_name: profile.last_name,
          phone: profile.phone,
          company: profile.company,
          address: profile.address,
          city: profile.city,
          postal_code: profile.postal_code,
          country: profile.country,
          bio: profile.bio,
          avatar_url: profile.avatar_url
        }
      })

      if (error) throw error

      toast.success('Profil uppdaterad', {
        description: 'Dina profilinställningar har sparats'
      })
    } catch (error) {
      console.error('Error updating profile:', error)
      toast.error('Kunde inte uppdatera profil')
    } finally {
      setSaving(false)
    }
  }

  const handleInputChange = (field: keyof UserProfile, value: string) => {
    setProfile(prev => ({ ...prev, [field]: value }))
  }

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Användarinställningar</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="animate-pulse space-y-4">
            {[...Array(6)].map((_, i) => (
              <div key={i} className="h-10 bg-gray-200 dark:bg-gray-700 rounded"></div>
            ))}
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <User className="h-5 w-5" />
          Användarinställningar
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="first_name">Förnamn</Label>
            <Input
              id="first_name"
              value={profile.first_name}
              onChange={(e) => handleInputChange('first_name', e.target.value)}
              placeholder="Ditt förnamn"
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="last_name">Efternamn</Label>
            <Input
              id="last_name"
              value={profile.last_name}
              onChange={(e) => handleInputChange('last_name', e.target.value)}
              placeholder="Ditt efternamn"
            />
          </div>
        </div>

        <div className="space-y-2">
          <Label htmlFor="email">E-postadress</Label>
          <Input
            id="email"
            type="email"
            value={profile.email}
            disabled
            className="bg-gray-50 dark:bg-gray-800"
          />
          <p className="text-sm text-gray-500 dark:text-gray-400">
            E-postadressen kan inte ändras här. Kontakta support för att ändra e-post.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="phone">Telefonnummer</Label>
            <Input
              id="phone"
              value={profile.phone}
              onChange={(e) => handleInputChange('phone', e.target.value)}
              placeholder="+46 70 123 45 67"
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="company">Företag</Label>
            <Input
              id="company"
              value={profile.company}
              onChange={(e) => handleInputChange('company', e.target.value)}
              placeholder="Ditt företag"
            />
          </div>
        </div>

        <div className="space-y-2">
          <Label htmlFor="address">Adress</Label>
          <Input
            id="address"
            value={profile.address}
            onChange={(e) => handleInputChange('address', e.target.value)}
            placeholder="Gatuadress"
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="space-y-2">
            <Label htmlFor="city">Stad</Label>
            <Input
              id="city"
              value={profile.city}
              onChange={(e) => handleInputChange('city', e.target.value)}
              placeholder="Stockholm"
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="postal_code">Postnummer</Label>
            <Input
              id="postal_code"
              value={profile.postal_code}
              onChange={(e) => handleInputChange('postal_code', e.target.value)}
              placeholder="123 45"
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="country">Land</Label>
            <Input
              id="country"
              value={profile.country}
              onChange={(e) => handleInputChange('country', e.target.value)}
              placeholder="Sverige"
            />
          </div>
        </div>

        <div className="space-y-2">
          <Label htmlFor="bio">Beskrivning</Label>
          <Textarea
            id="bio"
            value={profile.bio}
            onChange={(e) => handleInputChange('bio', e.target.value)}
            placeholder="Berätta lite om dig själv..."
            rows={3}
          />
        </div>

        <div className="flex justify-end">
          <Button onClick={handleSave} disabled={saving}>
            {saving ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2"></div>
                Sparar...
              </>
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                Spara ändringar
              </>
            )}
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
