'use client'

import React, { useState, useEffect } from 'react'
import { Building, Save, Upload, Globe, Phone, Mail, MapPin } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card'
import { supabase } from '@/lib/supabase/supabase'
import { toast } from 'sonner'

interface CompanySettings {
  id?: string
  company_name: string
  org_number: string
  vat_number: string
  address: string
  city: string
  postal_code: string
  country: string
  phone: string
  email: string
  website: string
  bank_account: string
  bank_name: string
  swift_code: string
  payment_terms: string
  invoice_footer: string
  logo_url?: string
}

export function CompanySettings() {
  const [settings, setSettings] = useState<CompanySettings>({
    company_name: '',
    org_number: '',
    vat_number: '',
    address: '',
    city: '',
    postal_code: '',
    country: 'Sverige',
    phone: '',
    email: '',
    website: '',
    bank_account: '',
    bank_name: '',
    swift_code: '',
    payment_terms: '30 dagar netto',
    invoice_footer: '',
    logo_url: ''
  })
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)

  useEffect(() => {
    fetchCompanySettings()
  }, [])

  const fetchCompanySettings = async () => {
    try {
      setLoading(true)
      
      const { data, error } = await supabase
        .from('company_settings')
        .select('*')
        .limit(1)
        .single()

      if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
        throw error
      }

      if (data) {
        setSettings(data)
      }
    } catch (error) {
      console.error('Error fetching company settings:', error)
      // Don't show error toast for missing settings, it's expected for new users
    } finally {
      setLoading(false)
    }
  }

  const handleSave = async () => {
    try {
      setSaving(true)
      
      const { data, error } = await supabase
        .from('company_settings')
        .upsert(settings, { onConflict: 'id' })
        .select()
        .single()

      if (error) throw error

      setSettings(data)
      toast.success('Företagsinställningar sparade', {
        description: 'Dina företagsinställningar har uppdaterats'
      })
    } catch (error) {
      console.error('Error saving company settings:', error)
      toast.error('Kunde inte spara inställningar')
    } finally {
      setSaving(false)
    }
  }

  const handleInputChange = (field: keyof CompanySettings, value: string) => {
    setSettings(prev => ({ ...prev, [field]: value }))
  }

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Företagsinställningar</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="animate-pulse space-y-4">
            {[...Array(8)].map((_, i) => (
              <div key={i} className="h-10 bg-gray-200 dark:bg-gray-700 rounded"></div>
            ))}
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Building className="h-5 w-5" />
          Företagsinställningar
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="company_name">Företagsnamn</Label>
            <Input
              id="company_name"
              value={settings.company_name}
              onChange={(e) => handleInputChange('company_name', e.target.value)}
              placeholder="Ditt företagsnamn"
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="org_number">Organisationsnummer</Label>
            <Input
              id="org_number"
              value={settings.org_number}
              onChange={(e) => handleInputChange('org_number', e.target.value)}
              placeholder="556123-4567"
            />
          </div>
        </div>

        <div className="space-y-2">
          <Label htmlFor="vat_number">Momsregistreringsnummer</Label>
          <Input
            id="vat_number"
            value={settings.vat_number}
            onChange={(e) => handleInputChange('vat_number', e.target.value)}
            placeholder="SE556123456701"
          />
        </div>

        <div className="space-y-4">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">Kontaktinformation</h3>
          
          <div className="space-y-2">
            <Label htmlFor="address">Adress</Label>
            <Input
              id="address"
              value={settings.address}
              onChange={(e) => handleInputChange('address', e.target.value)}
              placeholder="Gatuadress"
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="city">Stad</Label>
              <Input
                id="city"
                value={settings.city}
                onChange={(e) => handleInputChange('city', e.target.value)}
                placeholder="Stockholm"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="postal_code">Postnummer</Label>
              <Input
                id="postal_code"
                value={settings.postal_code}
                onChange={(e) => handleInputChange('postal_code', e.target.value)}
                placeholder="123 45"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="country">Land</Label>
              <Input
                id="country"
                value={settings.country}
                onChange={(e) => handleInputChange('country', e.target.value)}
                placeholder="Sverige"
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="phone">Telefon</Label>
              <Input
                id="phone"
                value={settings.phone}
                onChange={(e) => handleInputChange('phone', e.target.value)}
                placeholder="+46 8 123 456 78"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="email">E-post</Label>
              <Input
                id="email"
                type="email"
                value={settings.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                placeholder="info@företag.se"
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="website">Webbplats</Label>
            <Input
              id="website"
              value={settings.website}
              onChange={(e) => handleInputChange('website', e.target.value)}
              placeholder="https://www.företag.se"
            />
          </div>
        </div>

        <div className="space-y-4">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">Bankinformation</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="bank_name">Banknamn</Label>
              <Input
                id="bank_name"
                value={settings.bank_name}
                onChange={(e) => handleInputChange('bank_name', e.target.value)}
                placeholder="Swedbank"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="bank_account">Kontonummer</Label>
              <Input
                id="bank_account"
                value={settings.bank_account}
                onChange={(e) => handleInputChange('bank_account', e.target.value)}
                placeholder="1234 12 34567"
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="swift_code">SWIFT/BIC</Label>
            <Input
              id="swift_code"
              value={settings.swift_code}
              onChange={(e) => handleInputChange('swift_code', e.target.value)}
              placeholder="SWEDSESS"
            />
          </div>
        </div>

        <div className="space-y-4">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">Fakturainställningar</h3>
          
          <div className="space-y-2">
            <Label htmlFor="payment_terms">Betalningsvillkor</Label>
            <Input
              id="payment_terms"
              value={settings.payment_terms}
              onChange={(e) => handleInputChange('payment_terms', e.target.value)}
              placeholder="30 dagar netto"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="invoice_footer">Fakturasidfot</Label>
            <Textarea
              id="invoice_footer"
              value={settings.invoice_footer}
              onChange={(e) => handleInputChange('invoice_footer', e.target.value)}
              placeholder="Ytterligare information som ska visas längst ner på fakturor..."
              rows={3}
            />
          </div>
        </div>

        <div className="flex justify-end">
          <Button onClick={handleSave} disabled={saving}>
            {saving ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2"></div>
                Sparar...
              </>
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                Spara inställningar
              </>
            )}
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
