"use client";

import React, { useState } from 'react';
import Link from 'next/link';
import { Sidebar, SidebarBody, SidebarLink } from './ui/sidebar';
import { 
  LayoutDashboard, 
  Briefcase,
  Users, 
  FileSpreadsheet, 
  Receipt, 
  Settings, 
  User, 
  BarChart3,
  PieChart,
  LogOut,
  Building,
  FileText,
  UserCheck
} from 'lucide-react';
import { motion } from 'framer-motion';
import Image from 'next/image';
import { useAuth } from '@/context/AuthContext';
import { useRouter } from 'next/navigation';

interface DashboardSidebarProps {
  collapsed?: boolean;
  setCollapsed?: React.Dispatch<React.SetStateAction<boolean>>;
}

export default function DashboardSidebar({ collapsed = false, setCollapsed }: DashboardSidebarProps) {
  const router = useRouter();
  const { signOut } = useAuth();
  
  // Logout function
  const handleLogout = async () => {
    try {
      console.log('[DashboardSidebar] Logout initiated');
      await signOut();
      // Force redirect to login page
      router.push('/login');
    } catch (error) {
      console.error('[DashboardSidebar] Error logging out:', error);
      // Even if logout fails, redirect to login
      router.push('/login');
    }
  };
  
  // Links from the original sidebar
  const links = [
    {
      label: "Dashboard",
      href: "/dashboard",
      icon: (
        <BarChart3 className="text-neutral-700 dark:text-neutral-200 h-5 w-5 flex-shrink-0" />
      ),
    },
    {
      label: "Customers",
      href: "/dashboard/customers",
      icon: (
        <Building className="text-neutral-700 dark:text-neutral-200 h-5 w-5 flex-shrink-0" />
      ),
    },
    {
      label: "Offerter",
      href: "/dashboard/estimates",
      icon: (
        <FileText className="text-neutral-700 dark:text-neutral-200 h-5 w-5 flex-shrink-0" />
      ),
    },
    {
      label: "Projects",
      href: "/dashboard/projects",
      icon: (
        <Briefcase className="text-neutral-700 dark:text-neutral-200 h-5 w-5 flex-shrink-0" />
      ),
    },
    {
      label: "Fakturor",
      href: "/dashboard/fakturor",
      icon: (
        <Receipt className="text-neutral-700 dark:text-neutral-200 h-5 w-5 flex-shrink-0" />
      ),
    },
    {
      label: "Anställda",
      href: "/dashboard/employees",
      icon: (
        <UserCheck className="text-neutral-700 dark:text-neutral-200 h-5 w-5 flex-shrink-0" />
      ),
    },
    {
      label: "Settings",
      href: "/dashboard/settings",
      icon: (
        <Settings className="text-neutral-700 dark:text-neutral-200 h-5 w-5 flex-shrink-0" />
      ),
    },
  ];

  return (
    <Sidebar open={!collapsed} setOpen={setCollapsed ? (open) => setCollapsed(!open) : undefined}>
      <SidebarBody className="justify-between gap-10">
        <div className="flex flex-col flex-1 overflow-y-auto overflow-x-hidden">
          <div className="flex flex-col gap-2 pt-4">
            {links.map((link, idx) => (
              <SidebarLink key={idx} link={link} />
            ))}
          </div>
        </div>
        <div className="flex flex-col gap-3 pb-4">
          {/* Logout button */}
          <button 
            onClick={handleLogout}
            className="flex items-center justify-start gap-2 group/sidebar py-2 w-full hover:bg-neutral-200 dark:hover:bg-neutral-700 rounded-md px-2 transition-colors"
          >
            <LogOut className="text-red-500 dark:text-red-400 h-5 w-5 flex-shrink-0" />
            <motion.span
              animate={{
                display: !collapsed ? "inline-block" : "none",
                opacity: !collapsed ? 1 : 0,
              }}
              className="text-red-500 dark:text-red-400 text-sm group-hover/sidebar:translate-x-1 transition duration-150 whitespace-pre inline-block !p-0 !m-0"
            >
              Logga ut
            </motion.span>
          </button>
          
          <SidebarLink
            link={{
              label: "Youssef Mekidiche",
              href: "#",
              icon: (
                <div className="w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center text-white font-semibold text-sm">
                  YM
                </div>
              ),
            }}
          />
        </div>
      </SidebarBody>
    </Sidebar>
  );
}

// Logo components removed - no longer needed 