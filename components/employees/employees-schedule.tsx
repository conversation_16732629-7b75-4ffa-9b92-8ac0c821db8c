'use client';

import React from 'react';

// Sample project colors
const projectColors = {
  'Projekt A': { bg: 'bg-blue-100 dark:bg-blue-800/20', text: 'text-blue-800 dark:text-blue-400' },
  'Projekt B': { bg: 'bg-green-100 dark:bg-green-800/20', text: 'text-green-800 dark:text-green-400' },
  'Projekt C': { bg: 'bg-purple-100 dark:bg-purple-800/20', text: 'text-purple-800 dark:text-purple-400' },
  'Projekt D': { bg: 'bg-amber-100 dark:bg-amber-800/20', text: 'text-amber-800 dark:text-amber-400' },
  'Intern': { bg: 'bg-gray-100 dark:bg-gray-800/20', text: 'text-gray-800 dark:text-gray-400' },
  'Semester': { bg: 'bg-pink-100 dark:bg-pink-800/20', text: 'text-pink-800 dark:text-pink-400' },
  '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>varo': { bg: 'bg-red-100 dark:bg-red-800/20', text: 'text-red-800 dark:text-red-400' },
};

// Employee schedule data
const employeeSchedules = [
  {
    id: 1,
    name: '<PERSON> <PERSON>',
    avatar: 'https://i.pravatar.cc/150?img=1',
    schedule: [
      { day: 'Monday', project: 'Projekt A', hours: 8 },
      { day: 'Tuesday', project: 'Projekt A', hours: 8 },
      { day: 'Wednesday', project: 'Projekt B', hours: 4 },
      { day: 'Wednesday', project: 'Intern', hours: 4 },
      { day: 'Thursday', project: 'Projekt B', hours: 8 },
      { day: 'Friday', project: 'Projekt B', hours: 8 },
    ],
  },
  {
    id: 2,
    name: 'Erik Johansson',
    avatar: 'https://i.pravatar.cc/150?img=2',
    schedule: [
      { day: 'Monday', project: 'Projekt C', hours: 8 },
      { day: 'Tuesday', project: 'Projekt C', hours: 8 },
      { day: 'Wednesday', project: 'Projekt C', hours: 8 },
      { day: 'Thursday', project: 'Projekt C', hours: 8 },
      { day: 'Friday', project: 'Intern', hours: 8 },
    ],
  },
  {
    id: 3,
    name: 'Maria Larsson',
    avatar: 'https://i.pravatar.cc/150?img=3',
    schedule: [
      { day: 'Monday', project: 'Projekt A', hours: 4 },
      { day: 'Monday', project: 'Projekt B', hours: 4 },
      { day: 'Tuesday', project: 'Projekt A', hours: 4 },
      { day: 'Tuesday', project: 'Projekt D', hours: 4 },
      { day: 'Wednesday', project: 'Projekt D', hours: 8 },
      { day: 'Thursday', project: 'Projekt D', hours: 8 },
      { day: 'Friday', project: 'Projekt B', hours: 8 },
    ],
  },
  {
    id: 4,
    name: 'Johan Karlsson',
    avatar: 'https://i.pravatar.cc/150?img=4',
    schedule: [
      { day: 'Monday', project: 'Sjukfrånvaro', hours: 8 },
      { day: 'Tuesday', project: 'Sjukfrånvaro', hours: 8 },
      { day: 'Wednesday', project: 'Sjukfrånvaro', hours: 8 },
      { day: 'Thursday', project: 'Projekt B', hours: 8 },
      { day: 'Friday', project: 'Projekt B', hours: 8 },
    ],
  },
  {
    id: 5,
    name: 'Sofia Nilsson',
    avatar: 'https://i.pravatar.cc/150?img=5',
    schedule: [
      { day: 'Monday', project: 'Projekt A', hours: 8 },
      { day: 'Tuesday', project: 'Projekt A', hours: 8 },
      { day: 'Wednesday', project: 'Projekt A', hours: 8 },
      { day: 'Thursday', project: 'Projekt A', hours: 8 },
      { day: 'Friday', project: 'Intern', hours: 4 },
      { day: 'Friday', project: 'Projekt C', hours: 4 },
    ],
  },
  {
    id: 6,
    name: 'Lars Eriksson',
    avatar: 'https://i.pravatar.cc/150?img=6',
    schedule: [
      { day: 'Monday', project: 'Semester', hours: 8 },
      { day: 'Tuesday', project: 'Semester', hours: 8 },
      { day: 'Wednesday', project: 'Semester', hours: 8 },
      { day: 'Thursday', project: 'Semester', hours: 8 },
      { day: 'Friday', project: 'Semester', hours: 8 },
    ],
  }
];

// Days of the week
const weekDays = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'];

export function EmployeesSchedule() {
  const [selectedWeek, setSelectedWeek] = React.useState('Vecka 19 (6-10 maj)');
  const [searchTerm, setSearchTerm] = React.useState('');

  // Filter employees based on search term
  const filteredEmployees = employeeSchedules.filter(employee => 
    employee.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Helper function to get projects for a specific day
  const getProjectsForDay = (schedule: typeof employeeSchedules[0]['schedule'], day: string) => {
    return schedule.filter(item => item.day === day);
  };

  // Helper function to get block width based on hours
  const getBlockWidth = (hours: number) => {
    if (hours === 8) return 'w-full';
    if (hours === 4) return 'w-1/2';
    return `w-[${hours/8*100}%]`;
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Personalschema</h3>
        
        <div className="flex items-center gap-3 w-full md:w-auto">
          <select 
            value={selectedWeek}
            onChange={(e) => setSelectedWeek(e.target.value)}
            className="text-sm border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option>Vecka 18 (29 apr-3 maj)</option>
            <option>Vecka 19 (6-10 maj)</option>
            <option>Vecka 20 (13-17 maj)</option>
            <option>Vecka 21 (20-24 maj)</option>
          </select>
          
          <div className="relative flex-grow">
            <input
              type="text"
              placeholder="Sök anställd..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-8 pr-4 py-2 w-full md:w-52 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <svg 
              className="absolute left-2 top-2.5 h-4 w-4 text-gray-400" 
              fill="none" 
              viewBox="0 0 24 24" 
              stroke="currentColor"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>
        </div>
      </div>
      
      {/* Project Legend */}
      <div className="mb-6 flex flex-wrap items-center gap-2 p-3 border border-gray-100 dark:border-gray-700 rounded-md bg-gray-50 dark:bg-gray-900/50">
        <span className="text-xs font-medium text-gray-700 dark:text-gray-300 mr-2">Projekt:</span>
        {Object.entries(projectColors).map(([project, { bg, text }]) => (
          <div key={project} className="flex items-center">
            <div className={`h-3 w-3.5 rounded-sm ${bg} mr-1`}></div>
            <span className={`text-xs ${text}`}>{project}</span>
          </div>
        ))}
      </div>
      
      {/* Schedule Table */}
      <div className="border border-gray-200 dark:border-gray-700 rounded-md overflow-hidden">
        {/* Header */}
        <div className="grid grid-cols-6 bg-gray-50 dark:bg-gray-900/50 border-b border-gray-200 dark:border-gray-700">
          <div className="p-3 text-sm font-medium text-gray-500 dark:text-gray-400">
            Anställd
          </div>
          {weekDays.map(day => (
            <div key={day} className="p-3 text-sm font-medium text-gray-500 dark:text-gray-400">
              {day}
            </div>
          ))}
        </div>
        
        {/* Employee Rows */}
        {filteredEmployees.map(employee => (
          <div 
            key={employee.id}
            className="grid grid-cols-6 border-b border-gray-200 dark:border-gray-700 last:border-b-0"
          >
            {/* Employee cell */}
            <div className="p-3 flex items-center border-r border-gray-200 dark:border-gray-700">
              <img 
                src={employee.avatar} 
                alt={employee.name}
                className="h-8 w-8 rounded-full mr-3"
              />
              <span className="text-sm font-medium text-gray-900 dark:text-white">{employee.name}</span>
            </div>
            
            {/* Schedule cells for each day */}
            {weekDays.map(day => (
              <div key={day} className="p-2 relative min-h-[3.5rem] border-r last:border-r-0 border-gray-200 dark:border-gray-700">
                <div className="flex h-full">
                  {getProjectsForDay(employee.schedule, day).map((item, index) => {
                    const { bg, text } = projectColors[item.project as keyof typeof projectColors] || projectColors['Intern'];
                    return (
                      <div
                        key={index}
                        className={`${getBlockWidth(item.hours)} h-full ${bg} rounded-sm px-1.5 py-1 ${text} relative group overflow-hidden`}
                      >
                        <div className="text-xs font-medium truncate">{item.project}</div>
                        <div className="text-xs opacity-75">{item.hours}h</div>
                        <div className="absolute inset-0 bg-black/0 group-hover:bg-black/5 dark:group-hover:bg-white/5 transition-colors"></div>
                      </div>
                    );
                  })}
                </div>
              </div>
            ))}
          </div>
        ))}
        
        {filteredEmployees.length === 0 && (
          <div className="p-6 text-center text-gray-500 dark:text-gray-400">
            Inga anställda hittades
          </div>
        )}
      </div>
      
      <div className="mt-4 flex justify-between items-center">
        <button className="px-3 py-1.5 text-xs bg-white dark:bg-gray-700 rounded border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors">
          Föregående vecka
        </button>
        <button className="px-3 py-1.5 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors text-xs">
          Schemalägg ny vecka
        </button>
        <button className="px-3 py-1.5 text-xs bg-white dark:bg-gray-700 rounded border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors">
          Nästa vecka
        </button>
      </div>
    </div>
  );
} 