'use client';

import React, { useEffect, useState } from 'react';
import { <PERSON>, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { supabase, Employee, VacationRequest, EmployeeContract } from '@/lib/supabase/supabase';
import { Calendar, FileText, Upload, X, Plus, Eye, Edit, Download } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { FileUpload } from '@/components/ui/file-upload';
import { motion, AnimatePresence } from 'framer-motion';

// Helper function to truncate filename
const truncateFilename = (filename: string, maxLength: number = 30) => {
  if (filename.length <= maxLength) return filename;
  
  const extension = filename.split('.').pop();
  const nameWithoutExt = filename.substring(0, filename.lastIndexOf('.'));
  const truncatedName = nameWithoutExt.substring(0, maxLength - extension!.length - 4) + '...';
  
  return `${truncatedName}.${extension}`;
};

// Contract Preview Modal Component
const ContractPreviewModal = ({ 
  contract, 
  isOpen, 
  onClose 
}: { 
  contract: EmployeeContract; 
  isOpen: boolean; 
  onClose: () => void 
}) => {
  // For demo purposes, we'll show a placeholder since we don't have actual file URLs
  // In a real implementation, you would fetch the actual file from your storage
  
  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4"
          onClick={onClose}
        >
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.9, opacity: 0 }}
            transition={{ type: "spring", damping: 20, stiffness: 300 }}
            className="bg-white dark:bg-gray-900 rounded-2xl shadow-2xl max-w-4xl max-h-[90vh] w-full overflow-hidden"
            onClick={(e) => e.stopPropagation()}
          >
            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                  {contract.title}
                </h3>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  {contract.contract_type === 'employment' ? 'Anställningsavtal' : 
                   contract.contract_type === 'option' ? 'Optionsavtal' : 
                   contract.contract_type === 'nda' ? 'Sekretessavtal' : 'Övrigt'}
                  {contract.signed_date && ` • Signerat ${new Date(contract.signed_date).toLocaleDateString('sv-SE')}`}
                </p>
              </div>
              <button
                onClick={onClose}
                className="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
              >
                <X className="w-5 h-5 text-gray-500 dark:text-gray-400" />
              </button>
            </div>

            {/* Content */}
            <div className="p-6 max-h-[70vh] overflow-auto">
              <div className="text-center py-12">
                <div className="w-16 h-16 mx-auto mb-4 bg-blue-100 dark:bg-blue-900/20 rounded-full flex items-center justify-center">
                  <FileText className="w-8 h-8 text-blue-600 dark:text-blue-400" />
                </div>
                <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                  Kontraktförhandsvisning
                </h4>
                <p className="text-gray-500 dark:text-gray-400 mb-4">
                  Detta är en demo. I en riktig implementation skulle kontraktet visas här.
                </p>
                <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-6 text-left max-w-2xl mx-auto">
                  <h5 className="font-semibold text-gray-900 dark:text-white mb-3">Kontraktsinformation:</h5>
                  <div className="space-y-2 text-sm">
                    <div><span className="font-medium">Titel:</span> {contract.title}</div>
                    <div><span className="font-medium">Typ:</span> {
                      contract.contract_type === 'employment' ? 'Anställningsavtal' : 
                      contract.contract_type === 'option' ? 'Optionsavtal' : 
                      contract.contract_type === 'nda' ? 'Sekretessavtal' : 'Övrigt'
                    }</div>
                    <div><span className="font-medium">Status:</span> {
                      contract.status === 'active' ? 'Aktiv' : 
                      contract.status === 'expired' ? 'Utgången' : 
                      contract.status === 'terminated' ? 'Avslutad' : contract.status
                    }</div>
                    {contract.signed_date && (
                      <div><span className="font-medium">Signerat:</span> {new Date(contract.signed_date).toLocaleDateString('sv-SE')}</div>
                    )}
                    <div><span className="font-medium">Fil-URL:</span> {contract.file_url}</div>
                  </div>
                </div>
              </div>
            </div>

            {/* Footer */}
            <div className="flex justify-end gap-3 p-6 border-t border-gray-200 dark:border-gray-700">
              <button
                onClick={() => {
                  // In a real implementation, this would download the actual file
                  console.log('Download contract:', contract.file_url);
                }}
                className="px-4 py-2 text-sm font-medium text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 transition-colors flex items-center gap-2"
              >
                <Download className="w-4 h-4" />
                Ladda ner
              </button>
              <button
                onClick={onClose}
                className="px-4 py-2 text-sm font-medium bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                Stäng
              </button>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

interface EmployeeWithRequests extends Employee {
  vacation_requests?: VacationRequest[];
  contracts?: EmployeeContract[];
}

export function EmployeesList() {
  const [employees, setEmployees] = useState<EmployeeWithRequests[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState<{ field: keyof Employee, direction: 'asc' | 'desc' }>({ field: 'name', direction: 'asc' });
  const [expandedEmployee, setExpandedEmployee] = useState<string | null>(null);
  
  // Modal states
  const [showVacationModal, setShowVacationModal] = useState(false);
  const [showProfileModal, setShowProfileModal] = useState(false);
  const [showFileUploadModal, setShowFileUploadModal] = useState(false);
  const [showContractPreviewModal, setShowContractPreviewModal] = useState(false);
  const [selectedEmployee, setSelectedEmployee] = useState<EmployeeWithRequests | null>(null);
  const [selectedContract, setSelectedContract] = useState<EmployeeContract | null>(null);
  
  // Vacation request form
  const [vacationForm, setVacationForm] = useState({
    type: 'vacation',
    start_date: '',
    end_date: '',
    notes: ''
  });

  // File upload form
  const [fileUploadForm, setFileUploadForm] = useState({
    title: '',
    contract_type: 'employment',
    files: [] as File[]
  });

  useEffect(() => {
    fetchEmployees();
  }, []);

  const fetchEmployees = async () => {
    try {
      setLoading(true);
      
      // Fetch employees with their vacation requests and contracts
      const { data: employeesData, error: employeesError } = await supabase
        .from('employees')
        .select('*')
        .order('name');

      if (employeesError) throw employeesError;

      // Fetch vacation requests for all employees
      const { data: vacationRequests, error: vacationError } = await supabase
        .from('vacation_requests')
        .select('*');

      if (vacationError) throw vacationError;

      // Fetch contracts for all employees
      const { data: contracts, error: contractsError } = await supabase
        .from('employee_contracts')
        .select('*');

      if (contractsError) throw contractsError;

      // Combine the data
      const employeesWithData = employeesData?.map(employee => ({
        ...employee,
        vacation_requests: vacationRequests?.filter(req => req.employee_id === employee.id) || [],
        contracts: contracts?.filter(contract => contract.employee_id === employee.id) || []
      })) || [];

      setEmployees(employeesWithData);
    } catch (error: any) {
      console.error('Error fetching employees:', error.message);
    } finally {
      setLoading(false);
    }
  };

  // Filter employees based on search term
  const filteredEmployees = employees.filter(employee => 
    employee.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    employee.position.toLowerCase().includes(searchTerm.toLowerCase()) ||
    employee.department.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Sort employees based on sortBy state
  const sortedEmployees = [...filteredEmployees].sort((a, b) => {
    const aValue = a[sortBy.field];
    const bValue = b[sortBy.field];

    if (typeof aValue === 'string' && typeof bValue === 'string') {
      return sortBy.direction === 'asc' 
        ? aValue.localeCompare(bValue) 
        : bValue.localeCompare(aValue);
    }

    if (typeof aValue === 'number' && typeof bValue === 'number') {
      return sortBy.direction === 'asc' 
        ? aValue - bValue 
        : bValue - aValue;
    }

    return 0;
  });

  const handleSort = (field: keyof Employee) => {
    if (sortBy.field === field) {
      setSortBy({ field, direction: sortBy.direction === 'asc' ? 'desc' : 'asc' });
    } else {
      setSortBy({ field, direction: 'asc' });
    }
  };

  const handleRowClick = (employeeId: string) => {
    setExpandedEmployee(expandedEmployee === employeeId ? null : employeeId);
  };

  const getStatusColor = (status: string) => {
    switch(status) {
      case 'active':
        return 'bg-green-100 text-green-800 dark:bg-green-800/20 dark:text-green-500';
      case 'sick_leave':
        return 'bg-red-100 text-red-800 dark:bg-red-800/20 dark:text-red-500';
      case 'vacation':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-800/20 dark:text-blue-500';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-800/20 dark:text-gray-500';
    }
  };

  const getStatusLabel = (status: string) => {
    switch(status) {
      case 'active': return 'Aktiv';
      case 'sick_leave': return 'Sjukfrånvaro';
      case 'vacation': return 'Semester';
      case 'inactive': return 'Inaktiv';
      default: return status;
    }
  };

  const getRequestStatusColor = (status: string) => {
    switch(status) {
      case 'approved':
        return 'bg-green-100 text-green-800 dark:bg-green-800/20 dark:text-green-500';
      case 'pending':
        return 'bg-amber-100 text-amber-800 dark:bg-amber-800/20 dark:text-amber-500';
      case 'rejected':
        return 'bg-red-100 text-red-800 dark:bg-red-800/20 dark:text-red-500';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-800/20 dark:text-gray-500';
    }
  };

  const getRequestStatusLabel = (status: string) => {
    switch(status) {
      case 'approved': return 'Godkänd';
      case 'pending': return 'Väntande';
      case 'rejected': return 'Nekad';
      default: return status;
    }
  };

  const handleVacationRequest = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!selectedEmployee) return;

    try {
      const startDate = new Date(vacationForm.start_date);
      const endDate = new Date(vacationForm.end_date);
      const daysDiff = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 3600 * 24)) + 1;

      const { error } = await supabase
        .from('vacation_requests')
        .insert({
          employee_id: selectedEmployee.id,
          type: vacationForm.type,
          start_date: vacationForm.start_date,
          end_date: vacationForm.end_date,
          days_count: daysDiff,
          notes: vacationForm.notes
        });

      if (error) throw error;

      // Reset form and close modal
      setVacationForm({ type: 'vacation', start_date: '', end_date: '', notes: '' });
      setShowVacationModal(false);
      setSelectedEmployee(null);
      
      // Refresh data
      fetchEmployees();
    } catch (error: any) {
      console.error('Error creating vacation request:', error.message);
    }
  };

  const openVacationModal = (employee: EmployeeWithRequests) => {
    setSelectedEmployee(employee);
    setShowVacationModal(true);
  };

  const openProfileModal = (employee: EmployeeWithRequests) => {
    setSelectedEmployee(employee);
    setShowProfileModal(true);
  };

  const openFileUploadModal = (employee: EmployeeWithRequests) => {
    setSelectedEmployee(employee);
    setShowFileUploadModal(true);
  };

  const openContractPreviewModal = (contract: EmployeeContract) => {
    setSelectedContract(contract);
    setShowContractPreviewModal(true);
  };

  const handleFileUpload = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!selectedEmployee || fileUploadForm.files.length === 0) return;

    try {
      // In a real implementation, you would upload the file to storage first
      // For now, we'll just create a contract record with a placeholder URL
      const file = fileUploadForm.files[0];
      const fileUrl = `/contracts/${selectedEmployee.id}/${file.name}`;

      const { error } = await supabase
        .from('employee_contracts')
        .insert({
          employee_id: selectedEmployee.id,
          contract_type: fileUploadForm.contract_type,
          title: fileUploadForm.title || file.name,
          file_url: fileUrl,
          signed_date: new Date().toISOString().split('T')[0],
          status: 'active'
        });

      if (error) throw error;

      // Reset form and close modal
      setFileUploadForm({ title: '', contract_type: 'employment', files: [] });
      setShowFileUploadModal(false);
      setSelectedEmployee(null);
      
      // Refresh data
      fetchEmployees();
    } catch (error: any) {
      console.error('Error uploading contract:', error.message);
    }
  };

  const handleFileChange = (files: File[]) => {
    setFileUploadForm(prev => ({ ...prev, files }));
  };

  if (loading) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-1/4 mb-6"></div>
          <div className="space-y-4">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="h-16 bg-gray-200 dark:bg-gray-700 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <>
      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Alla Anställda</h3>
          
          <div className="flex items-center gap-2 w-full md:w-auto">
            <div className="relative flex-grow">
              <input
                type="text"
                placeholder="Sök efter anställda..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 pr-4 py-2 w-full md:w-64 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              <svg 
                className="absolute left-3 top-2.5 h-5 w-5 text-gray-400" 
                fill="none" 
                viewBox="0 0 24 24" 
                stroke="currentColor"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </div>
            
            <button className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
              + Lägg till
            </button>
          </div>
        </div>
        
        <div className="overflow-x-auto">
          <table className="w-full border-collapse">
            <thead>
              <tr className="border-b border-gray-200 dark:border-gray-700">
                <th 
                  className="px-4 py-3 text-left text-sm font-medium text-gray-500 dark:text-gray-400 cursor-pointer"
                  onClick={() => handleSort('name')}
                >
                  <div className="flex items-center">
                    <span>Namn</span>
                    {sortBy.field === 'name' && (
                      <svg 
                        className="ml-1 h-4 w-4" 
                        fill="none" 
                        viewBox="0 0 24 24" 
                        stroke="currentColor"
                      >
                        {sortBy.direction === 'asc' ? (
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 15l7-7 7 7" />
                        ) : (
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                        )}
                      </svg>
                    )}
                  </div>
                </th>
                <th 
                  className="px-4 py-3 text-left text-sm font-medium text-gray-500 dark:text-gray-400 cursor-pointer"
                  onClick={() => handleSort('position')}
                >
                  <div className="flex items-center">
                    <span>Befattning</span>
                    {sortBy.field === 'position' && (
                      <svg 
                        className="ml-1 h-4 w-4" 
                        fill="none" 
                        viewBox="0 0 24 24" 
                        stroke="currentColor"
                      >
                        {sortBy.direction === 'asc' ? (
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 15l7-7 7 7" />
                        ) : (
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                        )}
                      </svg>
                    )}
                  </div>
                </th>
                <th 
                  className="px-4 py-3 text-left text-sm font-medium text-gray-500 dark:text-gray-400 cursor-pointer"
                  onClick={() => handleSort('department')}
                >
                  <div className="flex items-center">
                    <span>Avdelning</span>
                    {sortBy.field === 'department' && (
                      <svg 
                        className="ml-1 h-4 w-4" 
                        fill="none" 
                        viewBox="0 0 24 24" 
                        stroke="currentColor"
                      >
                        {sortBy.direction === 'asc' ? (
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 15l7-7 7 7" />
                        ) : (
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                        )}
                      </svg>
                    )}
                  </div>
                </th>
                <th 
                  className="px-4 py-3 text-left text-sm font-medium text-gray-500 dark:text-gray-400 cursor-pointer"
                  onClick={() => handleSort('status')}
                >
                  <div className="flex items-center">
                    <span>Status</span>
                    {sortBy.field === 'status' && (
                      <svg 
                        className="ml-1 h-4 w-4" 
                        fill="none" 
                        viewBox="0 0 24 24" 
                        stroke="currentColor"
                      >
                        {sortBy.direction === 'asc' ? (
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 15l7-7 7 7" />
                        ) : (
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                        )}
                      </svg>
                    )}
                  </div>
                </th>
                <th 
                  className="px-4 py-3 text-left text-sm font-medium text-gray-500 dark:text-gray-400 cursor-pointer"
                  onClick={() => handleSort('hourly_rate')}
                >
                  <div className="flex items-center">
                    <span>Timtaxa</span>
                    {sortBy.field === 'hourly_rate' && (
                      <svg 
                        className="ml-1 h-4 w-4" 
                        fill="none" 
                        viewBox="0 0 24 24" 
                        stroke="currentColor"
                      >
                        {sortBy.direction === 'asc' ? (
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 15l7-7 7 7" />
                        ) : (
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                        )}
                      </svg>
                    )}
                  </div>
                </th>
                <th 
                  className="px-4 py-3 text-left text-sm font-medium text-gray-500 dark:text-gray-400 cursor-pointer"
                  onClick={() => handleSort('utilization_percentage')}
                >
                  <div className="flex items-center">
                    <span>Beläggning</span>
                    {sortBy.field === 'utilization_percentage' && (
                      <svg 
                        className="ml-1 h-4 w-4" 
                        fill="none" 
                        viewBox="0 0 24 24" 
                        stroke="currentColor"
                      >
                        {sortBy.direction === 'asc' ? (
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 15l7-7 7 7" />
                        ) : (
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                        )}
                      </svg>
                    )}
                  </div>
                </th>
                <th className="px-4 py-3 text-right text-sm font-medium text-gray-500 dark:text-gray-400">
                  Åtgärder
                </th>
              </tr>
            </thead>
            <tbody>
              {sortedEmployees.map((employee) => (
                <React.Fragment key={employee.id}>
                  <tr 
                    className={`border-b ${expandedEmployee === employee.id ? 'bg-gray-50 dark:bg-gray-800/50' : 'hover:bg-gray-50 dark:hover:bg-gray-800/50'} border-gray-200 dark:border-gray-700 transition-colors cursor-pointer`}
                    onClick={() => handleRowClick(employee.id)}
                  >
                    <td className="px-4 py-4">
                      <div className="flex items-center">
                        <div className="h-8 w-8 rounded-full bg-blue-500 flex items-center justify-center text-white font-semibold text-sm mr-3">
                          {employee.name.split(' ').map(n => n[0]).join('')}
                        </div>
                        <div>
                          <div className="font-medium text-gray-900 dark:text-white">{employee.name}</div>
                          <div className="text-xs text-gray-500 dark:text-gray-400">{employee.email}</div>
                        </div>
                      </div>
                    </td>
                    <td className="px-4 py-4 text-sm text-gray-700 dark:text-gray-300">
                      {employee.position}
                    </td>
                    <td className="px-4 py-4 text-sm text-gray-700 dark:text-gray-300">
                      {employee.department}
                    </td>
                    <td className="px-4 py-4">
                      <span className={`text-xs px-2 py-1 rounded-full ${getStatusColor(employee.status)}`}>
                        {getStatusLabel(employee.status)}
                      </span>
                    </td>
                    <td className="px-4 py-4 text-sm text-gray-700 dark:text-gray-300">
                      {employee.hourly_rate} kr/h
                    </td>
                    <td className="px-4 py-4 text-sm text-gray-700 dark:text-gray-300">
                      {employee.utilization_percentage}%
                    </td>
                    <td className="px-4 py-4 text-right">
                      <div className="flex justify-end items-center">
                        <button 
                          className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 mr-3"
                          onClick={(e) => {
                            e.stopPropagation();
                            openProfileModal(employee);
                          }}
                        >
                          Visa
                        </button>
                        <button 
                          className="text-gray-600 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-300"
                          onClick={(e) => {
                            e.stopPropagation();
                            // Handle edit action
                          }}
                        >
                          Redigera
                        </button>
                      </div>
                    </td>
                  </tr>
                  
                  {/* Expanded view */}
                  {expandedEmployee === employee.id && (
                    <tr>
                      <td colSpan={7} className="px-0 py-0">
                        <div className="bg-gray-50 dark:bg-gray-800/30 p-6 border-b border-gray-200 dark:border-gray-700">
                          <div className="flex flex-col">
                            {/* Personal Info */}
                            <div className="flex items-center mb-6">
                              <div className="h-24 w-24 rounded-full bg-blue-500 flex items-center justify-center text-white font-bold text-2xl mr-6">
                                {employee.name.split(' ').map(n => n[0]).join('')}
                              </div>
                              <div>
                                <h3 className="text-xl font-semibold text-gray-900 dark:text-white">{employee.name}</h3>
                                <p className="text-md text-gray-600 dark:text-gray-300">
                                  {employee.position} • {employee.location} • Anställd sedan {new Date(employee.hire_date).toLocaleDateString('sv-SE')}
                                </p>
                                <div className="flex mt-2 space-x-4">
                                  <div className="flex items-center text-gray-600 dark:text-gray-400">
                                    <svg 
                                      className="h-4 w-4 mr-1"
                                      fill="none" 
                                      viewBox="0 0 24 24" 
                                      stroke="currentColor"
                                    >
                                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                                    </svg>
                                    <span className="text-sm">{employee.email}</span>
                                  </div>
                                  {employee.phone && (
                                    <div className="flex items-center text-gray-600 dark:text-gray-400">
                                      <svg 
                                        className="h-4 w-4 mr-1"
                                        fill="none" 
                                        viewBox="0 0 24 24" 
                                        stroke="currentColor"
                                      >
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                                      </svg>
                                      <span className="text-sm">{employee.phone}</span>
                                    </div>
                                  )}
                                </div>
                              </div>
                            </div>

                            {/* Vacation Overview */}
                            <div className="border-t border-gray-200 dark:border-gray-700 pt-6">
                              <div className="mb-6">
                                <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Ledighetsöversikt</h4>
                                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                                  <div className="flex flex-col space-y-2 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                                    <div className="text-blue-500 text-2xl font-bold">{employee.vacation_days_available}</div>
                                    <div className="text-sm text-gray-600 dark:text-gray-400">Tillgängliga dagar</div>
                                    <div className="text-xs text-gray-500 dark:text-gray-500">Att boka ledighet</div>
                                  </div>

                                  <div className="flex flex-col space-y-2 p-4 bg-amber-50 dark:bg-amber-900/20 rounded-lg">
                                    <div className="text-amber-500 text-2xl font-bold">{employee.vacation_days_pending}</div>
                                    <div className="text-sm text-gray-600 dark:text-gray-400">Väntande förfrågningar</div>
                                    <div className="text-xs text-gray-500 dark:text-gray-500">Väntar på godkännande</div>
                                  </div>

                                  <div className="flex flex-col space-y-2 p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                                    <div className="text-purple-500 text-2xl font-bold">{employee.vacation_days_entitled}</div>
                                    <div className="text-sm text-gray-600 dark:text-gray-400">Dagar per år</div>
                                    <div className="text-xs text-gray-500 dark:text-gray-500">Enligt anställningsavtal</div>
                                  </div>

                                  <div className="flex flex-col space-y-2 p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                                    <div className="text-green-500 text-2xl font-bold">{employee.vacation_days_used}</div>
                                    <div className="text-sm text-gray-600 dark:text-gray-400">Använda dagar</div>
                                    <div className="text-xs text-gray-500 dark:text-gray-500">Detta år</div>
                                  </div>
                                </div>
                              </div>

                              {/* Vacation Requests */}
                              <div className="mb-4">
                                <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Ledighetsförfrågningar</h4>
                                {employee.vacation_requests && employee.vacation_requests.length > 0 ? (
                                  <div className="overflow-x-auto">
                                    <table className="w-full text-sm">
                                      <thead>
                                        <tr className="bg-gray-100 dark:bg-gray-700/50">
                                          <th className="px-3 py-2 text-left">Typ</th>
                                          <th className="px-3 py-2 text-left">Längd</th>
                                          <th className="px-3 py-2 text-left">Från</th>
                                          <th className="px-3 py-2 text-left">Till</th>
                                          <th className="px-3 py-2 text-left">Anteckningar</th>
                                          <th className="px-3 py-2 text-left">Status</th>
                                        </tr>
                                      </thead>
                                      <tbody>
                                        {employee.vacation_requests.map((request) => (
                                          <tr key={request.id} className="border-b border-gray-200 dark:border-gray-700">
                                            <td className="px-3 py-2">{request.type === 'vacation' ? 'Semester' : request.type}</td>
                                            <td className="px-3 py-2">{request.days_count} dagar</td>
                                            <td className="px-3 py-2">{new Date(request.start_date).toLocaleDateString('sv-SE')}</td>
                                            <td className="px-3 py-2">{new Date(request.end_date).toLocaleDateString('sv-SE')}</td>
                                            <td className="px-3 py-2">{request.notes || '-'}</td>
                                            <td className="px-3 py-2">
                                              <span className={`text-xs px-2 py-1 rounded-full ${getRequestStatusColor(request.status)}`}>
                                                {getRequestStatusLabel(request.status)}
                                              </span>
                                            </td>
                                          </tr>
                                        ))}
                                      </tbody>
                                    </table>
                                  </div>
                                ) : (
                                  <p className="text-gray-500 dark:text-gray-400 italic">Inga ledighetsförfrågningar</p>
                                )}
                              </div>

                              <div className="flex justify-end mt-6">
                                <button 
                                  className="px-4 py-2 text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    openProfileModal(employee);
                                  }}
                                >
                                  Se hela profilen
                                </button>
                                <button 
                                  className="ml-3 px-4 py-2 text-sm bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    openVacationModal(employee);
                                  }}
                                >
                                  Begär ledighet
                                </button>
                              </div>
                            </div>
                          </div>
                        </div>
                      </td>
                    </tr>
                  )}
                </React.Fragment>
              ))}
              {filteredEmployees.length === 0 && (
                <tr>
                  <td colSpan={7} className="px-4 py-6 text-center text-gray-500 dark:text-gray-400">
                    Inga anställda hittades
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* Vacation Request Modal */}
      {showVacationModal && selectedEmployee && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-md mx-4">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                Begär ledighet - {selectedEmployee.name}
              </h3>
              <button
                onClick={() => setShowVacationModal(false)}
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              >
                <X className="w-5 h-5" />
              </button>
            </div>
            
            <form onSubmit={handleVacationRequest} className="space-y-4">
              <div>
                <Label htmlFor="type">Typ av ledighet</Label>
                <Select value={vacationForm.type} onValueChange={(value) => setVacationForm({...vacationForm, type: value})}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="vacation">Semester</SelectItem>
                    <SelectItem value="sick_leave">Sjukledighet</SelectItem>
                    <SelectItem value="personal">Personlig ledighet</SelectItem>
                    <SelectItem value="parental">Föräldraledighet</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div>
                <Label htmlFor="start_date">Startdatum</Label>
                <Input
                  type="date"
                  id="start_date"
                  value={vacationForm.start_date}
                  onChange={(e) => setVacationForm({...vacationForm, start_date: e.target.value})}
                  required
                />
              </div>
              
              <div>
                <Label htmlFor="end_date">Slutdatum</Label>
                <Input
                  type="date"
                  id="end_date"
                  value={vacationForm.end_date}
                  onChange={(e) => setVacationForm({...vacationForm, end_date: e.target.value})}
                  required
                />
              </div>
              
              <div>
                <Label htmlFor="notes">Anteckningar (valfritt)</Label>
                <Textarea
                  id="notes"
                  value={vacationForm.notes}
                  onChange={(e) => setVacationForm({...vacationForm, notes: e.target.value})}
                  placeholder="Lägg till eventuella anteckningar..."
                  rows={3}
                />
              </div>
              
              <div className="flex justify-end space-x-2 pt-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setShowVacationModal(false)}
                >
                  Avbryt
                </Button>
                <Button type="submit">
                  Skicka förfrågan
                </Button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Profile Modal */}
      {showProfileModal && selectedEmployee && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-4xl mx-4 max-h-[90vh] overflow-y-auto">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
                Profil - {selectedEmployee.name}
              </h3>
              <button
                onClick={() => setShowProfileModal(false)}
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              >
                <X className="w-6 h-6" />
              </button>
            </div>
            
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Personal Information */}
              <div className="space-y-4">
                <h4 className="text-lg font-medium text-gray-900 dark:text-white">Personlig information</h4>
                <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4 space-y-3">
                  <div>
                    <span className="text-sm text-gray-500 dark:text-gray-400">Namn:</span>
                    <p className="font-medium text-gray-900 dark:text-white">{selectedEmployee.name}</p>
                  </div>
                  <div>
                    <span className="text-sm text-gray-500 dark:text-gray-400">E-post:</span>
                    <p className="font-medium text-gray-900 dark:text-white">{selectedEmployee.email}</p>
                  </div>
                  {selectedEmployee.phone && (
                    <div>
                      <span className="text-sm text-gray-500 dark:text-gray-400">Telefon:</span>
                      <p className="font-medium text-gray-900 dark:text-white">{selectedEmployee.phone}</p>
                    </div>
                  )}
                  <div>
                    <span className="text-sm text-gray-500 dark:text-gray-400">Befattning:</span>
                    <p className="font-medium text-gray-900 dark:text-white">{selectedEmployee.position}</p>
                  </div>
                  <div>
                    <span className="text-sm text-gray-500 dark:text-gray-400">Avdelning:</span>
                    <p className="font-medium text-gray-900 dark:text-white">{selectedEmployee.department}</p>
                  </div>
                  <div>
                    <span className="text-sm text-gray-500 dark:text-gray-400">Plats:</span>
                    <p className="font-medium text-gray-900 dark:text-white">{selectedEmployee.location}</p>
                  </div>
                  <div>
                    <span className="text-sm text-gray-500 dark:text-gray-400">Anställningsdatum:</span>
                    <p className="font-medium text-gray-900 dark:text-white">
                      {new Date(selectedEmployee.hire_date).toLocaleDateString('sv-SE')}
                    </p>
                  </div>
                </div>
              </div>

              {/* Contracts */}
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <h4 className="text-lg font-medium text-gray-900 dark:text-white">Kontrakt & Dokument</h4>
                  <Button 
                    size="sm" 
                    className="flex items-center gap-2"
                    onClick={() => openFileUploadModal(selectedEmployee)}
                  >
                    <Upload className="w-4 h-4" />
                    Ladda upp
                  </Button>
                </div>
                <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4">
                  {selectedEmployee.contracts && selectedEmployee.contracts.length > 0 ? (
                    <div className="space-y-3">
                      {selectedEmployee.contracts.map((contract) => (
                        <div key={contract.id} className="flex items-center justify-between p-3 bg-white dark:bg-gray-800 rounded border">
                          <div className="flex items-center gap-3">
                            <FileText className="w-5 h-5 text-blue-600" />
                            <div>
                              <p className="font-medium text-gray-900 dark:text-white">{truncateFilename(contract.title)}</p>
                              <p className="text-sm text-gray-500 dark:text-gray-400">
                                {contract.contract_type === 'employment' ? 'Anställningsavtal' : 
                                 contract.contract_type === 'option' ? 'Optionsavtal' : 
                                 contract.contract_type === 'nda' ? 'Sekretessavtal' : 'Övrigt'}
                                {contract.signed_date && ` • Signerat ${new Date(contract.signed_date).toLocaleDateString('sv-SE')}`}
                              </p>
                            </div>
                          </div>
                          <div className="flex items-center gap-2">
                            <span className={`text-xs px-2 py-1 rounded-full ${
                              contract.status === 'active' ? 'bg-green-100 text-green-800 dark:bg-green-800/20 dark:text-green-500' :
                              contract.status === 'expired' ? 'bg-red-100 text-red-800 dark:bg-red-800/20 dark:text-red-500' :
                              'bg-gray-100 text-gray-800 dark:bg-gray-800/20 dark:text-gray-500'
                            }`}>
                              {contract.status === 'active' ? 'Aktiv' : 
                               contract.status === 'expired' ? 'Utgången' : 
                               contract.status === 'terminated' ? 'Avslutad' : contract.status}
                            </span>
                            <Button 
                              size="sm" 
                              variant="outline"
                              onClick={() => openContractPreviewModal(contract)}
                            >
                              <Eye className="w-4 h-4" />
                            </Button>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-gray-500 dark:text-gray-400 italic text-center py-4">
                      Inga kontrakt uppladdade
                    </p>
                  )}
                </div>
              </div>
            </div>
            
            <div className="flex justify-end mt-6">
              <Button onClick={() => setShowProfileModal(false)}>
                Stäng
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* File Upload Modal */}
      {showFileUploadModal && selectedEmployee && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-2xl mx-4 max-h-[90vh] overflow-y-auto">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
                Ladda upp dokument - {selectedEmployee.name}
              </h3>
              <button
                onClick={() => setShowFileUploadModal(false)}
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              >
                <X className="w-6 h-6" />
              </button>
            </div>
            
            <form onSubmit={handleFileUpload} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="contract_type">Typ av dokument</Label>
                  <Select 
                    value={fileUploadForm.contract_type} 
                    onValueChange={(value) => setFileUploadForm({...fileUploadForm, contract_type: value})}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="employment">Anställningsavtal</SelectItem>
                      <SelectItem value="option">Optionsavtal</SelectItem>
                      <SelectItem value="nda">Sekretessavtal</SelectItem>
                      <SelectItem value="other">Övrigt</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <div>
                  <Label htmlFor="title">Titel (valfritt)</Label>
                  <Input
                    id="title"
                    value={fileUploadForm.title}
                    onChange={(e) => setFileUploadForm({...fileUploadForm, title: e.target.value})}
                    placeholder="T.ex. Anställningsavtal 2024"
                  />
                </div>
              </div>
              
              <div>
                <Label>Välj fil att ladda upp</Label>
                <div className="mt-2">
                  <FileUpload onChange={handleFileChange} />
                </div>
              </div>
              
              <div className="flex justify-end space-x-2 pt-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setShowFileUploadModal(false)}
                >
                  Avbryt
                </Button>
                <Button 
                  type="submit"
                  disabled={fileUploadForm.files.length === 0}
                >
                  Ladda upp dokument
                </Button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Contract Preview Modal */}
      {showContractPreviewModal && selectedContract && (
        <ContractPreviewModal
          contract={selectedContract}
          isOpen={showContractPreviewModal}
          onClose={() => setShowContractPreviewModal(false)}
        />
      )}
    </>
  );
} 