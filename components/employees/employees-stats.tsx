'use client';

import React, { useEffect, useState } from 'react';
import { StatsCards } from '@/components/ui/stats-cards';
import { supabase } from '@/lib/supabase/supabase';
import { Users, DollarSign, Calendar, TrendingUp, AlertTriangle } from 'lucide-react';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';

interface EmployeeStats {
  totalEmployees: number;
  activeEmployees: number;
  averageHourlyRate: number;
  totalUtilization: number;
  pendingVacationRequests: number;
}

export function EmployeesStats() {
  const [employeeStats, setEmployeeStats] = useState<EmployeeStats>({
    totalEmployees: 0,
    activeEmployees: 0,
    averageHourlyRate: 0,
    totalUtilization: 0,
    pendingVacationRequests: 0
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    async function fetchEmployeeStats() {
      try {
        setLoading(true);

        // Fetch employees data
        const { data: employees, error: employeesError } = await supabase
          .from('employees')
          .select('*');

        if (employeesError) {
          throw employeesError;
        }

        // Fetch vacation requests data
        const { data: vacationRequests, error: vacationError } = await supabase
          .from('vacation_requests')
          .select('*')
          .eq('status', 'pending');

        if (vacationError) {
          throw vacationError;
        }

        const employeesData = employees || [];
        const totalEmployees = employeesData.length;
        const activeEmployees = employeesData.filter(emp => emp.status === 'active').length;
        const averageHourlyRate = totalEmployees > 0 
          ? Math.round(employeesData.reduce((sum, emp) => sum + (emp.hourly_rate || 0), 0) / totalEmployees)
          : 0;
        const totalUtilization = totalEmployees > 0 
          ? Math.round(employeesData.reduce((sum, emp) => sum + (emp.utilization_percentage || 0), 0) / totalEmployees)
          : 0;
        const pendingVacationRequests = vacationRequests?.length || 0;

        setEmployeeStats({
          totalEmployees,
          activeEmployees,
          averageHourlyRate,
          totalUtilization,
          pendingVacationRequests
        });
      } catch (error: any) {
        console.error('Error fetching employee stats:', error.message);
      } finally {
        setLoading(false);
      }
    }

    fetchEmployeeStats();
  }, []);

  const employeeMetrics = [
    {
      label: "Totalt anställda",
      value: employeeStats.totalEmployees,
      icon: <Users className="w-5 h-5 text-blue-600 dark:text-blue-400" />,
      change: "+0%",
      changeType: "positive" as const,
      changeLabel: "senaste månaden"
    },
    {
      label: "Aktiva anställda",
      value: employeeStats.activeEmployees,
      icon: <Users className="w-5 h-5 text-green-600 dark:text-green-400" />,
      change: "+0%",
      changeType: "positive" as const,
      changeLabel: "senaste månaden"
    },
    {
      label: "Genomsnittlig timtaxa",
      value: `${employeeStats.averageHourlyRate} kr/h`,
      icon: <DollarSign className="w-5 h-5 text-emerald-600 dark:text-emerald-400" />,
      change: "+3%",
      changeType: "positive" as const,
      changeLabel: "senaste månaden"
    },
    {
      label: "Genomsnittlig beläggning",
      value: `${employeeStats.totalUtilization}%`,
      icon: <TrendingUp className="w-5 h-5 text-indigo-600 dark:text-indigo-400" />,
      change: "+5%",
      changeType: "positive" as const,
      changeLabel: "senaste månaden"
    },
    {
      label: "Väntande ledighetsansökningar",
      value: employeeStats.pendingVacationRequests,
      icon: <Calendar className="w-5 h-5 text-amber-600 dark:text-amber-400" />,
      change: employeeStats.pendingVacationRequests > 0 ? "+1" : "0",
      changeType: employeeStats.pendingVacationRequests > 0 ? "negative" as const : "positive" as const,
      changeLabel: "väntande granskning"
    }
  ];

  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4 my-6">
        {[...Array(5)].map((_, i) => (
          <div key={i} className="bg-white dark:bg-gray-800 rounded-xl p-5 animate-pulse">
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center">
                <div className="w-10 h-10 rounded-full bg-gray-200 dark:bg-gray-700 mr-3"></div>
                <div>
                  <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-20 mb-2"></div>
                  <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-16"></div>
                </div>
              </div>
            </div>
            <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-24"></div>
          </div>
        ))}
      </div>
    );
  }

  return <StatsCards metrics={employeeMetrics} />;
} 