"use client"

import React from 'react';
import { Users, Plus, Mail, Phone } from 'lucide-react';
import { GoogleCard, GoogleCardContent, GoogleCardHeader, GoogleCardTitle, GoogleButton } from '@/components/ui/google-components';

export default function ProjectsStaffingTab() {
  // Placeholder data - would come from database in real implementation
  const teamMembers = [
    {
      id: 1,
      name: "<PERSON>",
      role: "Project Manager",
      email: "<EMAIL>",
      phone: "+46 70 123 4567",
      avatar: "AA"
    },
    {
      id: 2,
      name: "<PERSON>",
      role: "<PERSON><PERSON>per",
      email: "<EMAIL>",
      phone: "+46 70 234 5678",
      avatar: "EE"
    },
    {
      id: 3,
      name: "<PERSON>",
      role: "Designer",
      email: "<EMAIL>",
      phone: "+46 70 345 6789",
      avatar: "M<PERSON>"
    }
  ];

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-medium text-gray-900">Project Staffing</h1>
        <GoogleButton>
          <Plus className="mr-2 h-4 w-4" />
          Add Team Member
        </GoogleButton>
      </div>

      {/* Team Members Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {teamMembers.map((member) => (
          <GoogleCard key={member.id}>
            <GoogleCardContent className="p-6">
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center text-white font-semibold">
                  {member.avatar}
                </div>
                <div className="flex-1">
                  <h3 className="font-semibold text-gray-900">{member.name}</h3>
                  <p className="text-sm text-gray-500">{member.role}</p>
                </div>
              </div>
              
              <div className="mt-4 space-y-2">
                <div className="flex items-center text-sm text-gray-600">
                  <Mail className="h-4 w-4 mr-2" />
                  {member.email}
                </div>
                <div className="flex items-center text-sm text-gray-600">
                  <Phone className="h-4 w-4 mr-2" />
                  {member.phone}
                </div>
              </div>
              
              <div className="mt-4 pt-4 border-t border-gray-200">
                <div className="flex space-x-2">
                  <GoogleButton variant="ghost" className="flex-1 text-sm">
                    View Details
                  </GoogleButton>
                  <GoogleButton variant="ghost" className="flex-1 text-sm">
                    Edit
                  </GoogleButton>
                </div>
              </div>
            </GoogleCardContent>
          </GoogleCard>
        ))}
      </div>

      {/* Resource Allocation */}
      <GoogleCard>
        <GoogleCardHeader>
          <GoogleCardTitle>Resource Allocation</GoogleCardTitle>
        </GoogleCardHeader>
        <GoogleCardContent>
          <div className="space-y-4">
            {teamMembers.map((member) => (
              <div key={member.id} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm font-semibold">
                    {member.avatar}
                  </div>
                  <div>
                    <p className="font-medium text-gray-900">{member.name}</p>
                    <p className="text-sm text-gray-500">{member.role}</p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-sm font-medium text-gray-900">75% Allocated</p>
                  <div className="w-24 bg-gray-200 rounded-full h-2 mt-1">
                    <div className="bg-blue-500 h-2 rounded-full" style={{ width: '75%' }}></div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </GoogleCardContent>
      </GoogleCard>

      {/* Skills Overview */}
      <GoogleCard>
        <GoogleCardHeader>
          <GoogleCardTitle>Team Skills</GoogleCardTitle>
        </GoogleCardHeader>
        <GoogleCardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {[
              { skill: "React/Next.js", count: 2 },
              { skill: "Node.js", count: 1 },
              { skill: "UI/UX Design", count: 1 },
              { skill: "Project Management", count: 1 },
              { skill: "TypeScript", count: 2 },
              { skill: "PostgreSQL", count: 1 },
              { skill: "Adobe Creative", count: 1 },
              { skill: "Agile/Scrum", count: 3 }
            ].map((item) => (
              <div key={item.skill} className="text-center p-3 border border-gray-200 rounded-lg">
                <p className="font-medium text-gray-900">{item.skill}</p>
                <p className="text-sm text-gray-500">{item.count} team member{item.count > 1 ? 's' : ''}</p>
              </div>
            ))}
          </div>
        </GoogleCardContent>
      </GoogleCard>
    </div>
  );
} 