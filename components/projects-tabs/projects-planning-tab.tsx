import React, { useEffect, useState } from 'react';
import { supabase } from '@/lib/supabase/supabase';
import { 
  GoogleCard, 
  GoogleCardHeader, 
  GoogleCardTitle, 
  GoogleCardContent,
  GoogleButton,
  GoogleBadge,
  GoogleChip,
  GoogleDivider 
} from '@/components/ui/google-components';
import { Calendar, Clock, CheckCircle, AlertCircle, PauseCircle, XCircle, ChevronLeft, ChevronRight, Flag, Calendar as CalendarIcon, Users, BriefcaseIcon, Layers, Plus } from 'lucide-react';
import Link from 'next/link';
import { formatDate } from '@/lib/utils';

interface ProjectTimeline {
  id: string;
  name: string;
  start_date: string;
  end_date: string;
  status: string;
  milestones: Milestone[];
  customer: {
    id: string;
    name: string;
  }[];
}

interface Milestone {
  id: string;
  project_id: string;
  name: string;
  due_date: string;
  status: string;
  is_milestone: boolean;
}

interface ProjectsPlanningTabProps {
  className?: string;
}

export default function ProjectsPlanningTab({}: ProjectsPlanningTabProps) {
  const [projects, setProjects] = useState<ProjectTimeline[]>([]);
  const [loading, setLoading] = useState(true);
  const [currentMonth, setCurrentMonth] = useState<Date>(new Date());
  const [view, setView] = useState<'month' | 'quarter'>('month');
  const [highlightWeekends, setHighlightWeekends] = useState(true);
  
  // Get start and end of current month
  const monthStart = new Date(currentMonth.getFullYear(), currentMonth.getMonth(), 1);
  const monthEnd = new Date(currentMonth.getFullYear(), currentMonth.getMonth() + 1, 0);
  
  // Get start and end of current quarter
  const getQuarterStartMonth = (date: Date) => Math.floor(date.getMonth() / 3) * 3;
  const quarterStartMonth = getQuarterStartMonth(currentMonth);
  const quarterStart = new Date(currentMonth.getFullYear(), quarterStartMonth, 1);
  const quarterEnd = new Date(currentMonth.getFullYear(), quarterStartMonth + 3, 0);
  
  // Get current quarter number
  const currentQuarter = Math.floor(currentMonth.getMonth() / 3) + 1;
  
  // Get today's date
  const today = new Date();
  const isCurrentMonth = today.getMonth() === currentMonth.getMonth() && 
                         today.getFullYear() === currentMonth.getFullYear();

  // Function to get week number for a date
  const getWeekNumber = (date: Date) => {
    const firstDayOfYear = new Date(date.getFullYear(), 0, 1);
    const pastDaysOfYear = (date.getTime() - firstDayOfYear.getTime()) / 86400000;
    return Math.ceil((pastDaysOfYear + firstDayOfYear.getDay() + 1) / 7);
  };

  // Function to check if date is start of week (Monday)
  const isStartOfWeek = (date: Date) => date.getDay() === 1; // Monday is 1

  useEffect(() => {
    async function fetchProjectTimelines() {
      try {
        setLoading(true);
        
        // Fetch projects with dates
        const { data: projectsData, error: projectsError } = await supabase
          .from('projects')
          .select(`
            id,
            name,
            start_date,
            end_date,
            status,
            customer:customers(id, name)
          `)
          .not('start_date', 'is', null)
          .not('end_date', 'is', null)
          .order('start_date');
          
        if (projectsError) throw projectsError;
        
        // Fetch milestones (tasks marked as milestones)
        const projects = projectsData || [];
        const projectsWithMilestones = [];
        
        for (const project of projects) {
          const { data: milestonesData, error: milestonesError } = await supabase
            .from('project_tasks')
            .select('id, project_id, name, due_date, status, is_milestone')
            .eq('project_id', project.id)
            .eq('is_milestone', true)
            .order('due_date');
            
          if (milestonesError) throw milestonesError;
          
          projectsWithMilestones.push({
            ...project,
            milestones: milestonesData || []
          });
        }
        
        setProjects(projectsWithMilestones);
      } catch (error) {
        console.error('Error fetching project timelines:', error);
      } finally {
        setLoading(false);
      }
    }
    
    fetchProjectTimelines();
  }, []);

  // Navigate to previous/next month or quarter
  const navigate = (direction: number) => {
    const newDate = new Date(currentMonth);
    if (view === 'month') {
      newDate.setMonth(newDate.getMonth() + direction);
    } else {
      // For quarter view, move 3 months at a time
      newDate.setMonth(newDate.getMonth() + (direction * 3));
    }
    setCurrentMonth(newDate);
  };
  
  // Calculate project position and width in the timeline
  const getTimelineStyles = (startDate: string, endDate: string) => {
    const start = new Date(startDate);
    const end = new Date(endDate);
    
    // Get the reference start and end dates based on the current view
    const viewStart = view === 'month' ? monthStart : quarterStart;
    const viewEnd = view === 'month' ? monthEnd : quarterEnd;
    const totalDays = (viewEnd.getTime() - viewStart.getTime()) / (1000 * 60 * 60 * 24) + 1;
    
    // Calculate days from start of view period
    const daysSinceViewStart = Math.max(0, Math.round((start.getTime() - viewStart.getTime()) / (1000 * 60 * 60 * 24)));
    
    // Calculate project duration in days
    const durationDays = Math.max(1, Math.round((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24))) + 1;
    
    // Position as percentage of view period width
    const leftPosition = (daysSinceViewStart / totalDays) * 100;
    
    // Width as percentage of view period width (capped at 100% - leftPosition)
    const width = Math.min(100 - leftPosition, (durationDays / totalDays) * 100);
    
    return {
      left: `${leftPosition}%`,
      width: `${width}%`
    };
  };
  
  // Get status color
  const getStatusColor = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'active': return 'bg-green-100 border-green-300 text-green-700';
      case 'planning': return 'bg-blue-100 border-blue-300 text-blue-700';
      case 'on-hold': return 'bg-amber-100 border-amber-300 text-amber-700';
      case 'completed': return 'bg-purple-100 border-purple-300 text-purple-700';
      case 'cancelled': return 'bg-red-100 border-red-300 text-red-700';
      default: return 'bg-gray-100 border-gray-300 text-gray-700';
    }
  };
  
  // Get status icon
  const getStatusIcon = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'active': return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'planning': return <AlertCircle className="h-4 w-4 text-blue-600" />;
      case 'on-hold': return <PauseCircle className="h-4 w-4 text-amber-600" />;
      case 'completed': return <CheckCircle className="h-4 w-4 text-purple-600" />;
      case 'cancelled': return <XCircle className="h-4 w-4 text-red-600" />;
      default: return <AlertCircle className="h-4 w-4 text-gray-600" />;
    }
  };
  
  // Check if project is in current view period
  const isProjectInViewPeriod = (startDate: string, endDate: string) => {
    const start = new Date(startDate);
    const end = new Date(endDate);
    
    // Get the reference start and end dates based on the current view
    const viewStart = view === 'month' ? monthStart : quarterStart;
    const viewEnd = view === 'month' ? monthEnd : quarterEnd;
    
    return (
      (start <= viewEnd && start >= viewStart) || // Start date in period
      (end <= viewEnd && end >= viewStart) || // End date in period
      (start <= viewStart && end >= viewEnd) // Project spans entire period
    );
  };

  // Calculate days for current view (month or quarter)
  const getViewDays = () => {
    const days = [];
    const viewStart = view === 'month' ? monthStart : quarterStart;
    const viewEnd = view === 'month' ? monthEnd : quarterEnd;
    
    // Calculate the number of days in the view period
    const dayCount = Math.round((viewEnd.getTime() - viewStart.getTime()) / (1000 * 60 * 60 * 24)) + 1;
    
    // For quarter view, we'll create week-based entries
    if (view === 'quarter') {
      // Find the first Monday on or before the start date to get a clean week
      const firstVisibleDate = new Date(viewStart);
      while (firstVisibleDate.getDay() !== 1) { // 1 is Monday
        firstVisibleDate.setDate(firstVisibleDate.getDate() - 1);
      }
      
      // Map the entire quarter by weeks
      const current = new Date(firstVisibleDate);
      const weeks = [];
      
      while (current <= viewEnd) {
        const weekStart = new Date(current);
        const weekNumber = getWeekNumber(weekStart);
        const monthName = weekStart.toLocaleString('default', { month: 'short' });
        const isCurrentWeek = 
          today >= weekStart && 
          today < new Date(weekStart.getFullYear(), weekStart.getMonth(), weekStart.getDate() + 7);
        
        // Check if this is the first week of a month
        const isFirstWeekOfMonth = weekStart.getDate() <= 7;
        
        weeks.push({
          weekStart,
          weekNumber,
          monthName,
          isCurrentWeek,
          isFirstWeekOfMonth,
          month: weekStart.getMonth()
        });
        
        // Move to next week
        current.setDate(current.getDate() + 7);
      }
      
      return weeks;
    } 
    // For month view, continue using days
    else {
      for (let i = 0; i < dayCount; i++) {
        const date = new Date(viewStart);
        date.setDate(viewStart.getDate() + i);
        
        const isWeekend = date.getDay() === 0 || date.getDay() === 6;
        const isToday = 
          date.getDate() === today.getDate() && 
          date.getMonth() === today.getMonth() && 
          date.getFullYear() === today.getFullYear();
        
        // Calculate week number
        const weekNumber = getWeekNumber(date);
        const isWeekStart = isStartOfWeek(date);
        
        days.push({ 
          date, 
          isWeekend, 
          isToday, 
          day: date.getDate(),
          month: date.getMonth(), 
          weekNumber,
          isWeekStart
        });
      }
      
      return days;
    }
  };

  // Get days or weeks for current view
  const viewDays = getViewDays();

  // Format the current period for display
  const getCurrentPeriodDisplay = () => {
    if (view === 'month') {
      return currentMonth.toLocaleString('default', { month: 'long', year: 'numeric' });
    } else {
      return `Q${currentQuarter} ${currentMonth.getFullYear()}`;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header with navigation */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-xl font-semibold text-gray-900">Project Planning</h2>
          <p className="text-sm text-gray-500 mt-1">
            Timeline view and project statistics
          </p>
        </div>
      </div>

      {/* Project Statistics */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-5 my-6">
        <div className="shadow-sm border-0 bg-white dark:bg-gray-800 rounded-xl overflow-hidden hover:shadow-md transition-shadow duration-300">
          <div className="p-5">
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center">
                <div className="w-10 h-10 rounded-full bg-gray-50 dark:bg-gray-700 flex items-center justify-center mr-3">
                  <BriefcaseIcon className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                </div>
                <div>
                  <div className="text-xs font-medium text-gray-500 dark:text-gray-400">Total Projects</div>
                  <div className="text-lg font-normal text-gray-900 dark:text-white">
                    {projects.length}
                  </div>
                </div>
              </div>
            </div>
            <div className="flex items-center">
              <span className="text-xs font-medium flex items-center text-blue-600 dark:text-blue-400">
                <BriefcaseIcon className="w-3 h-3 mr-1" />
                Alla projekt
              </span>
              <span className="text-xs text-gray-500 dark:text-gray-400 ml-2">i systemet</span>
            </div>
          </div>
        </div>

        <div className="shadow-sm border-0 bg-white dark:bg-gray-800 rounded-xl overflow-hidden hover:shadow-md transition-shadow duration-300">
          <div className="p-5">
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center">
                <div className="w-10 h-10 rounded-full bg-gray-50 dark:bg-gray-700 flex items-center justify-center mr-3">
                  <CheckCircle className="w-5 h-5 text-green-600 dark:text-green-400" />
                </div>
                <div>
                  <div className="text-xs font-medium text-gray-500 dark:text-gray-400">Active Projects</div>
                  <div className="text-lg font-normal text-gray-900 dark:text-white">
                    {projects.filter(p => p.status === 'active').length}
                  </div>
                </div>
              </div>
            </div>
            <div className="flex items-center">
              <span className="text-xs font-medium flex items-center text-green-600 dark:text-green-400">
                <CheckCircle className="w-3 h-3 mr-1" />
                Pågående
              </span>
              <span className="text-xs text-gray-500 dark:text-gray-400 ml-2">just nu</span>
            </div>
          </div>
        </div>

        <div className="shadow-sm border-0 bg-white dark:bg-gray-800 rounded-xl overflow-hidden hover:shadow-md transition-shadow duration-300">
          <div className="p-5">
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center">
                <div className="w-10 h-10 rounded-full bg-gray-50 dark:bg-gray-700 flex items-center justify-center mr-3">
                  <Flag className="w-5 h-5 text-orange-600 dark:text-orange-400" />
                </div>
                <div>
                  <div className="text-xs font-medium text-gray-500 dark:text-gray-400">Milstolpar</div>
                  <div className="text-lg font-normal text-gray-900 dark:text-white">
                    {projects.reduce((total, project) => total + project.milestones.length, 0)}
                  </div>
                </div>
              </div>
            </div>
            <div className="flex items-center">
              <span className="text-xs font-medium flex items-center text-orange-600 dark:text-orange-400">
                <Flag className="w-3 h-3 mr-1" />
                Milstolpar
              </span>
              <span className="text-xs text-gray-500 dark:text-gray-400 ml-2">totalt</span>
            </div>
          </div>
        </div>

        <div className="shadow-sm border-0 bg-white dark:bg-gray-800 rounded-xl overflow-hidden hover:shadow-md transition-shadow duration-300">
          <div className="p-5">
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center">
                <div className="w-10 h-10 rounded-full bg-gray-50 dark:bg-gray-700 flex items-center justify-center mr-3">
                  <CalendarIcon className="w-5 h-5 text-purple-600 dark:text-purple-400" />
                </div>
                <div>
                  <div className="text-xs font-medium text-gray-500 dark:text-gray-400">Completed</div>
                  <div className="text-lg font-normal text-gray-900 dark:text-white">
                    {projects.filter(p => p.status === 'completed').length}
                  </div>
                </div>
              </div>
            </div>
            <div className="flex items-center">
              <span className="text-xs font-medium flex items-center text-purple-600 dark:text-purple-400">
                <CalendarIcon className="w-3 h-3 mr-1" />
                Avslutade
              </span>
              <span className="text-xs text-gray-500 dark:text-gray-400 ml-2">projekt</span>
            </div>
          </div>
        </div>

        <div className="shadow-sm border-0 bg-white dark:bg-gray-800 rounded-xl overflow-hidden hover:shadow-md transition-shadow duration-300">
          <div className="p-5">
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center">
                <div className="w-10 h-10 rounded-full bg-gray-50 dark:bg-gray-700 flex items-center justify-center mr-3">
                  <Users className="w-5 h-5 text-emerald-600 dark:text-emerald-400" />
                </div>
                <div>
                  <div className="text-xs font-medium text-gray-500 dark:text-gray-400">Team Members</div>
                  <div className="text-lg font-normal text-gray-900 dark:text-white">
                    {projects.reduce((total, project) => total + (project.customer?.length || 0), 0)}
                  </div>
                </div>
              </div>
            </div>
            <div className="flex items-center">
              <span className="text-xs font-medium flex items-center text-emerald-600 dark:text-emerald-400">
                <Users className="w-3 h-3 mr-1" />
                Teammedlemmar
              </span>
              <span className="text-xs text-gray-500 dark:text-gray-400 ml-2">totalt</span>
            </div>
          </div>
        </div>
      </div>

      {/* Timeline Controls */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h3 className="text-lg font-medium text-gray-900">Timeline View</h3>
          <p className="text-sm text-gray-500 mt-1">
            {getCurrentPeriodDisplay()}
          </p>
        </div>
        
        <div className="flex items-center space-x-4">
          {/* View toggle */}
          <div className="flex bg-gray-100 rounded-lg p-1">
            <button
              onClick={() => setView('month')}
              className={`px-3 py-1 text-sm rounded-md transition-colors ${
                view === 'month' 
                  ? 'bg-white text-gray-900 shadow-sm' 
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              Month
            </button>
            <button
              onClick={() => setView('quarter')}
              className={`px-3 py-1 text-sm rounded-md transition-colors ${
                view === 'quarter' 
                  ? 'bg-white text-gray-900 shadow-sm' 
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              Quarter
            </button>
          </div>
          
          {/* Navigation */}
          <div className="flex items-center space-x-2">
            <button
              onClick={() => navigate(-1)}
              className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
            >
              <ChevronLeft className="h-4 w-4" />
            </button>
            <span className="text-sm font-medium min-w-[120px] text-center">
              {getCurrentPeriodDisplay()}
            </span>
            <button
              onClick={() => navigate(1)}
              className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
            >
              <ChevronRight className="h-4 w-4" />
            </button>
          </div>
        </div>
      </div>

      {loading ? (
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      ) : (
        <>
          {/* Timeline Chart */}
          <GoogleCard>
            <GoogleCardHeader>
              <GoogleCardTitle className="flex items-center">
                <Calendar className="h-5 w-5 mr-2" />
                Project Timeline
              </GoogleCardTitle>
            </GoogleCardHeader>
            <GoogleCardContent className="p-0">
              <div className="overflow-x-auto">
                <div className="min-w-[800px]">
                  {/* Timeline header */}
                  <div className="flex border-b border-gray-200 bg-gray-50">
                    <div className="w-64 p-4 font-medium text-gray-900 border-r border-gray-200">
                      Project
                    </div>
                    <div className="flex-1 relative">
                      {/* Time scale */}
                      <div className="flex h-12 border-b border-gray-200">
                        {view === 'month' ? (
                          viewDays.map((day, index) => {
                            // Type guard for month view
                            if (view === 'month' && 'isToday' in day) {
                              return (
                                <div
                                  key={index}
                                  className={`flex-1 min-w-[24px] text-center text-xs border-r border-gray-100 flex flex-col justify-center ${
                                    day.isToday ? 'bg-blue-50 text-blue-600 font-medium' : ''
                                  } ${
                                    day.isWeekend && highlightWeekends ? 'bg-gray-100' : ''
                                  }`}
                                >
                                  <div className="font-medium">{day.day}</div>
                                  {day.isWeekStart && (
                                    <div className="text-[10px] text-gray-500">W{day.weekNumber}</div>
                                  )}
                                </div>
                              );
                            }
                            return null;
                          })
                        ) : (
                          viewDays.map((week, index) => {
                            // Type guard for quarter view
                            if (view === 'quarter' && 'isCurrentWeek' in week) {
                              return (
                                <div
                                  key={index}
                                  className={`flex-1 min-w-[60px] text-center text-xs border-r border-gray-100 flex flex-col justify-center ${
                                    week.isCurrentWeek ? 'bg-blue-50 text-blue-600 font-medium' : ''
                                  }`}
                                >
                                  <div className="font-medium">W{week.weekNumber}</div>
                                  {week.isFirstWeekOfMonth && (
                                    <div className="text-[10px] text-gray-500">{week.monthName}</div>
                                  )}
                                </div>
                              );
                            }
                            return null;
                          })
                        )}
                      </div>
                      
                      {/* Today indicator */}
                      {view === 'month' && isCurrentMonth && (
                        <div
                          className="absolute top-0 bottom-0 w-px bg-red-500 z-10"
                          style={{
                            left: `${(today.getDate() - 1) / viewDays.length * 100}%`
                          }}
                        >
                          <div className="absolute -top-1 left-0 transform -translate-x-1/2 w-2 h-2 bg-red-500 rounded-full"></div>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Project rows */}
                  <div className="divide-y divide-gray-100">
                    {projects.filter(project => 
                      isProjectInViewPeriod(project.start_date, project.end_date)
                    ).map((project) => {
                      const timelineStyles = getTimelineStyles(project.start_date, project.end_date);
                      
                      return (
                        <div key={project.id} className="flex hover:bg-gray-50">
                          <div className="w-64 p-4 border-r border-gray-200">
                            <div className="flex items-center space-x-3">
                              <div className="flex-shrink-0">
                                {getStatusIcon(project.status)}
                              </div>
                              <div className="min-w-0 flex-1">
                                <Link 
                                  href={`/dashboard/projects/${project.id}`}
                                  className="text-sm font-medium text-gray-900 hover:text-blue-600 truncate block"
                                >
                                  {project.name}
                                </Link>
                                <p className="text-xs text-gray-500 truncate">
                                  {project.customer?.[0]?.name || 'No customer'}
                                </p>
                              </div>
                            </div>
                          </div>
                          
                          <div className="flex-1 relative h-16 flex items-center">
                            {/* Project timeline bar */}
                            <div
                              className={`absolute h-6 rounded-md border-2 ${getStatusColor(project.status)} flex items-center justify-center`}
                              style={timelineStyles}
                            >
                              <span className="text-xs font-medium px-2 truncate">
                                {project.name.length > 20 ? project.name.substring(0, 20) + '...' : project.name}
                              </span>
                            </div>
                            
                            {/* Milestones */}
                            {project.milestones.map((milestone) => {
                              if (!milestone.due_date || !isProjectInViewPeriod(milestone.due_date, milestone.due_date)) {
                                return null;
                              }
                              
                              const milestoneStyles = getTimelineStyles(milestone.due_date, milestone.due_date);
                              
                              return (
                                <div
                                  key={milestone.id}
                                  className="absolute top-1/2 transform -translate-y-1/2 -translate-x-1/2 z-10"
                                  style={{ left: milestoneStyles.left }}
                                  title={`${milestone.name} - ${formatDate(milestone.due_date)}`}
                                >
                                  <Flag className="h-4 w-4 text-orange-500" />
                                </div>
                              );
                            })}
                          </div>
                        </div>
                      );
                    })}
                  </div>
                  
                  {projects.filter(project => 
                    isProjectInViewPeriod(project.start_date, project.end_date)
                  ).length === 0 && (
                    <div className="text-center py-12 text-gray-500">
                      <Calendar className="h-8 w-8 mx-auto mb-2 text-gray-300" />
                      <p>No projects scheduled for {getCurrentPeriodDisplay()}</p>
                    </div>
                  )}
                </div>
              </div>
            </GoogleCardContent>
          </GoogleCard>
        </>
      )}
    </div>
  );
} 