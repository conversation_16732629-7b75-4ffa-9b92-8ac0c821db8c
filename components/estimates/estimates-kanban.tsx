"use client"

import React, { useState, useEffect } from "react"
import {
  DndContext,
  closestCenter,
  useDraggable,
  useDroppable,
  DragOverlay,
  DragEndEvent,
} from "@dnd-kit/core"
import { CSS } from '@dnd-kit/utilities'
import { formatCurrency, formatDate } from '@/lib/utils'
import { useEstimates } from '@/lib/hooks/use-estimates'

interface Estimate {
  id: string
  customer_name: string
  project_name: string
  created_at: string
  total_amount: number
  status: string
}

const statusColumns = [
  { key: "draft", label: "Utkast" },
  { key: "pending", label: "Väntande" },
  { key: "accepted", label: "Godkända" },
  { key: "rejected", label: "Förlorade" },
]

function DraggableEstimate({ estimate, dragging, bump }: { estimate: Estimate, dragging?: boolean, bump?: boolean }) {
  return (
    <div
      className={`bg-white dark:bg-gray-900 rounded-md shadow p-3 border border-gray-100 dark:border-gray-800 select-none transition-all duration-200
        ${dragging ? 'scale-105 z-50 shadow-2xl opacity-90' : ''}
        ${bump ? 'animate-bump' : ''}
        cursor-move`}
      style={{ pointerEvents: dragging ? 'none' : undefined }}
    >
      <div className="text-xs text-gray-500 dark:text-gray-400 mb-1">{estimate.id}</div>
      <div className="font-medium text-gray-900 dark:text-white">{estimate.project_name}</div>
      <div className="text-xs text-gray-500 dark:text-gray-400">{estimate.customer_name}</div>
      <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">{formatDate(estimate.created_at)}</div>
      <div className="text-xs text-blue-700 dark:text-blue-300 font-semibold mt-1">{formatCurrency(estimate.total_amount)}</div>
    </div>
  )
}

export function EstimatesKanban() {
  const [estimates, setEstimates] = useState<Estimate[]>([])
  const [loading, setLoading] = useState(true)
  const [activeId, setActiveId] = useState<string | null>(null)
  const [overColumn, setOverColumn] = useState<string | null>(null)
  const { triggerRefresh } = useEstimates()

  useEffect(() => {
    async function fetchEstimates() {
      try {
        const response = await fetch('/api/estimates')
        if (response.ok) {
          const data = await response.json()
          setEstimates(data)
        }
      } catch (error) {
        console.error('Error fetching estimates:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchEstimates()
  }, [])

  function handleDragStart(event: any) {
    setActiveId(event.active.id)
  }

  function handleDragOver(event: any) {
    if (event.over && statusColumns.some(col => col.key === event.over.id)) {
      setOverColumn(event.over.id)
    } else {
      setOverColumn(null)
    }
  }

  async function handleDragEnd(event: DragEndEvent) {
    const { active, over } = event
    setActiveId(null)
    setOverColumn(null)
    if (!over) return
    
    const newStatus = statusColumns.find(col => col.key === over.id)?.key
    if (!newStatus) return

    const estimate = estimates.find(e => e.id === active.id)
    if (!estimate || estimate.status === newStatus) return

    // Optimistically update the UI
    setEstimates((prev) =>
      prev.map((e) =>
        e.id === active.id ? { ...e, status: newStatus } : e
      )
    )

    // Update the backend
    try {
      const response = await fetch(`/api/estimates/${active.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          status: newStatus,
        }),
      })

      if (!response.ok) {
        // Revert on error
        setEstimates((prev) =>
          prev.map((e) =>
            e.id === active.id ? { ...e, status: estimate.status } : e
          )
        )
        console.error('Failed to update estimate status')
      } else {
        // Success - trigger refresh of statistics
        triggerRefresh()
      }
    } catch (error) {
      // Revert on error
      setEstimates((prev) =>
        prev.map((e) =>
          e.id === active.id ? { ...e, status: estimate.status } : e
        )
      )
      console.error('Error updating estimate:', error)
    }
  }

  const activeEstimate = estimates.find(e => e.id === activeId)

  if (loading) {
    return (
      <div className="flex gap-4 overflow-x-auto pb-2">
        {statusColumns.map((col) => (
          <div key={col.key} className="flex-1 min-w-[260px] bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-3 animate-pulse">
            <div className="h-6 bg-gray-200 rounded w-24 mb-3"></div>
            <div className="space-y-3">
              {[...Array(2)].map((_, i) => (
                <div key={i} className="bg-gray-200 rounded-md h-20"></div>
              ))}
            </div>
          </div>
        ))}
      </div>
    )
  }

  return (
    <>
      <style>{`
        @keyframes bump {
          0% { transform: scale(1); }
          30% { transform: scale(1.04) rotate(-1deg); }
          60% { transform: scale(0.98) rotate(1deg); }
          100% { transform: scale(1); }
        }
        .animate-bump { animation: bump 0.4s; }
      `}</style>
      <DndContext
        collisionDetection={closestCenter}
        onDragStart={handleDragStart}
        onDragOver={handleDragOver}
        onDragEnd={handleDragEnd}
      >
        <div className="flex gap-4 overflow-x-auto pb-2">
          {statusColumns.map((col) => (
            <KanbanColumn
              key={col.key}
              id={col.key}
              label={col.label}
              estimates={estimates.filter(e => e.status === col.key)}
              draggingId={activeId}
              highlight={overColumn === col.key}
            />
          ))}
        </div>
        <DragOverlay dropAnimation={{ duration: 200, easing: 'cubic-bezier(.18,.67,.6,1.22)' }}>
          {activeEstimate ? (
            <DraggableEstimate estimate={activeEstimate} dragging />
          ) : null}
        </DragOverlay>
      </DndContext>
    </>
  )
}

function KanbanColumn({ id, label, estimates, draggingId, highlight }: { id: string, label: string, estimates: Estimate[], draggingId?: string | null, highlight?: boolean }) {
  const { setNodeRef, isOver } = useDroppable({ id })
  return (
    <div
      ref={setNodeRef}
      className={`flex-1 min-w-[260px] bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-3 transition-colors duration-200
        ${highlight || isOver ? 'ring-2 ring-blue-500 ring-offset-2 ring-offset-gray-50 dark:ring-offset-gray-800' : ''}`}
      style={{ minHeight: 120 }}
    >
      <h3 className="text-md font-semibold mb-3 text-gray-700 dark:text-gray-200">
        {label} <span className="text-xs text-gray-500">({estimates.length})</span>
      </h3>
      <div className="space-y-3 min-h-[40px]">
        {estimates.length === 0 && (
          <div className="text-xs text-gray-400 text-center py-4">Inga offerter</div>
        )}
        {estimates.map((estimate) => (
          <KanbanDraggableCard
            key={estimate.id}
            estimate={estimate}
            bump={!!(draggingId && draggingId !== estimate.id && isOver)}
          />
        ))}
      </div>
    </div>
  )
}

function KanbanDraggableCard({ estimate, bump }: { estimate: Estimate, bump?: boolean }) {
  const { attributes, listeners, setNodeRef, isDragging } = useDraggable({ id: estimate.id })
  return (
    <div
      ref={setNodeRef}
      {...attributes}
      {...listeners}
      style={{
        opacity: isDragging ? 0 : 1,
        transition: 'opacity 0.2s',
      }}
    >
      <DraggableEstimate estimate={estimate} bump={bump} />
    </div>
  )
}

export { EstimatesKanban as default } 