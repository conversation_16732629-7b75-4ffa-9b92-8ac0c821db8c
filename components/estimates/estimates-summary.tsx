'use client'

import { <PERSON><PERSON><PERSON><PERSON>, FileQuestion, FileText, FileUp, FileX } from "lucide-react"
import { useEffect, useState, useCallback } from 'react'
import { useEstimates } from '@/lib/hooks/use-estimates'

interface EstimateStats {
  total: { count: number; change: number }
  accepted: { count: number; change: number }
  pending: { count: number; change: number }
  draft: { count: number; change: number }
  rejected: { count: number; change: number }
}

export function EstimatesSummary() {
  const [stats, setStats] = useState<EstimateStats | null>(null)
  const [loading, setLoading] = useState(true)
  const { refreshTrigger } = useEstimates()

  const fetchStats = useCallback(async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/estimates/stats')
      if (response.ok) {
        const data = await response.json()
        setStats(data)
      }
    } catch (error) {
      console.error('Error fetching estimate stats:', error)
    } finally {
      setLoading(false)
    }
  }, [])

  useEffect(() => {
    fetchStats()
  }, [fetchStats, refreshTrigger]) // Re-fetch when refreshTrigger changes

  if (loading) {
    return (
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-5">
        {[...Array(5)].map((_, i) => (
          <div key={i} className="bg-white dark:bg-gray-800 p-6 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm animate-pulse">
            <div className="flex justify-between items-start">
              <div>
                <div className="h-4 bg-gray-200 rounded w-24 mb-2"></div>
                <div className="h-8 bg-gray-200 rounded w-12 mb-1"></div>
                <div className="h-3 bg-gray-200 rounded w-32"></div>
              </div>
              <div className="rounded-full p-2 bg-gray-100 w-9 h-9"></div>
            </div>
          </div>
        ))}
      </div>
    )
  }

  if (!stats) {
    return (
      <div className="text-center text-gray-500 dark:text-gray-400 py-8">
        Kunde inte ladda statistik
      </div>
    )
  }

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-5">
      {/* Totalt - översikt först */}
      <div className="bg-white dark:bg-gray-800 p-6 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm">
        <div className="flex justify-between items-start">
          <div>
            <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Totalt Offerter</p>
            <h3 className="text-2xl font-bold text-gray-900 dark:text-white mt-1">{stats.total.count}</h3>
            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
              {stats.total.change > 0 ? '+' : ''}{stats.total.change}% från föregående period
            </p>
          </div>
          <div className="rounded-full p-2 bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-300">
            <FileText className="h-5 w-5" />
          </div>
        </div>
      </div>
      
      {/* Utkast - samma ordning som i Kanban */}
      <div className="bg-white dark:bg-gray-800 p-6 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm">
        <div className="flex justify-between items-start">
          <div>
            <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Utkast</p>
            <h3 className="text-2xl font-bold text-gray-900 dark:text-white mt-1">{stats.draft.count}</h3>
            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
              {stats.draft.change > 0 ? '+' : ''}{stats.draft.change}% från föregående period
            </p>
          </div>
          <div className="rounded-full p-2 bg-purple-100 dark:bg-purple-900 text-purple-600 dark:text-purple-300">
            <FileUp className="h-5 w-5" />
          </div>
        </div>
      </div>
      
      {/* Väntande */}
      <div className="bg-white dark:bg-gray-800 p-6 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm">
        <div className="flex justify-between items-start">
          <div>
            <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Väntande Offerter</p>
            <h3 className="text-2xl font-bold text-gray-900 dark:text-white mt-1">{stats.pending.count}</h3>
            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
              {stats.pending.change > 0 ? '+' : ''}{stats.pending.change}% från föregående period
            </p>
          </div>
          <div className="rounded-full p-2 bg-yellow-100 dark:bg-yellow-900 text-yellow-600 dark:text-yellow-300">
            <FileQuestion className="h-5 w-5" />
          </div>
        </div>
      </div>
      
      {/* Godkända */}
      <div className="bg-white dark:bg-gray-800 p-6 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm">
        <div className="flex justify-between items-start">
          <div>
            <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Godkända Offerter</p>
            <h3 className="text-2xl font-bold text-gray-900 dark:text-white mt-1">{stats.accepted.count}</h3>
            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
              {stats.accepted.change > 0 ? '+' : ''}{stats.accepted.change}% från föregående period
            </p>
          </div>
          <div className="rounded-full p-2 bg-green-100 dark:bg-green-900 text-green-600 dark:text-green-300">
            <FileCheck className="h-5 w-5" />
          </div>
        </div>
      </div>
      
      {/* Förlorade */}
      <div className="bg-white dark:bg-gray-800 p-6 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm">
        <div className="flex justify-between items-start">
          <div>
            <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Förlorade Offerter</p>
            <h3 className="text-2xl font-bold text-gray-900 dark:text-white mt-1">{stats.rejected.count}</h3>
            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
              {stats.rejected.change > 0 ? '+' : ''}{stats.rejected.change}% från föregående period
            </p>
          </div>
          <div className="rounded-full p-2 bg-red-100 dark:bg-red-900 text-red-600 dark:text-red-300">
            <FileX className="h-5 w-5" />
          </div>
        </div>
      </div>
    </div>
  )
} 