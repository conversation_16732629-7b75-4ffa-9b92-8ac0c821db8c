"use client"

import Link from "next/link"
import { formatCurrency, formatDate } from "@/lib/utils"

interface EstimateCardProps {
  estimate: {
    id: number
    estimate_number: string
    customer_name: string
    total_amount: number
    status: string
    created_at: string
    valid_until?: string
  }
}

export function EstimateCard({ estimate }: EstimateCardProps) {
  return (
    <div className="bg-white border border-gray-200 rounded-lg p-6 hover:shadow-lg transition-shadow">
      <div className="flex justify-between items-start mb-4">
        <div>
          <h3 className="text-lg font-semibold text-gray-900">
            {estimate.estimate_number}
          </h3>
          <p className="text-gray-600">{estimate.customer_name}</p>
        </div>
        <span className={`px-3 py-1 rounded-full text-sm font-medium ${
          estimate.status === 'approved' 
            ? 'bg-green-100 text-green-800'
            : estimate.status === 'rejected'
            ? 'bg-red-100 text-red-800'
            : 'bg-yellow-100 text-yellow-800'
        }`}>
          {estimate.status}
        </span>
      </div>
      
      <div className="space-y-2 mb-4">
        <div className="flex justify-between">
          <span className="text-gray-600">Belopp:</span>
          <span className="font-semibold">{formatCurrency(estimate.total_amount)}</span>
        </div>
        <div className="flex justify-between">
          <span className="text-gray-600">Skapad:</span>
          <span>{formatDate(estimate.created_at)}</span>
        </div>
        {estimate.valid_until && (
          <div className="flex justify-between">
            <span className="text-gray-600">Giltig till:</span>
            <span>{formatDate(estimate.valid_until)}</span>
          </div>
        )}
      </div>
      
      <div className="flex space-x-2">
        <Link
          href={`/dashboard/estimates/${estimate.id}`}
          className="flex-1 bg-blue-500 text-white px-4 py-2 rounded-md text-center hover:bg-blue-600 transition-colors"
        >
          Visa
        </Link>
        <Link
          href={`/dashboard/estimates/${estimate.id}/edit`}
          className="flex-1 bg-gray-500 text-white px-4 py-2 rounded-md text-center hover:bg-gray-600 transition-colors"
        >
          Redigera
        </Link>
      </div>
    </div>
  )
} 