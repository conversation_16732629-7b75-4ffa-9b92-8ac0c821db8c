'use client'

import React, { createContext, useContext, useCallback, useState } from 'react'

interface EstimatesContextType {
  refreshTrigger: number
  triggerRefresh: () => void
}

const EstimatesContext = createContext<EstimatesContextType | undefined>(undefined)

export function EstimatesProvider({ children }: { children: React.ReactNode }) {
  const [refreshTrigger, setRefreshTrigger] = useState(0)

  const triggerRefresh = useCallback(() => {
    setRefreshTrigger(prev => prev + 1)
  }, [])

  const value: EstimatesContextType = {
    refreshTrigger,
    triggerRefresh
  }

  return (
    <EstimatesContext.Provider value={value}>
      {children}
    </EstimatesContext.Provider>
  )
}

export function useEstimates() {
  const context = useContext(EstimatesContext)
  if (context === undefined) {
    throw new Error('useEstimates must be used within an EstimatesProvider')
  }
  return context
} 