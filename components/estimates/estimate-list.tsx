"use client"

import { EstimateCard } from "./estimate-card"

interface Estimate {
  id: number
  estimate_number: string
  customer_name: string
  total_amount: number
  status: string
  created_at: string
  valid_until?: string
}

interface EstimateListProps {
  estimates: Estimate[]
}

export function EstimateList({ estimates }: EstimateListProps) {
  if (estimates.length === 0) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-500 text-lg">Inga offerter hittades</p>
      </div>
    )
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {estimates.map((estimate) => (
        <EstimateCard key={estimate.id} estimate={estimate} />
      ))}
    </div>
  )
} 