'use client'

import React, { useEffect, useState, useCallback } from 'react'
import { Download, Eye, MoreHorizontal, Check, Clock, FileText, FileX, Edit, Calendar, User, DollarSign, Receipt, CheckCircle2, XCircle, Calculator, Hammer, X, Search, Filter, ChevronDown } from "lucide-react"
import { formatCurrency, formatDate } from '@/lib/utils'
import { useEstimates } from '@/lib/hooks/use-estimates'
import { ExpandableList } from '@/components/ui/expandable-list'
import { Button } from '@/components/ui/button'
import Link from 'next/link'

interface Estimate {
  id: string
  estimate_number: string
  customer_name: string
  project_name?: string
  created_at: string
  date?: string
  total_amount: number
  status: string
}

interface EstimateWithDetails extends Estimate {
  customers?: {
    name: string;
    company_name?: string;
    email: string;
    phone?: string;
  };
  description?: string;
  date: string;
  valid_until?: string;
  payment_terms?: string;
  intro_text?: string;
  closing_text?: string;
  estimate_line_items: any[];
}

// PDF Preview Modal Component
const PDFPreviewModal = ({ 
  isOpen, 
  onClose, 
  estimate
}: { 
  isOpen: boolean;
  onClose: () => void; 
  estimate: EstimateWithDetails | null;
}) => {
  if (!isOpen || !estimate) return null;

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('sv-SE', { style: 'currency', currency: 'SEK' }).format(amount);
  };

  const formatDate = (dateString: string) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString('sv-SE');
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-800 rounded-lg max-w-4xl w-full max-h-[90vh] overflow-hidden">
        <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">Förhandsgranska offert</h3>
          <Button variant="outline" size="sm" onClick={onClose}>
            <X className="w-4 h-4" />
          </Button>
        </div>
        
        <div className="overflow-y-auto max-h-[calc(90vh-120px)]">
          <div className="p-8 bg-white text-gray-900">
            <div className="text-center">
              <h2 className="text-2xl font-bold mb-4">Offert Preview</h2>
              <p>PDF Preview functionality would be implemented here</p>
            </div>
          </div>
        </div>
        
        <div className="flex items-center justify-end gap-3 p-4 border-t border-gray-200 dark:border-gray-700">
          <Button variant="outline" onClick={() => window.print()}>
            <FileText className="w-4 h-4 mr-2" />
            Skriv ut
          </Button>
          <Button onClick={onClose}>
            Stäng
          </Button>
        </div>
      </div>
    </div>
  );
};

export function EstimatesList() {
  const [estimates, setEstimates] = useState<Estimate[]>([])
  const [filteredEstimates, setFilteredEstimates] = useState<Estimate[]>([])
  const [loading, setLoading] = useState(true)
  const [openDropdown, setOpenDropdown] = useState<string | null>(null)
  const { refreshTrigger, triggerRefresh } = useEstimates()
  const [error, setError] = useState<string | null>(null)
  
  // PDF Preview states
  const [showPDFPreview, setShowPDFPreview] = useState(false)
  const [previewEstimate, setPreviewEstimate] = useState<EstimateWithDetails | null>(null)
  const [loadingEstimate, setLoadingEstimate] = useState(false)

  // Filter states
  const [showFilters, setShowFilters] = useState(false)
  const [filters, setFilters] = useState({
    search: '',
    status: 'all',
    dateFrom: '',
    dateTo: '',
    amountMin: '',
    amountMax: ''
  })

  const fetchEstimates = useCallback(async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/estimates')
      
      if (response.ok) {
        const data = await response.json()
        setEstimates(data)
        setFilteredEstimates(data)
      } else {
        const errorText = await response.text()
        console.error('Failed to fetch estimates:', response.status, errorText)
        setError('Failed to load estimates')
      }
    } catch (error) {
      console.error('Error fetching estimates:', error)
      setError('Failed to load estimates')
    } finally {
      setLoading(false)
    }
  }, [])

  // Filter logic
  useEffect(() => {
    let filtered = [...estimates]

    // Search filter
    if (filters.search) {
      const searchLower = filters.search.toLowerCase()
      filtered = filtered.filter(estimate => 
        estimate.customer_name?.toLowerCase().includes(searchLower) ||
        estimate.estimate_number?.toLowerCase().includes(searchLower) ||
        estimate.project_name?.toLowerCase().includes(searchLower)
      )
    }

    // Status filter
    if (filters.status !== 'all') {
      filtered = filtered.filter(estimate => estimate.status === filters.status)
    }

    // Date range filter
    if (filters.dateFrom) {
      filtered = filtered.filter(estimate => {
        const estimateDate = new Date(estimate.date || estimate.created_at)
        return estimateDate >= new Date(filters.dateFrom)
      })
    }

    if (filters.dateTo) {
      filtered = filtered.filter(estimate => {
        const estimateDate = new Date(estimate.date || estimate.created_at)
        return estimateDate <= new Date(filters.dateTo)
      })
    }

    // Amount range filter
    if (filters.amountMin) {
      filtered = filtered.filter(estimate => estimate.total_amount >= parseFloat(filters.amountMin))
    }

    if (filters.amountMax) {
      filtered = filtered.filter(estimate => estimate.total_amount <= parseFloat(filters.amountMax))
    }

    setFilteredEstimates(filtered)
  }, [estimates, filters])

  const handleFilterChange = (key: string, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }))
  }

  const clearFilters = () => {
    setFilters({
      search: '',
      status: 'all',
      dateFrom: '',
      dateTo: '',
      amountMin: '',
      amountMax: ''
    })
  }

  const hasActiveFilters = Object.entries(filters).some(([key, value]) => 
    key !== 'status' ? value !== '' : value !== 'all'
  )

  useEffect(() => {
    fetchEstimates()
  }, [fetchEstimates, refreshTrigger])

  const updateEstimateStatus = async (estimateId: string, newStatus: string) => {
    try {
      const response = await fetch(`/api/estimates/${estimateId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status: newStatus }),
      });

      if (response.ok) {
        setEstimates(prev => prev.map(est => 
          est.id === estimateId ? { ...est, status: newStatus } : est
        ));
        triggerRefresh();
        setOpenDropdown(null);

        if (newStatus === 'accepted') {
          const createProject = confirm('Offert godkänd! Vill du skapa ett projekt baserat på denna offert?');
          if (createProject) {
            await createProjectFromEstimate(estimateId);
          }
        }
      } else {
        alert('Kunde inte uppdatera offertens status');
      }
    } catch (error) {
      console.error('Error updating estimate status:', error);
      alert('Ett fel uppstod vid uppdatering av status');
    }
  };

  const createProjectFromEstimate = async (estimateId: string) => {
    try {
      const response = await fetch(`/api/estimates/${estimateId}/create-project`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const { projectId } = await response.json();
        alert('Projekt skapat framgångsrikt!');
        window.open(`/dashboard/projects/${projectId}`, '_blank');
      } else {
        const errorData = await response.json();
        alert(`Kunde inte skapa projekt: ${errorData.error || 'Okänt fel'}`);
      }
    } catch (error) {
      console.error('Error creating project from estimate:', error);
      alert('Ett fel uppstod vid skapande av projekt');
    }
  };

  const createInvoiceFromEstimate = async (estimateId: string) => {
    try {
      const response = await fetch(`/api/estimates/${estimateId}/create-invoice`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const { invoiceId } = await response.json();
        alert('Faktura skapad framgångsrikt!');
        window.open(`/dashboard/invoices/${invoiceId}`, '_blank');
      } else {
        const errorData = await response.json();
        alert(`Kunde inte skapa faktura: ${errorData.error || 'Okänt fel'}`);
      }
    } catch (error) {
      console.error('Error creating invoice from estimate:', error);
      alert('Ett fel uppstod vid skapande av faktura');
    }
  };

  const getStatusDisplayName = (status: string) => {
    switch (status) {
      case 'accepted': return 'Godkänd'
      case 'pending': return 'Väntande'
      case 'draft': return 'Utkast'
      case 'rejected': return 'Avvisad'
      default: return status
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'accepted': 
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
      case 'draft':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
      case 'rejected':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
    }
  }

  // Helper function to calculate waiting days
  const calculateWaitingDays = (estimateDate: string) => {
    if (!estimateDate) return 0;
    const today = new Date();
    const estimate = new Date(estimateDate);
    const diffTime = today.getTime() - estimate.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  }

  // Helper function to get waiting badge color based on days
  const getWaitingBadgeColor = (days: number) => {
    if (days <= 7) return 'bg-green-100 text-green-700 dark:bg-green-900 dark:text-green-300';
    if (days <= 30) return 'bg-yellow-100 text-yellow-700 dark:bg-yellow-900 dark:text-yellow-300';
    if (days <= 90) return 'bg-orange-100 text-orange-700 dark:bg-orange-900 dark:text-orange-300';
    return 'bg-red-100 text-red-700 dark:bg-red-900 dark:text-red-300';
  }

  const handleViewEstimate = (estimateId: string) => {
    window.open(`/dashboard/estimates/${estimateId}`, '_blank');
  };

  const handleDownloadEstimate = (estimateId: string) => {
    window.open(`/api/estimates/${estimateId}/pdf`, '_blank');
  };

  const handlePreviewEstimate = async (estimateId: string) => {
    setLoadingEstimate(true);
    try {
      const response = await fetch(`/api/estimates/${estimateId}`);
      if (response.ok) {
        const data = await response.json();
        setPreviewEstimate(data.estimate);
        setShowPDFPreview(true);
      } else {
        alert('Kunde inte ladda offerten för förhandsvisning');
      }
    } catch (error) {
      console.error('Error fetching estimate for preview:', error);
      alert('Ett fel uppstod vid laddning av förhandsvisning');
    } finally {
      setLoadingEstimate(false);
    }
  };

  const handleEditEstimate = (estimateId: string) => {
    window.open(`/dashboard/estimates/${estimateId}`, '_blank');
  };

  const estimateColumns = [
    { key: 'number', label: 'Offertnummer' },
    { key: 'customer', label: 'Kund' },
    { key: 'project', label: 'Projekt' },
    { key: 'amount', label: 'Belopp' },
    { key: 'status', label: 'Status' },
    { key: 'waiting', label: 'Väntetid' },
    { key: 'actions', label: 'Åtgärder', className: 'text-right' }
  ];

  const renderEstimateCell = (estimate: Estimate, columnKey: string) => {
    switch (columnKey) {
      case 'number':
        return (
          <div>
            <Link href={`/dashboard/estimates/${estimate.id}`} className="text-blue-600 dark:text-blue-400 hover:underline font-medium">
              {estimate.estimate_number || estimate.id}
            </Link>
            <p className="text-sm text-gray-500 dark:text-gray-400">{formatDate(estimate.created_at)}</p>
          </div>
        );
      case 'customer':
        return (
          <span className="text-gray-700 dark:text-gray-300">
            {estimate.customer_name}
          </span>
        );
      case 'project':
        return (
          <span className="text-gray-700 dark:text-gray-300">
            {estimate.project_name || '-'}
          </span>
        );
      case 'amount':
        return (
          <span className="text-gray-700 dark:text-gray-300 font-medium">
            {formatCurrency(estimate.total_amount)}
          </span>
        );
      case 'status':
        return (
          <span className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(estimate.status)} w-fit`}>
            {getStatusDisplayName(estimate.status)}
          </span>
        );
      case 'waiting':
        const waitingDays = calculateWaitingDays(estimate.date || estimate.created_at);
        const waitingBadgeColor = getWaitingBadgeColor(waitingDays);
        const shouldShowWaitingBadge = (estimate.status === 'draft' || estimate.status === 'pending') && waitingDays > 0;
        
        return (
          <div className="flex flex-col gap-1">
            {shouldShowWaitingBadge && (
              <span className={`px-2 py-1 rounded-full text-xs font-medium ${waitingBadgeColor} w-fit`}>
                ⏱️ {waitingDays} dagar
              </span>
            )}
          </div>
        );
      case 'actions':
        return (
          <div className="flex items-center justify-end space-x-2" onClick={(e) => e.stopPropagation()}>
            <button
              onClick={() => handleViewEstimate(estimate.id)}
              className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 p-1 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700"
              title="Visa offert"
            >
              <Eye className="h-4 w-4" />
            </button>
            <button
              onClick={() => handleDownloadEstimate(estimate.id)}
              className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 p-1 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700"
              title="Ladda ner PDF"
            >
              <Download className="h-4 w-4" />
            </button>
            <div className="relative">
              <button 
                onClick={() => setOpenDropdown(openDropdown === estimate.id ? null : estimate.id)}
                className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 p-1 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700"
                title="Mer åtgärder"
              >
                <MoreHorizontal className="h-4 w-4" />
              </button>

              {openDropdown === estimate.id && (
                <div className="absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-md shadow-lg border border-gray-200 dark:border-gray-700 z-[100]">
                  <div className="py-1">
                    <div className="px-4 py-2 text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Ändra status
                    </div>
                    
                    {estimate.status !== 'draft' && (
                      <button
                        onClick={() => updateEstimateStatus(estimate.id, 'draft')}
                        className="flex items-center w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
                      >
                        <Edit className="h-4 w-4 mr-3 text-blue-500" />
                        Markera som utkast
                      </button>
                    )}
                    
                    {estimate.status !== 'pending' && (
                      <button
                        onClick={() => updateEstimateStatus(estimate.id, 'pending')}
                        className="flex items-center w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
                      >
                        <Clock className="h-4 w-4 mr-3 text-yellow-500" />
                        Markera som väntande
                      </button>
                    )}
                    
                    {estimate.status !== 'accepted' && (
                      <button
                        onClick={() => updateEstimateStatus(estimate.id, 'accepted')}
                        className="flex items-center w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
                      >
                        <Check className="h-4 w-4 mr-3 text-green-500" />
                        Markera som godkänd
                      </button>
                    )}
                    
                    {estimate.status !== 'rejected' && (
                      <button
                        onClick={() => updateEstimateStatus(estimate.id, 'rejected')}
                        className="flex items-center w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
                      >
                        <FileX className="h-4 w-4 mr-3 text-red-500" />
                        Markera som förlorad
                      </button>
                    )}
                    
                    {estimate.status === 'accepted' && (
                      <>
                        <div className="border-t border-gray-200 dark:border-gray-600 my-1"></div>
                        <div className="px-4 py-2 text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                          Åtgärder
                        </div>
                        <button
                          onClick={() => createInvoiceFromEstimate(estimate.id)}
                          className="flex items-center w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
                        >
                          <Receipt className="h-4 w-4 mr-3 text-blue-500" />
                          Skapa faktura
                        </button>
                      </>
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>
        );
      default:
        return null;
    }
  };

  const renderEstimateExpandedContent = (estimate: Estimate) => (
    <div className="flex flex-col md:flex-row gap-8">
      <div className="flex-1 space-y-3">
        <div className="text-lg font-medium text-gray-900 dark:text-white mb-4">Offertdetaljer</div>
        <div><span className="font-medium text-gray-700 dark:text-gray-300">Offertnummer:</span> <span className="text-gray-900 dark:text-white">{estimate.estimate_number || estimate.id}</span></div>
        <div className="flex items-center"><User className="w-4 h-4 mr-2 text-gray-500" /><span className="font-medium text-gray-700 dark:text-gray-300">Kund:</span> <span className="text-gray-900 dark:text-white ml-2">{estimate.customer_name}</span></div>
        {estimate.project_name && (
          <div><span className="font-medium text-gray-700 dark:text-gray-300">Projekt:</span> <span className="text-gray-900 dark:text-white">{estimate.project_name}</span></div>
        )}
        <div className="flex items-center"><Calendar className="w-4 h-4 mr-2 text-gray-500" /><span className="font-medium text-gray-700 dark:text-gray-300">Skapad:</span> <span className="text-gray-900 dark:text-white ml-2">{formatDate(estimate.created_at)}</span></div>
        <div className="flex items-center"><DollarSign className="w-4 h-4 mr-2 text-gray-500" /><span className="font-medium text-gray-700 dark:text-gray-300">Belopp:</span> <span className="text-gray-900 dark:text-white ml-2 font-medium">{formatCurrency(estimate.total_amount)}</span></div>
        <div><span className="font-medium text-gray-700 dark:text-gray-300">Status:</span> 
          <span className={`ml-2 px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(estimate.status)}`}>
            {getStatusDisplayName(estimate.status)}
          </span>
        </div>
      </div>
      <div className="flex-1">
        <div className="text-lg font-medium text-gray-900 dark:text-white mb-4">Snabbåtgärder</div>
        <div className="space-y-3">
          <Button 
            variant="outline" 
            className="w-full justify-start" 
            onClick={() => handleViewEstimate(estimate.id)}
          >
            <Eye className="w-4 h-4 mr-2" />
            Visa offert
          </Button>
          <Button variant="outline" className="w-full justify-start" onClick={() => handleDownloadEstimate(estimate.id)}>
            <Download className="w-4 h-4 mr-2" />
            Ladda ner PDF
          </Button>
          <Button 
            variant="outline" 
            className="w-full justify-start" 
            onClick={() => handleEditEstimate(estimate.id)}
          >
            <Edit className="w-4 h-4 mr-2" />
            Redigera offert
          </Button>
          {estimate.status === 'accepted' && (
            <Button variant="outline" className="w-full justify-start" onClick={() => createProjectFromEstimate(estimate.id)}>
              <FileText className="w-4 h-4 mr-2 text-green-600" />
              <span className="text-green-600">Skapa projekt</span>
            </Button>
          )}
          {estimate.status === 'accepted' && (
            <Button variant="outline" className="w-full justify-start" onClick={() => createInvoiceFromEstimate(estimate.id)}>
              <Receipt className="w-4 h-4 mr-2 text-blue-600" />
              <span className="text-blue-600">Skapa faktura</span>
            </Button>
          )}
        </div>
      </div>
    </div>
  );

  if (loading) {
    return (
      <div className="flex justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    )
  }

  if (estimates.length === 0) {
    return (
      <div className="text-center py-12 bg-white dark:bg-gray-800 rounded-lg shadow">
        <FileText className="h-12 w-12 mx-auto text-gray-400 mb-4" />
        <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">Inga offerter hittades</h3>
        <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
          Kom igång genom att skapa din första offert.
        </p>
        <Link href="/dashboard/estimates/create">
          <Button className="mt-4">
            <FileText className="h-4 w-4 mr-2" />
            Skapa ny offert
          </Button>
        </Link>
      </div>
    )
  }

  return (
    <>
      {/* Apple-style Filter Interface */}
      <div className="mb-6 bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
        {/* Filter Header */}
        <div className="px-6 py-4 border-b border-gray-100 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              {/* Search */}
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <input
                  type="text"
                  placeholder="Sök offerter..."
                  value={filters.search}
                  onChange={(e) => handleFilterChange('search', e.target.value)}
                  className="pl-10 pr-4 py-2 w-64 text-sm border border-gray-200 dark:border-gray-600 rounded-lg bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:bg-white dark:focus:bg-gray-600 focus:border-blue-500 dark:focus:border-blue-400 focus:ring-2 focus:ring-blue-500/20 outline-none transition-all duration-200"
                />
              </div>

              {/* Quick Status Filters */}
              <div className="flex items-center space-x-2">
                {[
                  { key: 'all', label: 'Alla' },
                  { key: 'draft', label: 'Utkast' },
                  { key: 'pending', label: 'Väntande' },
                  { key: 'accepted', label: 'Godkända' },
                  { key: 'rejected', label: 'Avvisade' }
                ].map((status) => {
                  const isActive = filters.status === status.key
                  let activeClasses = ''
                  
                  if (isActive) {
                    switch (status.key) {
                      case 'all':
                        activeClasses = 'bg-gray-100 text-gray-700 border-gray-300 dark:bg-gray-700 dark:text-gray-300 shadow-sm'
                        break
                      case 'draft':
                        activeClasses = 'bg-blue-100 text-blue-700 border-blue-300 dark:bg-blue-900 dark:text-blue-300 shadow-sm'
                        break
                      case 'pending':
                        activeClasses = 'bg-yellow-100 text-yellow-700 border-yellow-300 dark:bg-yellow-900 dark:text-yellow-300 shadow-sm'
                        break
                      case 'accepted':
                        activeClasses = 'bg-green-100 text-green-700 border-green-300 dark:bg-green-900 dark:text-green-300 shadow-sm'
                        break
                      case 'rejected':
                        activeClasses = 'bg-red-100 text-red-700 border-red-300 dark:bg-red-900 dark:text-red-300 shadow-sm'
                        break
                    }
                  }
                  
                  return (
                    <button
                      key={status.key}
                      onClick={() => handleFilterChange('status', status.key)}
                      className={`px-3 py-1.5 text-xs font-medium rounded-lg transition-all duration-200 border ${
                        isActive
                          ? activeClasses
                          : 'bg-gray-50 text-gray-600 hover:bg-gray-100 dark:bg-gray-700 dark:text-gray-400 dark:hover:bg-gray-600 border-transparent'
                      }`}
                    >
                      {status.label}
                    </button>
                  )
                })}
              </div>
            </div>

            {/* Advanced Filters Toggle */}
            <div className="flex items-center space-x-3">
              {hasActiveFilters && (
                <button
                  onClick={clearFilters}
                  className="text-sm text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 font-medium transition-colors duration-200"
                >
                  Rensa filter
                </button>
              )}
              
              <button
                onClick={() => setShowFilters(!showFilters)}
                className={`flex items-center space-x-2 px-3 py-2 text-sm font-medium rounded-lg transition-all duration-200 ${
                  showFilters || hasActiveFilters
                    ? 'bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-900/30 dark:text-blue-300 dark:border-blue-700'
                    : 'bg-gray-50 text-gray-600 hover:bg-gray-100 dark:bg-gray-700 dark:text-gray-400 dark:hover:bg-gray-600'
                } border`}
              >
                <Filter className="h-4 w-4" />
                <span>Filter</span>
                <ChevronDown className={`h-4 w-4 transition-transform duration-200 ${showFilters ? 'rotate-180' : ''}`} />
              </button>
            </div>
          </div>
        </div>

        {/* Advanced Filters Panel */}
        <div className={`transition-all duration-300 ease-in-out ${showFilters ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'} overflow-hidden`}>
          <div className="px-6 py-4 bg-gray-50 dark:bg-gray-700/30">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {/* Date Range */}
              <div>
                <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-2">Från datum</label>
                <input
                  type="date"
                  value={filters.dateFrom}
                  onChange={(e) => handleFilterChange('dateFrom', e.target.value)}
                  className="w-full px-3 py-2 text-sm border border-gray-200 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:border-blue-500 dark:focus:border-blue-400 focus:ring-2 focus:ring-blue-500/20 outline-none transition-all duration-200"
                />
              </div>

              <div>
                <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-2">Till datum</label>
                <input
                  type="date"
                  value={filters.dateTo}
                  onChange={(e) => handleFilterChange('dateTo', e.target.value)}
                  className="w-full px-3 py-2 text-sm border border-gray-200 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:border-blue-500 dark:focus:border-blue-400 focus:ring-2 focus:ring-blue-500/20 outline-none transition-all duration-200"
                />
              </div>

              {/* Amount Range */}
              <div>
                <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-2">Min belopp (SEK)</label>
                <input
                  type="number"
                  placeholder="0"
                  value={filters.amountMin}
                  onChange={(e) => handleFilterChange('amountMin', e.target.value)}
                  className="w-full px-3 py-2 text-sm border border-gray-200 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:border-blue-500 dark:focus:border-blue-400 focus:ring-2 focus:ring-blue-500/20 outline-none transition-all duration-200"
                />
              </div>

              <div>
                <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-2">Max belopp (SEK)</label>
                <input
                  type="number"
                  placeholder="999999"
                  value={filters.amountMax}
                  onChange={(e) => handleFilterChange('amountMax', e.target.value)}
                  className="w-full px-3 py-2 text-sm border border-gray-200 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:border-blue-500 dark:focus:border-blue-400 focus:ring-2 focus:ring-blue-500/20 outline-none transition-all duration-200"
                />
              </div>
            </div>
          </div>
        </div>

        {/* Results Summary */}
        <div className="px-6 py-3 bg-gray-50 dark:bg-gray-700/30 border-t border-gray-100 dark:border-gray-700">
          <div className="flex items-center justify-between text-sm">
            <span className="text-gray-600 dark:text-gray-400">
              Visar {filteredEstimates.length} av {estimates.length} offerter
            </span>
            {hasActiveFilters && (
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                <span className="text-blue-600 dark:text-blue-400 font-medium">Filter aktiva</span>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Empty state for filtered results */}
      {filteredEstimates.length === 0 && estimates.length > 0 ? (
        <div className="text-center py-12 bg-white dark:bg-gray-800 rounded-lg shadow">
          <Search className="h-12 w-12 mx-auto text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">Inga offerter matchar din sökning</h3>
          <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
            Försök justera dina filter eller rensa alla filter för att se fler resultat.
          </p>
          <Button 
            variant="outline" 
            onClick={clearFilters}
            className="mt-4"
          >
            <X className="h-4 w-4 mr-2" />
            Rensa alla filter
          </Button>
        </div>
      ) : (
        <ExpandableList
          data={filteredEstimates}
          columns={estimateColumns}
          renderCell={renderEstimateCell}
          renderExpandedContent={renderEstimateExpandedContent}
          getRowKey={(estimate) => estimate.id}
        />
      )}
      
      {/* Click outside to close dropdown */}
      {openDropdown && (
        <div 
          className="fixed inset-0 z-[90]" 
          onClick={() => setOpenDropdown(null)}
        />
      )}

      {/* PDF Preview Modal */}
      <PDFPreviewModal
        isOpen={showPDFPreview}
        onClose={() => {
          setShowPDFPreview(false);
          setPreviewEstimate(null);
        }}
        estimate={previewEstimate}
      />
    </>
  )
} 