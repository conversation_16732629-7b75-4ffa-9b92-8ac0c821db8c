"use client"

import { useState } from "react"
import { supabase } from "@/lib/supabase/supabase"

interface Customer {
  id: number
  name: string
  email: string
  phone?: string
  address?: string
}

interface LineItem {
  id?: number
  description: string
  quantity: number
  unit_price: number
  amount: number
}

interface EstimateFormProps {
  customers: Customer[]
  estimate?: {
    id: number
    estimate_number: string
    customer_id: number
    description: string
    valid_until: string
    vat_rate: number
    line_items: LineItem[]
  }
  onSubmit: (data: any) => void
  isLoading?: boolean
}

export function EstimateForm({ customers, estimate, onSubmit, isLoading }: EstimateFormProps) {
  const [formData, setFormData] = useState({
    customer_id: estimate?.customer_id || "",
    description: estimate?.description || "",
    valid_until: estimate?.valid_until ? estimate.valid_until.split('T')[0] : "",
    vat_rate: estimate?.vat_rate || 25,
  })

  const [lineItems, setLineItems] = useState<LineItem[]>(
    estimate?.line_items || [{ description: "", quantity: 1, unit_price: 0, amount: 0 }]
  )

  const updateLineItem = (index: number, field: keyof LineItem, value: string | number) => {
    const newItems = [...lineItems]
    newItems[index] = { ...newItems[index], [field]: value }
    
    // Recalculate amount if quantity or unit_price changed
    if (field === 'quantity' || field === 'unit_price') {
      newItems[index].amount = newItems[index].quantity * newItems[index].unit_price
    }
    
    setLineItems(newItems)
  }

  const addLineItem = () => {
    setLineItems([...lineItems, { description: "", quantity: 1, unit_price: 0, amount: 0 }])
  }

  const removeLineItem = (index: number) => {
    if (lineItems.length > 1) {
      setLineItems(lineItems.filter((_, i) => i !== index))
    }
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    
    const subtotal = lineItems.reduce((sum, item) => sum + item.amount, 0)
    const vatAmount = subtotal * (formData.vat_rate / 100)
    const totalAmount = subtotal + vatAmount

    onSubmit({
      ...formData,
      line_items: lineItems.filter(item => item.description.trim() !== ""),
      subtotal,
      vat_amount: vatAmount,
      total_amount: totalAmount,
    })
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Customer Selection */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Kund
        </label>
        <select
          value={formData.customer_id}
          onChange={(e) => setFormData({ ...formData, customer_id: e.target.value })}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          required
        >
          <option value="">Välj kund</option>
          {customers.map((customer) => (
            <option key={customer.id} value={customer.id}>
              {customer.name}
            </option>
          ))}
        </select>
      </div>

      {/* Description */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Beskrivning
        </label>
        <textarea
          value={formData.description}
          onChange={(e) => setFormData({ ...formData, description: e.target.value })}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          rows={3}
          required
        />
      </div>

      {/* Valid Until */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Giltig till
        </label>
        <input
          type="date"
          value={formData.valid_until}
          onChange={(e) => setFormData({ ...formData, valid_until: e.target.value })}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          required
        />
      </div>

      {/* VAT Rate */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Moms (%)
        </label>
        <input
          type="number"
          value={formData.vat_rate}
          onChange={(e) => setFormData({ ...formData, vat_rate: Number(e.target.value) })}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          min="0"
          max="100"
          required
        />
      </div>

      {/* Line Items */}
      <div>
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-medium text-gray-900">Rader</h3>
          <button
            type="button"
            onClick={addLineItem}
            className="bg-blue-500 text-white px-4 py-2 rounded-md hover:bg-blue-600 transition-colors"
          >
            Lägg till rad
          </button>
        </div>

        <div className="space-y-4">
          {lineItems.map((item, index) => (
            <div key={index} className="grid grid-cols-12 gap-4 items-end">
              <div className="col-span-5">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Beskrivning
                </label>
                <input
                  type="text"
                  value={item.description}
                  onChange={(e) => updateLineItem(index, 'description', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  required
                />
              </div>
              <div className="col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Antal
                </label>
                <input
                  type="number"
                  value={item.quantity}
                  onChange={(e) => updateLineItem(index, 'quantity', Number(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  min="0"
                  step="0.01"
                  required
                />
              </div>
              <div className="col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Enhetspris
                </label>
                <input
                  type="number"
                  value={item.unit_price}
                  onChange={(e) => updateLineItem(index, 'unit_price', Number(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  min="0"
                  step="0.01"
                  required
                />
              </div>
              <div className="col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Belopp
                </label>
                <input
                  type="number"
                  value={item.amount}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50"
                  readOnly
                />
              </div>
              <div className="col-span-1">
                {lineItems.length > 1 && (
                  <button
                    type="button"
                    onClick={() => removeLineItem(index)}
                    className="w-full bg-red-500 text-white px-3 py-2 rounded-md hover:bg-red-600 transition-colors"
                  >
                    ✕
                  </button>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Submit Button */}
      <div className="flex justify-end">
        <button
          type="submit"
          disabled={isLoading}
          className="bg-blue-500 text-white px-6 py-2 rounded-md hover:bg-blue-600 transition-colors disabled:opacity-50"
        >
          {isLoading ? "Sparar..." : estimate ? "Uppdatera offert" : "Skapa offert"}
        </button>
      </div>
    </form>
  )
} 