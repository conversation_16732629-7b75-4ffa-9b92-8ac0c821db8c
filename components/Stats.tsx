import React from 'react';
import { BadgeDelta } from './ui/badge-delta';

export default function Stats() {
  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
      <div className="flex justify-between items-center mb-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Sales Metrics</h3>
        <select className="text-sm border border-gray-300 dark:border-gray-600 rounded-md px-3 py-1.5 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500">
          <option>This Month</option>
          <option>This Quarter</option>
          <option>This Year</option>
        </select>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <div className="flex flex-col space-y-2 p-4 bg-gray-50 dark:bg-gray-900/50 rounded-lg">
          <div className="flex justify-between items-center">
            <p className="text-sm font-medium text-gray-700 dark:text-gray-300">Aktiva prospekt</p>
          </div>
          <div className="flex items-baseline justify-between">
            <span className="text-2xl font-bold text-gray-900 dark:text-white">24</span>
            <BadgeDelta 
              variant="solid"
              deltaType="increase"
              iconStyle="line"
              value="8%"
            />
          </div>
          <p className="text-xs text-gray-500 dark:text-gray-400">5 nya prospekt denna månad</p>
        </div>
        
        <div className="flex flex-col space-y-2 p-4 bg-gray-50 dark:bg-gray-900/50 rounded-lg">
          <div className="flex justify-between items-center">
            <p className="text-sm font-medium text-gray-700 dark:text-gray-300">Offertvärde</p>
          </div>
          <div className="flex items-baseline justify-between">
            <span className="text-2xl font-bold text-gray-900 dark:text-white">732 000 kr</span>
            <BadgeDelta 
              variant="solid"
              deltaType="increase"
              iconStyle="line"
              value="12%"
            />
          </div>
          <p className="text-xs text-gray-500 dark:text-gray-400">9 godkända offerter</p>
        </div>
        
        <div className="flex flex-col space-y-2 p-4 bg-gray-50 dark:bg-gray-900/50 rounded-lg">
          <div className="flex justify-between items-center">
            <p className="text-sm font-medium text-gray-700 dark:text-gray-300">Väntande offerter</p>
          </div>
          <div className="flex items-baseline justify-between">
            <span className="text-2xl font-bold text-gray-900 dark:text-white">121 000 kr</span>
            <BadgeDelta 
              variant="solid"
              deltaType="increase"
              iconStyle="line"
              value="5%"
            />
          </div>
          <p className="text-xs text-gray-500 dark:text-gray-400">2 väntande offerter</p>
        </div>
        
        <div className="flex flex-col space-y-2 p-4 bg-gray-50 dark:bg-gray-900/50 rounded-lg">
          <div className="flex justify-between items-center">
            <p className="text-sm font-medium text-gray-700 dark:text-gray-300">Konverteringsgrad</p>
          </div>
          <div className="flex items-baseline justify-between">
            <span className="text-2xl font-bold text-gray-900 dark:text-white">68%</span>
            <BadgeDelta 
              variant="solid"
              deltaType="increase"
              iconStyle="line"
              value="7%"
            />
          </div>
          <p className="text-xs text-gray-500 dark:text-gray-400">15 av 22 prospekt konverterade</p>
        </div>
      </div>
    </div>
  );
} 