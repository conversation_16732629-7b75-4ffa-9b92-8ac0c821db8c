"use client"

import React, { useEffect, useState } from 'react';
import { supabase } from '@/lib/supabase/supabase';
import { Briefcase, Clock, DollarSign, TrendingUp, Users, Calendar, ArrowUpRight, MoreVertical } from 'lucide-react';
import { GoogleCard, GoogleCardContent, GoogleCardHeader, GoogleCardTitle } from '@/components/ui/google-components';
import Link from 'next/link';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';

interface ProjectStats {
  totalProjects: number;
  activeProjects: number;
  completedProjects: number;
  totalHours: number;
  totalBudget: number;
  averageProgress: number;
}

export default function ProjectsOverviewDashboard() {
  const [stats, setStats] = useState<ProjectStats>({
    totalProjects: 0,
    activeProjects: 0,
    completedProjects: 0,
    totalHours: 0,
    totalBudget: 0,
    averageProgress: 0
  });
  const [loading, setLoading] = useState(true);
  const [recentProjects, setRecentProjects] = useState<any[]>([]);

  useEffect(() => {
    fetchOverviewData();
  }, []);

  const fetchOverviewData = async () => {
    try {
      setLoading(true);

      // Fetch all projects with task progress
      const { data: projects, error: projectsError } = await supabase
        .from('projects')
        .select('*');

      if (projectsError) throw projectsError;

      // Fetch project tasks to calculate real progress
      const { data: tasks, error: tasksError } = await supabase
        .from('project_tasks')
        .select('project_id, status');

      if (tasksError) throw tasksError;

      // Calculate progress for each project
      const projectsWithProgress = projects?.map(project => {
        const projectTasks = tasks?.filter(task => task.project_id === project.id) || [];
        const completedTasks = projectTasks.filter(task => task.status === 'completed').length;
        const totalTasks = projectTasks.length;
        const progress = totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0;
        
        return {
          ...project,
          progress,
          completed_tasks: completedTasks,
          total_tasks: totalTasks
        };
      }) || [];

      // Calculate stats
      const totalProjects = projectsWithProgress.length;
      const activeProjects = projectsWithProgress.filter(p => p.status === 'active').length;
      const completedProjects = projectsWithProgress.filter(p => p.status === 'completed').length;
      const totalHours = projectsWithProgress.reduce((sum, p) => sum + (p.estimated_hours || 0), 0);
      const totalBudget = projectsWithProgress.reduce((sum, p) => sum + (p.budget || 0), 0);
      const averageProgress = projectsWithProgress.length > 0 
        ? Math.round(projectsWithProgress.reduce((sum, p) => sum + p.progress, 0) / projectsWithProgress.length)
        : 0;

      // Get recent projects (last 5)
      const recent = projectsWithProgress.slice(-5).reverse();

      setStats({
        totalProjects,
        activeProjects,
        completedProjects,
        totalHours,
        totalBudget,
        averageProgress
      });

      setRecentProjects(recent);
    } catch (error) {
      console.error('Error fetching overview data:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center py-16">
        <div className="animate-spin rounded-full h-8 w-8 border-2 border-blue-600 border-t-transparent"></div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-normal text-gray-900">Project Overview</h1>
          <p className="text-sm text-gray-600 mt-1">Track your project portfolio performance</p>
        </div>
      </div>

      {/* Stats Grid - Fakturor Style */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
        {/* Total Projects */}
        <div className="shadow-sm border-0 bg-white dark:bg-gray-800 rounded-xl overflow-hidden hover:shadow-md transition-shadow duration-300">
          <div className="p-5">
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center">
                <div className="w-10 h-10 rounded-full bg-gray-50 dark:bg-gray-700 flex items-center justify-center mr-3">
                  <Briefcase className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                </div>
                <div>
                  <div className="text-xs font-medium text-gray-500 dark:text-gray-400">Total projects</div>
                  <div className="text-lg font-normal text-gray-900 dark:text-white">
                    {stats.totalProjects}
                  </div>
                </div>
              </div>
            </div>
            <div className="flex items-center">
              <span className="text-xs font-medium flex items-center text-blue-600 dark:text-blue-400">
                <Briefcase className="w-3 h-3 mr-1" />
                Alla projekt
              </span>
              <span className="text-xs text-gray-500 dark:text-gray-400 ml-2">i systemet</span>
            </div>
          </div>
        </div>

        {/* Active Projects */}
        <div className="shadow-sm border-0 bg-white dark:bg-gray-800 rounded-xl overflow-hidden hover:shadow-md transition-shadow duration-300">
          <div className="p-5">
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center">
                <div className="w-10 h-10 rounded-full bg-gray-50 dark:bg-gray-700 flex items-center justify-center mr-3">
                  <TrendingUp className="w-5 h-5 text-green-600 dark:text-green-400" />
                </div>
                <div>
                  <div className="text-xs font-medium text-gray-500 dark:text-gray-400">Active projects</div>
                  <div className="text-lg font-normal text-gray-900 dark:text-white">
                    {stats.activeProjects}
                  </div>
                </div>
              </div>
            </div>
            <div className="flex items-center">
              <span className="text-xs font-medium flex items-center text-green-600 dark:text-green-400">
                <TrendingUp className="w-3 h-3 mr-1" />
                Pågående
              </span>
              <span className="text-xs text-gray-500 dark:text-gray-400 ml-2">just nu</span>
            </div>
          </div>
        </div>

        {/* Completed Projects */}
        <div className="shadow-sm border-0 bg-white dark:bg-gray-800 rounded-xl overflow-hidden hover:shadow-md transition-shadow duration-300">
          <div className="p-5">
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center">
                <div className="w-10 h-10 rounded-full bg-gray-50 dark:bg-gray-700 flex items-center justify-center mr-3">
                  <Calendar className="w-5 h-5 text-purple-600 dark:text-purple-400" />
                </div>
                <div>
                  <div className="text-xs font-medium text-gray-500 dark:text-gray-400">Completed</div>
                  <div className="text-lg font-normal text-gray-900 dark:text-white">
                    {stats.completedProjects}
                  </div>
                </div>
              </div>
            </div>
            <div className="flex items-center">
              <span className="text-xs font-medium flex items-center text-purple-600 dark:text-purple-400">
                <Calendar className="w-3 h-3 mr-1" />
                Avslutade
              </span>
              <span className="text-xs text-gray-500 dark:text-gray-400 ml-2">projekt</span>
            </div>
          </div>
        </div>

        {/* Total Hours */}
        <div className="shadow-sm border-0 bg-white dark:bg-gray-800 rounded-xl overflow-hidden hover:shadow-md transition-shadow duration-300">
          <div className="p-5">
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center">
                <div className="w-10 h-10 rounded-full bg-gray-50 dark:bg-gray-700 flex items-center justify-center mr-3">
                  <Clock className="w-5 h-5 text-amber-600 dark:text-amber-400" />
                </div>
                <div>
                  <div className="text-xs font-medium text-gray-500 dark:text-gray-400">Total hours</div>
                  <div className="text-lg font-normal text-gray-900 dark:text-white">
                    {stats.totalHours.toLocaleString()}
                  </div>
                </div>
              </div>
            </div>
            <div className="flex items-center">
              <span className="text-xs font-medium flex items-center text-amber-600 dark:text-amber-400">
                <Clock className="w-3 h-3 mr-1" />
                Uppskattade
              </span>
              <span className="text-xs text-gray-500 dark:text-gray-400 ml-2">timmar</span>
            </div>
          </div>
        </div>

        {/* Total Budget */}
        <div className="shadow-sm border-0 bg-white dark:bg-gray-800 rounded-xl overflow-hidden hover:shadow-md transition-shadow duration-300">
          <div className="p-5">
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center">
                <div className="w-10 h-10 rounded-full bg-gray-50 dark:bg-gray-700 flex items-center justify-center mr-3">
                  <DollarSign className="w-5 h-5 text-emerald-600 dark:text-emerald-400" />
                </div>
                <div>
                  <div className="text-xs font-medium text-gray-500 dark:text-gray-400">Total budget</div>
                  <div className="text-lg font-normal text-gray-900 dark:text-white">
                    {stats.totalBudget.toLocaleString('sv-SE')} SEK
                  </div>
                </div>
              </div>
            </div>
            <div className="flex items-center">
              <span className="text-xs font-medium flex items-center text-emerald-600 dark:text-emerald-400">
                <DollarSign className="w-3 h-3 mr-1" />
                Total budget
              </span>
              <span className="text-xs text-gray-500 dark:text-gray-400 ml-2">SEK</span>
            </div>
          </div>
        </div>
      </div>

      {/* Recent Projects - Google Table Style */}
      <GoogleCard className="border-0 bg-white">
        <GoogleCardHeader className="px-6 py-4 border-b border-gray-100">
          <div className="flex items-center justify-between">
            <GoogleCardTitle className="text-base font-medium text-gray-900">Recent projects</GoogleCardTitle>
            <Link
              href="/dashboard/projects"
              className="text-sm text-blue-600 hover:text-blue-700 font-medium flex items-center space-x-1 hover:underline"
            >
              <span>View all</span>
              <ArrowUpRight className="h-4 w-4" />
            </Link>
          </div>
        </GoogleCardHeader>
        <GoogleCardContent className="p-0">
          {recentProjects.length > 0 ? (
            <div className="divide-y divide-gray-50">
              {recentProjects.map((project, index) => (
                <div key={project.id} className="group hover:bg-gray-50 transition-colors">
                  <div className="px-6 py-4">
                    <div className="flex items-center justify-between">
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-3">
                          <div className="flex-shrink-0">
                            <div className="w-8 h-8 rounded-full bg-blue-50 flex items-center justify-center">
                              <Briefcase className="h-4 w-4 text-blue-600" />
                            </div>
                          </div>
                          <div className="flex-1 min-w-0">
                            <Link
                              href={`/dashboard/projects/${project.id}`}
                              className="text-sm font-medium text-gray-900 hover:text-blue-600 transition-colors truncate block"
                            >
                              {project.name}
                            </Link>
                            <p className="text-xs text-gray-500 truncate mt-0.5">
                              {project.description || 'No description'}
                            </p>
                          </div>
                        </div>
                      </div>

                      <div className="flex items-center space-x-4 ml-4">
                        {/* Progress */}
                        <div className="flex items-center space-x-2">
                          <div className="w-16 h-1.5 bg-gray-100 rounded-full overflow-hidden">
                            <div
                              className={`h-full rounded-full transition-all duration-300 ${
                                project.progress >= 80 ? 'bg-green-500' :
                                project.progress >= 50 ? 'bg-blue-500' :
                                project.progress >= 25 ? 'bg-amber-500' :
                                'bg-gray-300'
                              }`}
                              style={{ width: `${project.progress || 0}%` }}
                            />
                          </div>
                          <span className="text-xs text-gray-500 w-8 text-right">
                            {project.progress || 0}%
                          </span>
                        </div>

                        {/* Status */}
                        <div className="flex-shrink-0">
                          <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                            project.status === 'active' ? 'bg-green-50 text-green-700 border border-green-200' :
                            project.status === 'completed' ? 'bg-purple-50 text-purple-700 border border-purple-200' :
                            project.status === 'on-hold' ? 'bg-amber-50 text-amber-700 border border-amber-200' :
                            project.status === 'planning' ? 'bg-blue-50 text-blue-700 border border-blue-200' :
                            'bg-gray-50 text-gray-700 border border-gray-200'
                          }`}>
                            {project.status || 'pending'}
                          </span>
                        </div>

                        {/* More menu */}
                        <button className="opacity-0 group-hover:opacity-100 transition-opacity p-1 hover:bg-gray-100 rounded">
                          <MoreVertical className="h-4 w-4 text-gray-400" />
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="px-6 py-12 text-center">
              <Briefcase className="h-8 w-8 mx-auto text-gray-300 mb-3" />
              <p className="text-sm text-gray-500">No recent projects</p>
            </div>
          )}
        </GoogleCardContent>
      </GoogleCard>
    </div>
  );
}
