import React from 'react';
import { PlusCircle, User, Calendar, DollarSign, Percent } from 'lucide-react';
import { Google<PERSON>ard, GoogleCardHeader, GoogleCardTitle, GoogleCardContent, GoogleButton } from '@/components/ui/google-components';
import { formatDate } from '@/lib/utils';
import Link from 'next/link';

interface UserProfile {
  id: string;
  first_name?: string;
  last_name?: string;
  email?: string;
  full_name?: string;
}

interface ProjectResource {
  id: string;
  project_id: string;
  user_id: string;
  role?: string;
  allocation_percentage?: number;
  hourly_rate?: number;
  start_date?: string;
  end_date?: string;
  notes?: string;
  profile?: UserProfile;
}

interface ProjectResourcesTabProps {
  projectId: string;
  resources: ProjectResource[];
}

export function ProjectResourcesTab({
  projectId,
  resources
}: ProjectResourcesTabProps) {
  
  const getInitials = (profile?: UserProfile) => {
    if (!profile) return 'U';
    
    if (profile.first_name && profile.last_name) {
      return `${profile.first_name.charAt(0)}${profile.last_name.charAt(0)}`.toUpperCase();
    }
    
    if (profile.full_name) {
      const parts = profile.full_name.split(' ');
      if (parts.length >= 2) {
        return `${parts[0].charAt(0)}${parts[parts.length - 1].charAt(0)}`.toUpperCase();
      } else if (parts.length === 1) {
        return parts[0].charAt(0).toUpperCase();
      }
    }
    
    return profile.email?.charAt(0).toUpperCase() || 'U';
  };
  
  const getRandomColor = (userId: string) => {
    const colors = [
      'bg-blue-100 text-blue-700',
      'bg-green-100 text-green-700',
      'bg-purple-100 text-purple-700',
      'bg-amber-100 text-amber-700',
      'bg-pink-100 text-pink-700',
      'bg-cyan-100 text-cyan-700',
    ];
    
    // Simple hash function to get deterministic color based on user ID
    const hash = userId.split('').reduce((acc, char) => {
      return acc + char.charCodeAt(0);
    }, 0);
    
    return colors[hash % colors.length];
  };
  
  const formatCurrency = (amount?: number) => {
    if (amount === undefined || amount === null) return '-';
    return new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(amount);
  };
  
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-medium text-gray-900">Project Team</h2>
        <Link href={`/dashboard/projects/${projectId}/resources/add`}>
          <GoogleButton>
            <PlusCircle className="h-4 w-4 mr-1" /> Add Resource
          </GoogleButton>
        </Link>
      </div>
      
      {resources.length === 0 ? (
        <GoogleCard>
          <GoogleCardContent className="text-center py-10">
            <User className="h-12 w-12 mx-auto text-gray-300 mb-3" />
            <h3 className="text-lg font-medium text-gray-700 mb-2">No Team Members Yet</h3>
            <p className="text-gray-500 mb-4">Add team members to your project to get started.</p>
            <Link href={`/dashboard/projects/${projectId}/resources/add`}>
              <GoogleButton>
                <PlusCircle className="h-4 w-4 mr-1" /> Add Team Member
              </GoogleButton>
            </Link>
          </GoogleCardContent>
        </GoogleCard>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {resources.map((resource) => (
            <GoogleCard key={resource.id}>
              <GoogleCardContent className="p-0">
                <div className="p-4 flex items-center border-b border-gray-100">
                  <div className={`w-10 h-10 rounded-full flex items-center justify-center font-medium ${getRandomColor(resource.user_id)}`}>
                    {resource.profile ? getInitials(resource.profile) : 'U'}
                  </div>
                  <div className="ml-3">
                    <h3 className="font-medium text-gray-900">
                      {resource.profile?.full_name || resource.profile?.email || 'Unnamed User'}
                    </h3>
                    <p className="text-sm text-gray-500">{resource.role || 'Team Member'}</p>
                  </div>
                </div>
                
                <div className="p-4 space-y-3">
                  {resource.allocation_percentage !== undefined && resource.allocation_percentage !== null && (
                    <div className="flex items-center justify-between text-sm">
                      <span className="flex items-center text-gray-500">
                        <Percent className="h-4 w-4 mr-1.5" />
                        Allocation
                      </span>
                      <span className="font-medium text-gray-700">{resource.allocation_percentage}%</span>
                    </div>
                  )}
                  
                  {resource.hourly_rate !== undefined && resource.hourly_rate !== null && (
                    <div className="flex items-center justify-between text-sm">
                      <span className="flex items-center text-gray-500">
                        <DollarSign className="h-4 w-4 mr-1.5" />
                        Hourly Rate
                      </span>
                      <span className="font-medium text-gray-700">{formatCurrency(resource.hourly_rate)}</span>
                    </div>
                  )}
                  
                  {(resource.start_date || resource.end_date) && (
                    <div className="flex items-center justify-between text-sm">
                      <span className="flex items-center text-gray-500">
                        <Calendar className="h-4 w-4 mr-1.5" />
                        Period
                      </span>
                      <span className="font-medium text-gray-700">
                        {resource.start_date ? formatDate(resource.start_date) : 'Start'} 
                        {' - '}
                        {resource.end_date ? formatDate(resource.end_date) : 'End'}
                      </span>
                    </div>
                  )}
                </div>
                
                {resource.notes && (
                  <div className="p-4 pt-0">
                    <p className="text-sm text-gray-600">
                      <span className="font-medium text-gray-700 block mb-1">Notes:</span>
                      {resource.notes}
                    </p>
                  </div>
                )}
                
                <div className="p-4 pt-0 flex justify-end">
                  <Link 
                    href={`/dashboard/projects/${projectId}/staff/edit?id=${resource.id}`}
                    className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                  >
                    Edit Resource
                  </Link>
                </div>
              </GoogleCardContent>
            </GoogleCard>
          ))}
        </div>
      )}
    </div>
  );
}

export default ProjectResourcesTab; 