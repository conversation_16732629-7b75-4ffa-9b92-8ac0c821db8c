'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { supabase } from '@/lib/supabase/supabase';
import { Customer } from '@/lib/supabase/supabase';
import { ensureProjectTablesExist } from '@/lib/supabase/create-tables';
import { useAuth } from '@/app/(auth)/auth/auth-provider';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { CheckCircle, Plus, User, Calendar, Clock, DollarSign, FileText, Building, Hammer, MapPin, Users } from 'lucide-react';
import { Calendar as CalendarComponent } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { format } from 'date-fns';

export default function ProjectCreateForm() {
  const router = useRouter();
  const { session, user, isLoading: authLoading } = useAuth();
  
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [customers, setCustomers] = useState<Customer[]>([]);
  
  // Form state
  const [name, setName] = useState('');
  const [description, setDescription] = useState('');
  const [customerId, setCustomerId] = useState('');
  const [projectType, setProjectType] = useState('');
  const [location, setLocation] = useState('');
  const [status, setStatus] = useState('planning');
  const [priority, setPriority] = useState('medium');
  const [startDate, setStartDate] = useState<Date | null>(null);
  const [endDate, setEndDate] = useState<Date | null>(null);
  const [budget, setBudget] = useState('');
  const [estimatedHours, setEstimatedHours] = useState('');
  const [teamSize, setTeamSize] = useState('');
  const [notes, setNotes] = useState('');

  useEffect(() => {
    async function fetchCustomers() {
      try {
        const { data, error } = await supabase
          .from('customers')
          .select('*')
          .order('name');
        
        if (error) throw error;
        setCustomers(data || []);
      } catch (error) {
        console.error('Error fetching customers:', error);
      }
    }
    
    fetchCustomers();
  }, []);

  const resetForm = () => {
    setName('');
    setDescription('');
    setCustomerId('');
    setProjectType('');
    setLocation('');
    setStatus('planning');
    setPriority('medium');
    setStartDate(null);
    setEndDate(null);
    setBudget('');
    setEstimatedHours('');
    setTeamSize('');
    setNotes('');
    setError(null);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!session || !user) {
      setError('You must be logged in to create a project');
      return;
    }
    
    if (!name.trim()) {
      setError('Project name is required');
      return;
    }
    
    setLoading(true);
    setError(null);
    
    try {
      // Ensure tables exist
      await ensureProjectTablesExist();
      
      const projectData = {
        name: name.trim(),
        description: description.trim() || null,
        customer_id: customerId || null,
        status,
        priority,
        start_date: startDate?.toISOString().split('T')[0] || null,
        end_date: endDate?.toISOString().split('T')[0] || null,
        budget: budget ? parseFloat(budget) : null,
        estimated_hours: estimatedHours ? parseFloat(estimatedHours) : null,
        notes: notes.trim() || null,
        created_by: user.id,
        updated_by: user.id
      };
      
      const { data, error } = await supabase
        .from('projects')
        .insert([projectData])
        .select()
        .single();
      
      if (error) throw error;
      
      setSuccess(true);
      resetForm();
      
      // Show success for a moment, then redirect
      setTimeout(() => {
        router.push(`/dashboard/projects/${data.id}`);
      }, 1500);
      
    } catch (error: any) {
      console.error('Error creating project:', error);
      setError(error.message || 'Failed to create project');
    } finally {
      setLoading(false);
    }
  };

  // Show loading while auth is initializing
  if (authLoading) {
    return (
      <div className="flex items-center justify-center min-h-[500px]">
        <div className="text-center">
          <div className="w-5 h-5 border-2 border-gray-300 border-t-gray-600 rounded-full animate-spin mx-auto mb-3"></div>
          <p className="text-gray-500 text-sm font-medium">Loading...</p>
        </div>
      </div>
    );
  }

  // Show message if not authenticated
  if (!session || !user) {
    return (
      <div className="flex items-center justify-center min-h-[500px]">
        <div className="bg-white rounded-2xl shadow-sm border border-gray-200 p-8 max-w-sm text-center">
          <div className="w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <User className="w-6 h-6 text-gray-400" />
          </div>
          <h3 className="font-semibold text-gray-900 mb-2">Sign in required</h3>
          <p className="text-gray-500 text-sm">Please sign in to create a new construction project.</p>
        </div>
      </div>
    );
  }

  if (success) {
    return (
      <div className="flex items-center justify-center min-h-[500px]">
        <div className="text-center max-w-sm">
          <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <CheckCircle className="w-8 h-8 text-green-600" />
          </div>
          <h3 className="text-xl font-semibold text-gray-900 mb-2">Project created</h3>
          <p className="text-gray-500 mb-4">Your construction project has been successfully created.</p>
          <div className="text-blue-600 text-sm font-medium">Redirecting...</div>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto p-6 bg-gray-50">
      {/* Header */}
      <div className="mb-8 text-center">
        <div className="inline-flex items-center justify-center w-14 h-14 bg-blue-100 rounded-2xl mb-4">
          <Building className="w-7 h-7 text-blue-600" />
        </div>
        <h1 className="text-2xl font-semibold text-gray-900 mb-2">New Construction Project</h1>
        <p className="text-gray-500">Create a comprehensive construction project with scheduling, budgeting, and task management.</p>
      </div>

      {/* Error Message */}
      {error && (
        <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-xl">
          <div className="flex items-center">
            <div className="w-4 h-4 bg-red-100 rounded-full flex items-center justify-center mr-3">
              <span className="text-red-600 text-xs font-bold">!</span>
            </div>
            <span className="text-red-800 text-sm font-medium">{error}</span>
          </div>
        </div>
      )}

      {/* Form */}
      <form onSubmit={handleSubmit} className="space-y-6 pb-8">
        {/* Project Overview */}
        <div className="bg-white rounded-2xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center mb-6">
            <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
              <FileText className="w-4 h-4 text-blue-600" />
            </div>
            <h2 className="text-lg font-semibold text-gray-900">Project Overview</h2>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Project Name */}
            <div className="md:col-span-2">
              <Label htmlFor="name" className="text-sm font-medium text-gray-700 mb-2 block">
                Project Name
              </Label>
              <Input 
                id="name"
                value={name}
                onChange={(e) => setName(e.target.value)}
                placeholder="e.g., Downtown Office Renovation"
                required
                className="h-11 px-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 text-gray-900 placeholder-gray-400"
              />
            </div>

            {/* Customer */}
            <div>
              <Label htmlFor="customer" className="text-sm font-medium text-gray-700 mb-2 block">
                Client
              </Label>
              <Select value={customerId} onValueChange={setCustomerId}>
                <SelectTrigger className="h-11 px-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-white text-gray-900">
                  <SelectValue placeholder="Select client" className="text-gray-900" />
                </SelectTrigger>
                <SelectContent className="rounded-lg border border-gray-200 shadow-lg bg-white z-50">
                  {customers.map((customer) => (
                    <SelectItem 
                      key={customer.id} 
                      value={customer.id} 
                      className="px-4 py-3 text-gray-900 hover:bg-gray-50 focus:bg-blue-50 focus:text-blue-900 cursor-pointer transition-colors duration-150"
                    >
                      {customer.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Project Type */}
            <div>
              <Label htmlFor="projectType" className="text-sm font-medium text-gray-700 mb-2 block">
                Project Type
              </Label>
              <Select value={projectType} onValueChange={setProjectType}>
                <SelectTrigger className="h-11 px-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-white text-gray-900">
                  <SelectValue placeholder="Select project type" className="text-gray-900" />
                </SelectTrigger>
                <SelectContent className="rounded-lg border border-gray-200 shadow-lg bg-white z-50">
                  <SelectItem value="residential" className="px-4 py-3 text-gray-900 hover:bg-gray-50 focus:bg-blue-50 focus:text-blue-900 cursor-pointer transition-colors duration-150">Residential</SelectItem>
                  <SelectItem value="commercial" className="px-4 py-3 text-gray-900 hover:bg-gray-50 focus:bg-blue-50 focus:text-blue-900 cursor-pointer transition-colors duration-150">Commercial</SelectItem>
                  <SelectItem value="renovation" className="px-4 py-3 text-gray-900 hover:bg-gray-50 focus:bg-blue-50 focus:text-blue-900 cursor-pointer transition-colors duration-150">Renovation</SelectItem>
                  <SelectItem value="new-construction" className="px-4 py-3 text-gray-900 hover:bg-gray-50 focus:bg-blue-50 focus:text-blue-900 cursor-pointer transition-colors duration-150">New Construction</SelectItem>
                  <SelectItem value="infrastructure" className="px-4 py-3 text-gray-900 hover:bg-gray-50 focus:bg-blue-50 focus:text-blue-900 cursor-pointer transition-colors duration-150">Infrastructure</SelectItem>
                </SelectContent>
              </Select>
            </div>
            {/* Location */}
            <div className="md:col-span-2">
              <Label htmlFor="location" className="text-sm font-medium text-gray-700 mb-2 block">
                Project Location
              </Label>
              <div className="relative">
                <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                <Input
                  id="location"
                  value={location}
                  onChange={(e) => setLocation(e.target.value)}
                  placeholder="e.g., 123 Main St, Downtown District"
                  className="h-11 pl-10 pr-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 text-gray-900 placeholder-gray-400"
                />
              </div>
            </div>

            {/* Description */}
            <div className="md:col-span-2">
              <Label htmlFor="description" className="text-sm font-medium text-gray-700 mb-2 block">
                Project Description
              </Label>
              <Textarea
                id="description"
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                placeholder="Describe the scope of work, key deliverables, and project objectives..."
                rows={3}
                className="px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 resize-none text-gray-900 placeholder-gray-400"
              />
            </div>
          </div>
        </div>

        {/* Project Management */}
        <div className="bg-white rounded-2xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center mb-6">
            <div className="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center mr-3">
              <Hammer className="w-4 h-4 text-orange-600" />
            </div>
            <h2 className="text-lg font-semibold text-gray-900">Project Management</h2>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* Status */}
            <div>
              <Label htmlFor="status" className="text-sm font-medium text-gray-700 mb-2 block">
                Status
              </Label>
              <Select value={status} onValueChange={setStatus}>
                <SelectTrigger className="h-11 px-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-white text-gray-900">
                  <SelectValue className="text-gray-900" />
                </SelectTrigger>
                <SelectContent className="rounded-lg border border-gray-200 shadow-lg bg-white z-50">
                  <SelectItem value="planning" className="px-4 py-3 text-gray-900 hover:bg-gray-50 focus:bg-blue-50 focus:text-blue-900 cursor-pointer transition-colors duration-150">Planning</SelectItem>
                  <SelectItem value="active" className="px-4 py-3 text-gray-900 hover:bg-gray-50 focus:bg-blue-50 focus:text-blue-900 cursor-pointer transition-colors duration-150">Active</SelectItem>
                  <SelectItem value="on-hold" className="px-4 py-3 text-gray-900 hover:bg-gray-50 focus:bg-blue-50 focus:text-blue-900 cursor-pointer transition-colors duration-150">On Hold</SelectItem>
                  <SelectItem value="completed" className="px-4 py-3 text-gray-900 hover:bg-gray-50 focus:bg-blue-50 focus:text-blue-900 cursor-pointer transition-colors duration-150">Completed</SelectItem>
                  <SelectItem value="cancelled" className="px-4 py-3 text-gray-900 hover:bg-gray-50 focus:bg-blue-50 focus:text-blue-900 cursor-pointer transition-colors duration-150">Cancelled</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Priority */}
            <div>
              <Label htmlFor="priority" className="text-sm font-medium text-gray-700 mb-2 block">
                Priority
              </Label>
              <Select value={priority} onValueChange={setPriority}>
                <SelectTrigger className="h-11 px-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-white text-gray-900">
                  <SelectValue className="text-gray-900" />
                </SelectTrigger>
                <SelectContent className="rounded-lg border border-gray-200 shadow-lg bg-white z-50">
                  <SelectItem value="low" className="px-4 py-3 text-gray-900 hover:bg-gray-50 focus:bg-blue-50 focus:text-blue-900 cursor-pointer transition-colors duration-150">Low</SelectItem>
                  <SelectItem value="medium" className="px-4 py-3 text-gray-900 hover:bg-gray-50 focus:bg-blue-50 focus:text-blue-900 cursor-pointer transition-colors duration-150">Medium</SelectItem>
                  <SelectItem value="high" className="px-4 py-3 text-gray-900 hover:bg-gray-50 focus:bg-blue-50 focus:text-blue-900 cursor-pointer transition-colors duration-150">High</SelectItem>
                  <SelectItem value="urgent" className="px-4 py-3 text-gray-900 hover:bg-gray-50 focus:bg-blue-50 focus:text-blue-900 cursor-pointer transition-colors duration-150">Urgent</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Team Size */}
            <div>
              <Label htmlFor="teamSize" className="text-sm font-medium text-gray-700 mb-2 block">
                Team Size
              </Label>
              <div className="relative">
                <Users className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                <Input
                  id="teamSize"
                  type="number"
                  min="1"
                  value={teamSize}
                  onChange={(e) => setTeamSize(e.target.value)}
                  placeholder="5"
                  className="h-11 pl-10 pr-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 text-gray-900 placeholder-gray-400"
                />
              </div>
            </div>
          </div>
        </div>

        {/* Schedule & Budget */}
        <div className="bg-white rounded-2xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center mb-6">
            <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center mr-3">
              <Calendar className="w-4 h-4 text-green-600" />
            </div>
            <h2 className="text-lg font-semibold text-gray-900">Schedule & Budget</h2>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Start Date */}
            <div>
              <Label htmlFor="startDate" className="text-sm font-medium text-gray-700 mb-2 block">
                Project Start Date
              </Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className="w-full h-11 justify-start text-left font-normal border border-gray-300 rounded-lg hover:border-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                  >
                    <Calendar className="mr-3 h-4 w-4 text-gray-400" />
                    {startDate ? format(startDate, 'MMM d, yyyy') : <span className="text-gray-400">Select start date</span>}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0 rounded-lg border border-gray-200 shadow-lg">
                  <CalendarComponent
                    mode="single"
                    selected={startDate || undefined}
                    onSelect={(date) => setStartDate(date || null)}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>

            {/* End Date */}
            <div>
              <Label htmlFor="endDate" className="text-sm font-medium text-gray-700 mb-2 block">
                Expected Completion
              </Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className="w-full h-11 justify-start text-left font-normal border border-gray-300 rounded-lg hover:border-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                  >
                    <Calendar className="mr-3 h-4 w-4 text-gray-400" />
                    {endDate ? format(endDate, 'MMM d, yyyy') : <span className="text-gray-400">Select completion date</span>}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0 rounded-lg border border-gray-200 shadow-lg">
                  <CalendarComponent
                    mode="single"
                    selected={endDate || undefined}
                    onSelect={(date) => setEndDate(date || null)}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>

            {/* Budget */}
            <div>
              <Label htmlFor="budget" className="text-sm font-medium text-gray-700 mb-2 block">
                Total Budget
              </Label>
              <div className="relative">
                <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                <Input
                  id="budget"
                  type="number"
                  step="0.01"
                  value={budget}
                  onChange={(e) => setBudget(e.target.value)}
                  placeholder="150000.00"
                  className="h-11 pl-10 pr-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 text-gray-900 placeholder-gray-400"
                />
              </div>
            </div>

            {/* Estimated Hours */}
            <div>
              <Label htmlFor="estimatedHours" className="text-sm font-medium text-gray-700 mb-2 block">
                Estimated Labor Hours
              </Label>
              <div className="relative">
                <Clock className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                <Input
                  id="estimatedHours"
                  type="number"
                  step="0.5"
                  value={estimatedHours}
                  onChange={(e) => setEstimatedHours(e.target.value)}
                  placeholder="480"
                  className="h-11 pl-10 pr-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 text-gray-900 placeholder-gray-400"
                />
              </div>
            </div>
          </div>
        </div>

        {/* Additional Notes */}
        <div className="bg-white rounded-2xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center mb-6">
            <div className="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center mr-3">
              <FileText className="w-4 h-4 text-purple-600" />
            </div>
            <h2 className="text-lg font-semibold text-gray-900">Project Notes</h2>
          </div>

          <div>
            <Label htmlFor="notes" className="text-sm font-medium text-gray-700 mb-2 block">
              Special Requirements & Notes
            </Label>
            <Textarea
              id="notes"
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              placeholder="Include any special requirements, safety considerations, permit information, or other important notes for the construction team..."
              rows={4}
              className="px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 resize-none text-gray-900 placeholder-gray-400"
            />
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex justify-end space-x-3 pt-4">
          <Button
            type="button"
            variant="outline"
            onClick={resetForm}
            disabled={loading}
            className="px-6 py-2.5 border border-gray-300 rounded-lg hover:bg-gray-50 transition-all duration-200 text-gray-700 font-medium"
          >
            Reset
          </Button>
          <Button
            type="submit"
            disabled={loading || !name.trim()}
            className="px-6 py-2.5 bg-blue-600 hover:bg-blue-700 text-white rounded-lg shadow-sm hover:shadow transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed font-medium"
          >
            {loading ? (
              <>
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                Creating...
              </>
            ) : (
              <>
                <Plus className="w-4 h-4 mr-2" />
                Create Construction Project
              </>
            )}
          </Button>
        </div>
      </form>
    </div>
  );
}
