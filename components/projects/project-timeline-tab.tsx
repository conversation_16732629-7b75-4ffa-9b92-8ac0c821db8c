import React from 'react';
import { PlusCircle, Calendar, Clock, CheckCircle, AlertCircle, Flag } from 'lucide-react';
import { GoogleCard, GoogleCardHeader, GoogleCardTitle, GoogleCardContent, GoogleButton, GoogleBadge } from '@/components/ui/google-components';
import { formatDate } from '@/lib/utils';
import Link from 'next/link';

interface ProjectTask {
  id: string;
  project_id: string;
  name: string;
  description?: string;
  assignee_id?: string;
  status?: string;
  priority?: string;
  is_milestone?: boolean;
  estimated_hours?: number;
  due_date?: string;
  completion_date?: string;
  parent_task_id?: string;
  notes?: string;
  created_at?: string;
  updated_at?: string;
}

interface Project {
  id: string;
  name: string;
  description?: string;
  customer_id?: string;
  start_date?: string;
  end_date?: string;
  status?: string;
  budget?: number;
  budget_spent?: number;
  estimated_hours?: number;
  project_manager?: string;
  priority?: string;
  notes?: string;
  created_at?: string;
  updated_at?: string;
}

interface ProjectTimelineTabProps {
  projectId: string;
  project: Project;
  tasks: ProjectTask[];
}

export function ProjectTimelineTab({
  projectId,
  project,
  tasks
}: ProjectTimelineTabProps) {
  
  // Sort tasks by due date, with milestones at the top
  const sortedTasks = [...tasks]
    .filter(task => task.due_date)
    .sort((a, b) => {
      // First sort by milestone (milestones first)
      if (a.is_milestone && !b.is_milestone) return -1;
      if (!a.is_milestone && b.is_milestone) return 1;
      
      // Then sort by due date
      const dateA = new Date(a.due_date || '').getTime();
      const dateB = new Date(b.due_date || '').getTime();
      return dateA - dateB;
    });
  
  // Calculate project timeline bounds
  const today = new Date();
  const projectStart = project.start_date ? new Date(project.start_date) : today;
  const projectEnd = project.end_date ? new Date(project.end_date) : 
    (sortedTasks.length > 0 && sortedTasks[sortedTasks.length - 1].due_date ? 
      new Date(sortedTasks[sortedTasks.length - 1].due_date!) : 
      new Date(today.getTime() + 30 * 24 * 60 * 60 * 1000)); // 30 days from today if no end date
  
  const totalDays = Math.max(1, Math.ceil((projectEnd.getTime() - projectStart.getTime()) / (1000 * 60 * 60 * 24)));
  
  // Helper function to calculate position on timeline
  const getPositionPercent = (date: string) => {
    const taskDate = new Date(date);
    const daysDiff = Math.max(0, Math.ceil((taskDate.getTime() - projectStart.getTime()) / (1000 * 60 * 60 * 24)));
    return Math.min(100, Math.max(0, (daysDiff / totalDays) * 100));
  };
  
  const getStatusIcon = (status?: string) => {
    switch (status?.toLowerCase()) {
      case 'completed':
        return <CheckCircle className="h-4 w-4 mr-1.5" />;
      case 'in-progress':
        return <Clock className="h-4 w-4 mr-1.5" />;
      case 'blocked':
        return <AlertCircle className="h-4 w-4 mr-1.5" />;
      default:
        return null;
    }
  };
  
  const getStatusColor = (status?: string) => {
    switch (status?.toLowerCase()) {
      case 'completed':
        return 'bg-green-500';
      case 'in-progress':
        return 'bg-blue-500';
      case 'blocked':
        return 'bg-red-500';
      case 'pending':
        return 'bg-yellow-500';
      default:
        return 'bg-gray-400';
    }
  };
  
  // Check if a task is late
  const isTaskLate = (task: ProjectTask) => {
    if (task.status === 'completed') return false;
    if (!task.due_date) return false;
    
    const dueDate = new Date(task.due_date);
    return dueDate < today;
  };
  
  // Extract only milestones for the milestone section
  const milestones = tasks.filter(task => task.is_milestone);
  
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-medium text-gray-900">Project Timeline</h2>
        <Link href={`/dashboard/projects/${projectId}/timeline/add`}>
          <GoogleButton>
            <PlusCircle className="h-4 w-4 mr-1" /> Add Milestone
          </GoogleButton>
        </Link>
      </div>
      
      {/* Project Duration */}
      <GoogleCard>
        <GoogleCardHeader>
          <GoogleCardTitle>Project Duration</GoogleCardTitle>
        </GoogleCardHeader>
        <GoogleCardContent>
          <div className="flex items-center justify-between mb-2">
            <div>
              <span className="text-sm text-gray-500 block">Start Date</span>
              <span className="font-medium">{project.start_date ? formatDate(project.start_date) : 'Not set'}</span>
            </div>
            <div className="text-right">
              <span className="text-sm text-gray-500 block">End Date</span>
              <span className="font-medium">{project.end_date ? formatDate(project.end_date) : 'Not set'}</span>
            </div>
          </div>
          
          <div className="mt-4 mb-2">
            <div className="h-2 bg-gray-100 rounded-full overflow-hidden">
              {/* Timeline progress bar */}
              <div
                className="h-full bg-blue-500"
                style={{
                  width: `${Math.min(100, Math.max(0, (today.getTime() - projectStart.getTime()) / (projectEnd.getTime() - projectStart.getTime()) * 100))}%`
                }}
              ></div>
            </div>
            <div className="flex justify-between mt-1 text-xs text-gray-500">
              <span>{formatDate(projectStart.toISOString())}</span>
              <span>{formatDate(projectEnd.toISOString())}</span>
            </div>
          </div>
          
          <div className="flex justify-center mt-2">
            <GoogleBadge variant="default" className="mx-auto text-xs">
              <Calendar className="h-3 w-3 mr-1" />
              <span>
                {totalDays} {totalDays === 1 ? 'day' : 'days'} total duration
              </span>
            </GoogleBadge>
          </div>
        </GoogleCardContent>
      </GoogleCard>
      
      {/* Milestones */}
      <GoogleCard>
        <GoogleCardHeader>
          <GoogleCardTitle>Key Milestones</GoogleCardTitle>
        </GoogleCardHeader>
        <GoogleCardContent>
          {milestones.length === 0 ? (
            <div className="text-center py-6">
              <Flag className="h-8 w-8 mx-auto text-gray-300 mb-2" />
              <p className="text-gray-500">No milestones defined yet.</p>
              <Link href={`/dashboard/projects/${projectId}/timeline/add`} className="text-blue-600 hover:text-blue-800 text-sm font-medium inline-block mt-2">
                + Add a Milestone
              </Link>
            </div>
          ) : (
            <div className="space-y-4">
              {milestones.map((milestone) => (
                <div key={milestone.id} className="border-l-4 pl-4 py-2" style={{ borderColor: getStatusColor(milestone.status) }}>
                  <div className="flex items-center">
                    <h3 className="text-lg font-medium">{milestone.name}</h3>
                    {milestone.status && (
                      <GoogleBadge 
                        variant="default" 
                        className="ml-2 text-xs"
                      >
                        {getStatusIcon(milestone.status)}
                        <span>{milestone.status}</span>
                      </GoogleBadge>
                    )}
                  </div>
                  
                  {milestone.description && (
                    <p className="text-sm text-gray-600 my-1">{milestone.description}</p>
                  )}
                  
                  <div className="flex items-center mt-1 text-sm text-gray-500">
                    <Calendar className="h-4 w-4 mr-1.5" />
                    <span>{milestone.due_date ? formatDate(milestone.due_date) : 'No due date'}</span>
                  </div>
                </div>
              ))}
            </div>
          )}
        </GoogleCardContent>
      </GoogleCard>
      
      {/* Gantt Chart View */}
      {sortedTasks.length > 0 && (
        <GoogleCard>
          <GoogleCardHeader>
            <GoogleCardTitle>Timeline View</GoogleCardTitle>
          </GoogleCardHeader>
          <GoogleCardContent className="p-0">
            <div className="p-4 overflow-x-auto">
              <div className="relative min-w-[800px]">
                {/* Timeline header - month markers */}
                <div className="h-8 border-b border-gray-200 flex">
                  <div className="w-1/4 text-xs text-gray-500 font-medium">Task</div>
                  <div className="w-3/4 relative">
                    {/* Today marker */}
                    <div 
                      className="absolute top-0 bottom-0 w-px bg-red-500 z-10" 
                      style={{ 
                        left: `${getPositionPercent(today.toISOString())}%`,
                      }}
                    >
                      <div className="absolute top-8 left-0 transform -translate-x-1/2 bg-red-100 text-red-800 text-[10px] px-1 rounded">
                        Today
                      </div>
                    </div>
                  </div>
                </div>
                
                {/* Task rows */}
                {sortedTasks.map((task) => (
                  <div 
                    key={task.id}
                    className="flex h-12 items-center border-b border-gray-100 hover:bg-gray-50"
                  >
                    <div className="w-1/4 px-4 truncate flex items-center">
                      <span className={`w-2 h-2 rounded-full mr-2 ${getStatusColor(task.status)}`}></span>
                      <span className={`truncate ${task.is_milestone ? 'font-medium' : ''}`}>
                        {task.name}
                      </span>
                    </div>
                    <div className="w-3/4 relative h-full">
                      {task.due_date && (
                        <div
                          className={`absolute top-1/2 transform -translate-y-1/2 h-5 rounded px-1 text-xs flex items-center justify-center whitespace-nowrap ${
                            task.is_milestone ? 'w-auto' : 'w-4'
                          } ${
                            isTaskLate(task) && task.status !== 'completed' 
                              ? 'bg-red-100 text-red-800' 
                              : getStatusColor(task.status) + ' text-white'
                          }`}
                          style={{
                            left: `${getPositionPercent(task.due_date)}%`,
                            transform: task.is_milestone ? 'translate(-50%, -50%)' : 'translateY(-50%)',
                          }}
                          title={`${task.name} - Due: ${formatDate(task.due_date)}`}
                        >
                          {task.is_milestone && (
                            <span>
                              {task.name.length > 15 ? task.name.substring(0, 15) + '...' : task.name}
                            </span>
                          )}
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </GoogleCardContent>
        </GoogleCard>
      )}
    </div>
  );
}

export default ProjectTimelineTab; 