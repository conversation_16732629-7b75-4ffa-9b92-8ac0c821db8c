import React, { useState } from 'react';
import { Save, Archive, Trash2, <PERSON><PERSON><PERSON><PERSON>gle, <PERSON>fresh<PERSON>w, Loader2 } from 'lucide-react';
import { GoogleCard, GoogleCardHeader, GoogleCardTitle, GoogleCardContent, GoogleButton } from '@/components/ui/google-components';
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { formatDate } from '@/lib/utils';

interface Project {
  id: string;
  name: string;
  description?: string;
  customer_id?: string;
  start_date?: string;
  end_date?: string;
  status?: string;
  budget?: number;
  budget_spent?: number;
  estimated_hours?: number;
  project_manager?: string;
  priority?: string;
  notes?: string;
  created_at?: string;
  updated_at?: string;
}

interface ProjectSettingsTabProps {
  projectId: string;
  project: Project;
  handleDeleteConfirm: () => void;
  handleSelectChange: (name: string, value: string) => void;
  handleChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => void;
  saving: boolean;
  handleSave: () => void;
  isDeleting: boolean;
}

export function ProjectSettingsTab({
  projectId,
  project,
  handleDeleteConfirm,
  handleSelectChange,
  handleChange,
  saving,
  handleSave,
  isDeleting
}: ProjectSettingsTabProps) {
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [archiveConfirm, setArchiveConfirm] = useState(false);
  
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-medium text-gray-900">Project Settings</h2>
        <GoogleButton
          onClick={handleSave}
          disabled={saving}
        >
          {saving ? (
            <>
              <Loader2 className="h-4 w-4 mr-1 animate-spin" /> Saving...
            </>
          ) : (
            <>
              <Save className="h-4 w-4 mr-1" /> Save Changes
            </>
          )}
        </GoogleButton>
      </div>
      
      {/* Project Status */}
      <GoogleCard>
        <GoogleCardHeader>
          <GoogleCardTitle>Project Status</GoogleCardTitle>
        </GoogleCardHeader>
        <GoogleCardContent>
          <Label htmlFor="status">Current Status</Label>
          <Select 
            name="status" 
            value={project.status || ''} 
            onValueChange={(value) => handleSelectChange('status', value)}
          >
            <SelectTrigger className="mt-1 w-full">
              <SelectValue placeholder="Select status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="planning">Planning</SelectItem>
              <SelectItem value="active">Active</SelectItem>
              <SelectItem value="on-hold">On Hold</SelectItem>
              <SelectItem value="completed">Completed</SelectItem>
              <SelectItem value="cancelled">Cancelled</SelectItem>
            </SelectContent>
          </Select>
          
          <div className="mt-4">
            <Label htmlFor="priority">Priority</Label>
            <Select 
              name="priority" 
              value={project.priority || ''} 
              onValueChange={(value) => handleSelectChange('priority', value)}
            >
              <SelectTrigger className="mt-1 w-full">
                <SelectValue placeholder="Select priority" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="low">Low</SelectItem>
                <SelectItem value="medium">Medium</SelectItem>
                <SelectItem value="high">High</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </GoogleCardContent>
      </GoogleCard>
      
      {/* Project Manager Settings */}
      <GoogleCard>
        <GoogleCardHeader>
          <GoogleCardTitle>Project Management</GoogleCardTitle>
        </GoogleCardHeader>
        <GoogleCardContent>
          <Label htmlFor="project_manager">Project Manager</Label>
          <Textarea 
            id="project_manager" 
            name="project_manager" 
            value={project.project_manager || ''} 
            onChange={handleChange} 
            className="mt-1"
            placeholder="Enter project manager name or email"
          />
          
          <div className="mt-4 text-sm text-gray-500">
            <p>Project created: {project.created_at ? formatDate(project.created_at) : 'Unknown'}</p>
            <p>Last updated: {project.updated_at ? formatDate(project.updated_at) : 'Unknown'}</p>
          </div>
        </GoogleCardContent>
      </GoogleCard>
      
      {/* Danger Zone */}
      <GoogleCard className="border-red-200">
        <GoogleCardHeader>
          <GoogleCardTitle className="text-red-700">Danger Zone</GoogleCardTitle>
        </GoogleCardHeader>
        <GoogleCardContent>
          <div className="space-y-4">
            {/* Archive Project */}
            <div className="flex items-center justify-between pb-4 border-b border-gray-100">
              {!archiveConfirm ? (
                <>
                  <div>
                    <h3 className="font-medium text-gray-900">Archive Project</h3>
                    <p className="text-sm text-gray-500">Move this project to archives. You can restore it later.</p>
                  </div>
                  <GoogleButton
                    variant="outline"
                    className="text-amber-600 border-amber-200 hover:bg-amber-50"
                    onClick={() => setArchiveConfirm(true)}
                  >
                    <Archive className="h-4 w-4 mr-1" /> Archive
                  </GoogleButton>
                </>
              ) : (
                <>
                  <div>
                    <h3 className="font-medium text-amber-700">Confirm Archiving</h3>
                    <p className="text-sm text-amber-600">Are you sure you want to archive this project?</p>
                  </div>
                  <div className="flex space-x-2">
                    <GoogleButton
                      variant="outline"
                      className="text-gray-600"
                      onClick={() => setArchiveConfirm(false)}
                    >
                      Cancel
                    </GoogleButton>
                    <GoogleButton
                      variant="outline"
                      className="text-amber-600 border-amber-200 hover:bg-amber-50"
                      onClick={() => {
                        // Implement archive functionality
                        handleSelectChange('status', 'archived');
                        setArchiveConfirm(false);
                      }}
                    >
                      <Archive className="h-4 w-4 mr-1" /> Confirm Archive
                    </GoogleButton>
                  </div>
                </>
              )}
            </div>
            
            {/* Delete Project */}
            <div className="flex items-center justify-between pt-2">
              {!showDeleteConfirm ? (
                <>
                  <div>
                    <h3 className="font-medium text-gray-900">Delete Project</h3>
                    <p className="text-sm text-gray-500">Permanently delete this project and all its data.</p>
                  </div>
                  <GoogleButton
                    variant="outline"
                    className="text-red-600 border-red-200 hover:bg-red-50"
                    onClick={() => setShowDeleteConfirm(true)}
                  >
                    <Trash2 className="h-4 w-4 mr-1" /> Delete
                  </GoogleButton>
                </>
              ) : (
                <>
                  <div>
                    <h3 className="font-medium text-red-700 flex items-center">
                      <AlertTriangle className="h-4 w-4 mr-1" /> 
                      Confirm Deletion
                    </h3>
                    <p className="text-sm text-red-600">This action cannot be undone. All project data will be permanently removed.</p>
                  </div>
                  <div className="flex space-x-2">
                    <GoogleButton
                      variant="outline"
                      className="text-gray-600"
                      onClick={() => setShowDeleteConfirm(false)}
                    >
                      Cancel
                    </GoogleButton>
                    <GoogleButton
                      variant="outline"
                      className="text-red-600 border-red-200 hover:bg-red-50"
                      onClick={handleDeleteConfirm}
                      disabled={isDeleting}
                    >
                      {isDeleting ? (
                        <>
                          <Loader2 className="h-4 w-4 mr-1 animate-spin" /> Deleting...
                        </>
                      ) : (
                        <>
                          <Trash2 className="h-4 w-4 mr-1" /> Confirm Delete
                        </>
                      )}
                    </GoogleButton>
                  </div>
                </>
              )}
            </div>
          </div>
        </GoogleCardContent>
      </GoogleCard>
      
      {/* Reset Settings */}
      <GoogleCard>
        <GoogleCardHeader>
          <GoogleCardTitle>Reset Settings</GoogleCardTitle>
        </GoogleCardHeader>
        <GoogleCardContent>
          <div className="flex items-center justify-between">
            <div>
              <h3 className="font-medium text-gray-900">Reset to Default Settings</h3>
              <p className="text-sm text-gray-500">Reset all project settings to their default values.</p>
            </div>
            <GoogleButton
              variant="outline"
              className="text-blue-600 border-blue-200 hover:bg-blue-50"
              onClick={() => {
                // Reset functionality would go here
                handleSelectChange('status', 'planning');
                handleSelectChange('priority', 'medium');
              }}
            >
              <RefreshCw className="h-4 w-4 mr-1" /> Reset
            </GoogleButton>
          </div>
        </GoogleCardContent>
      </GoogleCard>
    </div>
  );
}

export default ProjectSettingsTab; 