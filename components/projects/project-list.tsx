'use client';

import React, { useState } from 'react';
import { ChevronDown, ChevronUp, Edit, Trash, Users, Clock, FileText } from 'lucide-react';

interface Project {
  id: number;
  name: string;
  status: 'active' | 'completed' | 'on-hold';
  progress: number;
  deadline: string;
  team: string[];
  description?: string;
  client?: string;
  budget?: string;
  tasks?: {id: number; title: string; completed: boolean}[];
}

interface ProjectListProps {
  projects: Project[];
  onEdit?: (project: Project) => void;
  onDelete?: (projectId: number) => void;
  onViewStaffing?: (projectId: number) => void;
}

export function ProjectList({ projects, onEdit, onDelete, onViewStaffing }: ProjectListProps) {
  const [expandedProjectId, setExpandedProjectId] = useState<number | null>(null);
  const [activeTab, setActiveTab] = useState<'details' | 'tasks'>('details');
  
  const toggleProjectExpand = (projectId: number) => {
    setExpandedProjectId(expandedProjectId === projectId ? null : projectId);
    setActiveTab('details'); // Reset to details tab when toggling
  };
  
  const getStatusColor = (status: string) => {
    switch(status) {
      case 'active': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      case 'completed': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'on-hold': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
          <thead className="bg-gray-50 dark:bg-gray-700">
            <tr>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"></th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Project</th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Status</th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Team</th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Deadline</th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Progress</th>
              <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Actions</th>
            </tr>
          </thead>
          <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
            {projects.map((project) => (
              <React.Fragment key={project.id}>
                <tr 
                  className={`cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors ${
                    expandedProjectId === project.id ? 'bg-blue-50 dark:bg-blue-900/20' : ''
                  }`}
                  onClick={() => toggleProjectExpand(project.id)}
                >
                  <td className="px-6 py-4 whitespace-nowrap">
                    <button className="text-gray-500 dark:text-gray-400">
                      {expandedProjectId === project.id ? <ChevronUp size={16} /> : <ChevronDown size={16} />}
                    </button>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900 dark:text-white">{project.name}</div>
                    {project.client && (
                      <div className="text-xs text-gray-500 dark:text-gray-400">{project.client}</div>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`px-2 py-1 text-xs rounded-full ${getStatusColor(project.status)}`}>
                      {project.status === 'active' ? 'Active' : project.status === 'completed' ? 'Completed' : 'On Hold'}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex -space-x-2">
                      {project.team.map((member, index) => (
                        <div key={index} className="w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center text-white font-semibold text-xs border-2 border-white dark:border-gray-800">
                          {member}
                        </div>
                      ))}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                    {new Date(project.deadline).toLocaleDateString('en-US', { year: 'numeric', month: 'short', day: 'numeric' })}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5">
                      <div 
                        className={`h-2.5 rounded-full ${
                          project.status === 'completed' ? 'bg-green-600' : 
                          project.status === 'active' ? 'bg-blue-600' : 'bg-yellow-500'
                        }`}
                        style={{ width: `${project.progress}%` }}
                      ></div>
                    </div>
                    <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">{project.progress}%</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <button 
                      className="text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300 mr-3"
                      onClick={(e) => { e.stopPropagation(); onEdit?.(project); }}
                    >
                      <Edit size={16} />
                    </button>
                    <button 
                      className="text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300"
                      onClick={(e) => { e.stopPropagation(); onDelete?.(project.id); }}
                    >
                      <Trash size={16} />
                    </button>
                  </td>
                </tr>

                {/* Expanded Content */}
                {expandedProjectId === project.id && (
                  <tr>
                    <td colSpan={7} className="bg-gray-50 dark:bg-gray-800/50 px-6 py-4">
                      <div className="mt-2 mb-4">
                        <div className="flex border-b border-gray-200 dark:border-gray-700">
                          <button
                            onClick={() => setActiveTab('details')}
                            className={`px-4 py-2 text-sm font-medium ${
                              activeTab === 'details'
                                ? 'text-blue-600 dark:text-blue-400 border-b-2 border-blue-600 dark:border-blue-400'
                                : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
                            }`}
                          >
                            Details
                          </button>
                          <button
                            onClick={() => setActiveTab('tasks')}
                            className={`px-4 py-2 text-sm font-medium ${
                              activeTab === 'tasks'
                                ? 'text-blue-600 dark:text-blue-400 border-b-2 border-blue-600 dark:border-blue-400'
                                : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
                            }`}
                          >
                            Tasks
                          </button>
                        </div>

                        {activeTab === 'details' && (
                          <div className="mt-4 grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">Project Details</h3>
                              <div className="space-y-3">
                                <div>
                                  <span className="text-sm text-gray-500 dark:text-gray-400 block">Description:</span>
                                  <span className="text-sm text-gray-900 dark:text-white">{project.description || 'No description available'}</span>
                                </div>
                                
                                <div className="grid grid-cols-2 gap-4">
                                  <div>
                                    <span className="text-sm text-gray-500 dark:text-gray-400 block">Client:</span>
                                    <span className="text-sm text-gray-900 dark:text-white">{project.client || 'Not specified'}</span>
                                  </div>
                                  <div>
                                    <span className="text-sm text-gray-500 dark:text-gray-400 block">Budget:</span>
                                    <span className="text-sm text-gray-900 dark:text-white">{project.budget || 'Not specified'}</span>
                                  </div>
                                </div>
                              </div>
                            </div>

                            <div>
                              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">Team Members</h3>
                              <div className="space-y-2">
                                {project.team.map((member, index) => (
                                  <div key={index} className="flex items-center space-x-3 bg-white dark:bg-gray-700 p-2 rounded-md">
                                    <div className="w-10 h-10 rounded-full bg-blue-500 flex items-center justify-center text-white font-semibold">
                                      {member}
                                    </div>
                                    <div className="flex-1">
                                      <div className="text-sm font-medium text-gray-900 dark:text-white">Team Member {index + 1}</div>
                                      <div className="text-xs text-gray-500 dark:text-gray-400">Role: Developer</div>
                                    </div>
                                  </div>
                                ))}
                              </div>
                            </div>
                          </div>
                        )}

                        {activeTab === 'tasks' && (
                          <div className="mt-4">
                            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">Tasks</h3>
                            {project.tasks && project.tasks.length > 0 ? (
                              <ul className="space-y-2">
                                {project.tasks.map(task => (
                                  <li key={task.id} className="flex items-center p-2 bg-white dark:bg-gray-700 rounded-md">
                                    <input
                                      type="checkbox"
                                      checked={task.completed}
                                      readOnly
                                      className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500 mr-3"
                                    />
                                    <span className={`text-sm ${task.completed ? 'line-through text-gray-500' : 'text-gray-900 dark:text-white'}`}>
                                      {task.title}
                                    </span>
                                  </li>
                                ))}
                              </ul>
                            ) : (
                              <p className="text-gray-500 dark:text-gray-400">No tasks available for this project.</p>
                            )}
                          </div>
                        )}

                        <div className="mt-6 flex justify-end">
                          <button
                            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors flex items-center space-x-2"
                            onClick={(e) => { e.stopPropagation(); onViewStaffing?.(project.id); }}
                          >
                            <Users size={16} />
                            <span>View Staffing</span>
                          </button>
                        </div>
                      </div>
                    </td>
                  </tr>
                )}
              </React.Fragment>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
} 