import React from 'react';
import { Briefcase, Calendar, DollarSign, Pencil, ChevronDown, ChevronUp, Clock, CheckCircle, AlertTriangle } from 'lucide-react';
import { GoogleCard, GoogleCardHeader, GoogleCardTitle, GoogleCardContent, GoogleChip } from '@/components/ui/google-components';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from '@/components/ui/select';
import { formatDate } from '@/lib/utils';

interface Project {
  id: string;
  name: string;
  description?: string;
  customer_id?: string;
  start_date?: string;
  end_date?: string;
  status?: string;
  budget?: number;
  budget_spent?: number;
  estimated_hours?: number;
  project_manager?: string;
  priority?: string;
  notes?: string;
  created_at?: string;
  updated_at?: string;
}

interface Customer {
  id: string;
  name: string;
  email?: string;
  phone?: string;
  address?: string;
  notes?: string;
}

interface ProjectOverviewTabProps {
  project: Project;
  customer: Customer | null;
  isEditing: boolean;
  editSections: {
    basicInfo: boolean;
    timeline: boolean;
    budget: boolean;
    notes: boolean;
  };
  customers: Customer[];
  toggleSection: (section: 'basicInfo' | 'timeline' | 'budget' | 'notes') => void;
  handleChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => void;
  handleSelectChange: (name: string, value: string) => void;
  handleNumberChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  calculateDuration: () => number | null;
  calculateProgress: () => number;
  formattedBudget: (amount: number | null | undefined) => string;
}

export function ProjectOverviewTab({
  project,
  customer,
  isEditing,
  editSections,
  customers,
  toggleSection,
  handleChange,
  handleSelectChange,
  handleNumberChange,
  calculateDuration,
  calculateProgress,
  formattedBudget
}: ProjectOverviewTabProps) {
  const progress = calculateProgress();
  const duration = calculateDuration();
  
  return (
    <div className="grid grid-cols-3 gap-6">
      <div className="col-span-2 space-y-6">
        {/* Description */}
        <GoogleCard>
          <GoogleCardHeader className="flex items-center justify-between">
            <GoogleCardTitle>Description</GoogleCardTitle>
            {isEditing && !editSections.basicInfo && (
              <button 
                onClick={() => toggleSection('basicInfo')}
                className="text-blue-600 hover:text-blue-800 flex items-center"
              >
                <Pencil className="h-4 w-4 mr-1" /> Edit
              </button>
            )}
          </GoogleCardHeader>
          <GoogleCardContent>
            {!isEditing || !editSections.basicInfo ? (
              <p className="text-gray-700">{project.description || 'No description provided.'}</p>
            ) : (
              <div className="space-y-4">
                <div>
                  <Label htmlFor="name">Project Name</Label>
                  <Input 
                    id="name" 
                    name="name" 
                    value={project.name || ''} 
                    onChange={handleChange} 
                    className="mt-1"
                  />
                </div>
                <div>
                  <Label htmlFor="description">Description</Label>
                  <Textarea 
                    id="description" 
                    name="description" 
                    value={project.description || ''} 
                    onChange={handleChange} 
                    className="mt-1"
                    rows={4}
                  />
                </div>
                <div>
                  <Label htmlFor="customer_id">Customer</Label>
                  <Select 
                    name="customer_id" 
                    value={project.customer_id || 'none'}
                    onValueChange={(value) => handleSelectChange('customer_id', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select a customer" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="none">None</SelectItem>
                      {customers.map((cust) => (
                        <SelectItem key={cust.id} value={cust.id}>{cust.name}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            )}
          </GoogleCardContent>
        </GoogleCard>
        
        {/* Timeline */}
        <GoogleCard>
          <GoogleCardHeader className="flex items-center justify-between">
            <GoogleCardTitle>Timeline</GoogleCardTitle>
            {isEditing && !editSections.timeline && (
              <button 
                onClick={() => toggleSection('timeline')}
                className="text-blue-600 hover:text-blue-800 flex items-center"
              >
                <Pencil className="h-4 w-4 mr-1" /> Edit
              </button>
            )}
          </GoogleCardHeader>
          <GoogleCardContent>
            {!isEditing || !editSections.timeline ? (
              <div className="space-y-3">
                <div className="flex gap-4">
                  <div className="w-1/2">
                    <p className="text-sm text-gray-500 mb-1">Start Date</p>
                    <p className="text-gray-700">{project.start_date ? formatDate(project.start_date) : 'Not set'}</p>
                  </div>
                  <div className="w-1/2">
                    <p className="text-sm text-gray-500 mb-1">End Date</p>
                    <p className="text-gray-700">{project.end_date ? formatDate(project.end_date) : 'Not set'}</p>
                  </div>
                </div>
                {duration && (
                  <div>
                    <p className="text-sm text-gray-500 mb-1">Duration</p>
                    <p className="text-gray-700">{duration} days</p>
                  </div>
                )}
              </div>
            ) : (
              <div className="space-y-4">
                <div className="flex gap-4">
                  <div className="w-1/2">
                    <Label htmlFor="start_date">Start Date</Label>
                    <Input 
                      id="start_date" 
                      name="start_date" 
                      type="date" 
                      value={project.start_date || ''} 
                      onChange={handleChange} 
                      className="mt-1"
                    />
                  </div>
                  <div className="w-1/2">
                    <Label htmlFor="end_date">End Date</Label>
                    <Input 
                      id="end_date" 
                      name="end_date" 
                      type="date" 
                      value={project.end_date || ''} 
                      onChange={handleChange} 
                      className="mt-1"
                    />
                  </div>
                </div>
              </div>
            )}
          </GoogleCardContent>
        </GoogleCard>
        
        {/* Budget */}
        <GoogleCard>
          <GoogleCardHeader className="flex items-center justify-between">
            <GoogleCardTitle>Budget</GoogleCardTitle>
            {isEditing && !editSections.budget && (
              <button 
                onClick={() => toggleSection('budget')}
                className="text-blue-600 hover:text-blue-800 flex items-center"
              >
                <Pencil className="h-4 w-4 mr-1" /> Edit
              </button>
            )}
          </GoogleCardHeader>
          <GoogleCardContent>
            {!isEditing || !editSections.budget ? (
              <div className="space-y-3">
                <div className="flex gap-4">
                  <div className="w-1/2">
                    <p className="text-sm text-gray-500 mb-1">Total Budget</p>
                    <p className="text-gray-700">{formattedBudget(project.budget)}</p>
                  </div>
                  <div className="w-1/2">
                    <p className="text-sm text-gray-500 mb-1">Budget Spent</p>
                    <p className="text-gray-700">{formattedBudget(project.budget_spent)}</p>
                  </div>
                </div>
                <div>
                  <p className="text-sm text-gray-500 mb-1">Estimated Hours</p>
                  <p className="text-gray-700">{project.estimated_hours || 'Not specified'}</p>
                </div>
              </div>
            ) : (
              <div className="space-y-4">
                <div className="flex gap-4">
                  <div className="w-1/2">
                    <Label htmlFor="budget">Total Budget ($)</Label>
                    <Input 
                      id="budget" 
                      name="budget" 
                      type="number" 
                      value={project.budget || ''} 
                      onChange={handleNumberChange} 
                      className="mt-1"
                    />
                  </div>
                  <div className="w-1/2">
                    <Label htmlFor="budget_spent">Budget Spent ($)</Label>
                    <Input 
                      id="budget_spent" 
                      name="budget_spent" 
                      type="number" 
                      value={project.budget_spent || ''} 
                      onChange={handleNumberChange} 
                      className="mt-1"
                    />
                  </div>
                </div>
                <div>
                  <Label htmlFor="estimated_hours">Estimated Hours</Label>
                  <Input 
                    id="estimated_hours" 
                    name="estimated_hours" 
                    type="number" 
                    value={project.estimated_hours || ''} 
                    onChange={handleNumberChange} 
                    className="mt-1"
                  />
                </div>
              </div>
            )}
          </GoogleCardContent>
        </GoogleCard>
        
        {/* Notes */}
        <GoogleCard>
          <GoogleCardHeader className="flex items-center justify-between">
            <GoogleCardTitle>Notes</GoogleCardTitle>
            {isEditing && !editSections.notes && (
              <button 
                onClick={() => toggleSection('notes')}
                className="text-blue-600 hover:text-blue-800 flex items-center"
              >
                <Pencil className="h-4 w-4 mr-1" /> Edit
              </button>
            )}
          </GoogleCardHeader>
          <GoogleCardContent>
            {!isEditing || !editSections.notes ? (
              <p className="text-gray-700">{project.notes || 'No notes added yet.'}</p>
            ) : (
              <div>
                <Label htmlFor="notes">Project Notes</Label>
                <Textarea 
                  id="notes" 
                  name="notes" 
                  value={project.notes || ''} 
                  onChange={handleChange} 
                  className="mt-1"
                  rows={6}
                />
              </div>
            )}
          </GoogleCardContent>
        </GoogleCard>
      </div>
      
      {/* Right column - Project stats */}
      <div className="space-y-6">
        <GoogleCard>
          <GoogleCardHeader>
            <GoogleCardTitle>Project Status</GoogleCardTitle>
          </GoogleCardHeader>
          <GoogleCardContent>
            <div className="space-y-4">
              {/* Progress bar */}
              <div>
                <div className="flex justify-between mb-1">
                  <span className="text-sm text-gray-500">Progress</span>
                  <span className="text-sm font-medium text-gray-700">{progress}%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className="bg-blue-600 h-2 rounded-full" 
                    style={{ width: `${progress}%` }}
                  ></div>
                </div>
              </div>
              
              {/* Project stats */}
              <div className="pt-2 space-y-3 border-t border-gray-200">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-500">Created</span>
                  <span className="text-sm text-gray-700">{project.created_at ? formatDate(project.created_at) : 'Unknown'}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-500">Last Updated</span>
                  <span className="text-sm text-gray-700">{project.updated_at ? formatDate(project.updated_at) : 'Unknown'}</span>
                </div>
                {project.project_manager && (
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-500">Project Manager</span>
                    <span className="text-sm text-gray-700">{project.project_manager}</span>
                  </div>
                )}
              </div>
            </div>
          </GoogleCardContent>
        </GoogleCard>
        
        {/* Customer card */}
        {customer && (
          <GoogleCard>
            <GoogleCardHeader>
              <GoogleCardTitle>Customer Details</GoogleCardTitle>
            </GoogleCardHeader>
            <GoogleCardContent>
              <div className="space-y-3">
                <div>
                  <p className="text-sm text-gray-500 mb-1">Name</p>
                  <p className="text-gray-700 font-medium">{customer.name}</p>
                </div>
                {customer.email && (
                  <div>
                    <p className="text-sm text-gray-500 mb-1">Email</p>
                    <p className="text-gray-700">{customer.email}</p>
                  </div>
                )}
                {customer.phone && (
                  <div>
                    <p className="text-sm text-gray-500 mb-1">Phone</p>
                    <p className="text-gray-700">{customer.phone}</p>
                  </div>
                )}
                {customer.address && (
                  <div>
                    <p className="text-sm text-gray-500 mb-1">Address</p>
                    <p className="text-gray-700">{customer.address}</p>
                  </div>
                )}
              </div>
            </GoogleCardContent>
          </GoogleCard>
        )}
      </div>
    </div>
  );
}

export default ProjectOverviewTab; 