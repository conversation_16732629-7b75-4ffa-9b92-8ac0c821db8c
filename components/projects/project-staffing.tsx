'use client';

import React from 'react';
import { Search, Calendar } from 'lucide-react';
import { Input } from '@/components/ui/input';

// Project staffing types
interface StaffingEntry {
  employeeId: string;
  day: string;
  hours: number;
}

interface Employee {
  id: string;
  name: string;
  role: string;
  avatar?: string;
}

interface ProjectStaffingProps {
  projectId: string;
  projectName: string;
  employees: Employee[];
  staffing: StaffingEntry[];
  selectedWeek: string;
  onWeekChange: (week: string) => void;
  weekOptions: string[];
}

// Days of the week
const weekDays = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'];

export function ProjectStaffing({ 
  projectId, 
  projectName, 
  employees, 
  staffing,
  selectedWeek,
  onWeekChange,
  weekOptions
}: ProjectStaffingProps) {
  const [searchTerm, setSearchTerm] = React.useState('');

  // Filter employees based on search term
  const filteredEmployees = employees.filter(employee => 
    employee.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Helper function to get hours for a specific day and employee
  const getHoursForDay = (employeeId: string, day: string) => {
    const entry = staffing.find(s => s.employeeId === employeeId && s.day === day);
    return entry ? entry.hours : 0;
  };

  // Helper function to get block width based on hours
  const getBlockWidth = (hours: number) => {
    if (hours === 8) return 'w-full';
    if (hours === 4) return 'w-1/2';
    if (hours === 0) return 'w-0';
    return `w-[${(hours/8)*100}%]`;
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4">
        <div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">{projectName} - Staffing Schedule</h3>
          <p className="text-sm text-gray-500 dark:text-gray-400">Resource allocation for current project</p>
        </div>
        
        <div className="flex items-center gap-3 w-full md:w-auto">
          <select 
            value={selectedWeek}
            onChange={(e) => onWeekChange(e.target.value)}
            className="text-sm border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            {weekOptions.map(week => (
              <option key={week} value={week}>{week}</option>
            ))}
          </select>
          
          <div className="relative flex-grow md:w-64">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-gray-400" />
            <Input
              type="text"
              placeholder="Search employee..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-8 w-full"
            />
          </div>
        </div>
      </div>
      
      {/* Hours Legend */}
      <div className="mb-6 flex flex-wrap items-center gap-2 p-3 border border-gray-100 dark:border-gray-700 rounded-md bg-gray-50 dark:bg-gray-900/50">
        <span className="text-xs font-medium text-gray-700 dark:text-gray-300 mr-2">Hours:</span>
        <div className="flex items-center">
          <div className="h-3 w-6 rounded-sm bg-blue-100 dark:bg-blue-800/20 mr-1"></div>
          <span className="text-xs text-blue-800 dark:text-blue-400">Allocated Hours</span>
        </div>
        <div className="flex items-center ml-4">
          <div className="h-3 w-6 rounded-sm bg-gray-100 dark:bg-gray-800/20 mr-1"></div>
          <span className="text-xs text-gray-800 dark:text-gray-400">Available</span>
        </div>
      </div>
      
      {/* Schedule Table */}
      <div className="border border-gray-200 dark:border-gray-700 rounded-md overflow-hidden">
        {/* Header */}
        <div className="grid grid-cols-6 bg-gray-50 dark:bg-gray-900/50 border-b border-gray-200 dark:border-gray-700">
          <div className="p-3 text-sm font-medium text-gray-500 dark:text-gray-400">
            Employee
          </div>
          {weekDays.map(day => (
            <div key={day} className="p-3 text-sm font-medium text-gray-500 dark:text-gray-400">
              {day}
            </div>
          ))}
        </div>
        
        {/* Employee Rows */}
        {filteredEmployees.length > 0 ? (
          filteredEmployees.map(employee => (
            <div 
              key={employee.id}
              className="grid grid-cols-6 border-b border-gray-200 dark:border-gray-700 last:border-b-0"
            >
              {/* Employee cell */}
              <div className="p-3 flex items-center border-r border-gray-200 dark:border-gray-700">
                {employee.avatar ? (
                  <img 
                    src={employee.avatar} 
                    alt={employee.name}
                    className="h-8 w-8 rounded-full mr-3"
                  />
                ) : (
                  <div className="h-8 w-8 rounded-full bg-blue-500 flex items-center justify-center text-white font-medium mr-3">
                    {employee.name.charAt(0).toUpperCase()}
                  </div>
                )}
                <div>
                  <span className="text-sm font-medium text-gray-900 dark:text-white block">{employee.name}</span>
                  <span className="text-xs text-gray-500 dark:text-gray-400">{employee.role}</span>
                </div>
              </div>
              
              {/* Schedule cells for each day */}
              {weekDays.map(day => {
                const hours = getHoursForDay(employee.id, day);
                return (
                  <div key={day} className="p-2 relative min-h-[3.5rem] border-r last:border-r-0 border-gray-200 dark:border-gray-700">
                    {hours > 0 ? (
                      <div 
                        className={`${hours >= 8 ? 'w-full' : `w-[${(hours/8)*100}%]`} h-full bg-blue-100 dark:bg-blue-800/20 rounded-sm px-1.5 py-1 text-blue-800 dark:text-blue-400 relative group overflow-hidden`}
                      >
                        <div className="text-xs font-medium truncate">{projectName}</div>
                        <div className="text-xs opacity-75">{hours}h</div>
                        <div className="absolute inset-0 bg-black/0 group-hover:bg-black/5 dark:group-hover:bg-white/5 transition-colors"></div>
                      </div>
                    ) : (
                      <div className="flex items-center justify-center h-full text-xs text-gray-400">
                        <span>--</span>
                      </div>
                    )}
                  </div>
                );
              })}
            </div>
          ))
        ) : (
          <div className="p-6 text-center text-gray-500 dark:text-gray-400">
            {searchTerm ? 'No employees match your search' : 'No employees assigned to this project'}
          </div>
        )}
      </div>
      
      <div className="mt-6 flex justify-between items-center">
        <div className="text-sm text-gray-500 dark:text-gray-400">
          <span className="font-medium">Total allocated:</span>{' '}
          {staffing.reduce((total, entry) => total + entry.hours, 0)} hours this week
        </div>
      </div>
    </div>
  );
} 