import React, { useState } from 'react';
import { PlusCircle, Filter, Search, MoreHorizontal, CheckCircle, Clock, AlertCircle, ArrowUpDown } from 'lucide-react';
import { GoogleCard, GoogleCardHeader, GoogleCardTitle, GoogleCardContent, GoogleBadge, GoogleButton } from '@/components/ui/google-components';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { formatDate } from '@/lib/utils';
import Link from 'next/link';

interface ProjectTask {
  id: string;
  project_id: string;
  name: string;
  description?: string;
  assignee_id?: string;
  status?: string;
  priority?: string;
  is_milestone?: boolean;
  estimated_hours?: number;
  due_date?: string;
  completion_date?: string;
  parent_task_id?: string;
  notes?: string;
  created_at?: string;
  updated_at?: string;
}

interface UserProfile {
  id: string;
  first_name?: string;
  last_name?: string;
  email?: string;
  full_name?: string;
}

interface ProjectTasksTabProps {
  projectId: string;
  tasks: ProjectTask[];
  userProfiles: Record<string, UserProfile>;
}

export function ProjectTasksTab({
  projectId,
  tasks,
  userProfiles
}: ProjectTasksTabProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string | null>(null);
  const [sortField, setSortField] = useState<string>('due_date');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');
  
  // Filter tasks by search term and status
  const filteredTasks = tasks.filter(task => {
    const matchesSearch = searchTerm === '' || 
      task.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (task.description && task.description.toLowerCase().includes(searchTerm.toLowerCase()));
    
    const matchesStatus = !statusFilter || task.status === statusFilter;
    
    return matchesSearch && matchesStatus;
  });
  
  // Sort tasks
  const sortedTasks = [...filteredTasks].sort((a, b) => {
    let aVal: any = a[sortField as keyof ProjectTask];
    let bVal: any = b[sortField as keyof ProjectTask];
    
    // Handle null/undefined values
    if (aVal === undefined || aVal === null) return sortDirection === 'asc' ? -1 : 1;
    if (bVal === undefined || bVal === null) return sortDirection === 'asc' ? 1 : -1;
    
    // Date comparisons
    if (sortField === 'due_date' || sortField === 'created_at' || sortField === 'completion_date') {
      aVal = new Date(aVal).getTime();
      bVal = new Date(bVal).getTime();
    }
    
    // String comparisons
    if (typeof aVal === 'string' && typeof bVal === 'string') {
      return sortDirection === 'asc' ? 
        aVal.localeCompare(bVal) : 
        bVal.localeCompare(aVal);
    }
    
    // Number comparisons
    return sortDirection === 'asc' ? aVal - bVal : bVal - aVal;
  });
  
  const toggleSort = (field: string) => {
    if (sortField === field) {
      setSortDirection(prev => prev === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };
  
  const getStatusBadgeVariant = (status?: string): 'default' | 'success' | 'warning' | 'error' | 'info' => {
    switch (status?.toLowerCase()) {
      case 'completed':
        return 'success';
      case 'in-progress':
        return 'info';
      case 'pending':
        return 'warning';
      case 'blocked':
        return 'error';
      default:
        return 'default';
    }
  };
  
  const getStatusIcon = (status?: string) => {
    switch (status?.toLowerCase()) {
      case 'completed':
        return <CheckCircle className="h-3 w-3 mr-1" />;
      case 'in-progress':
        return <Clock className="h-3 w-3 mr-1" />;
      case 'blocked':
        return <AlertCircle className="h-3 w-3 mr-1" />;
      default:
        return null;
    }
  };
  
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-medium text-gray-900">Project Tasks</h2>
        <Link href={`/dashboard/projects/${projectId}/tasks/add`}>
          <GoogleButton>
            <PlusCircle className="h-4 w-4 mr-1" /> Add Task
          </GoogleButton>
        </Link>
      </div>
      
      <GoogleCard>
        <GoogleCardHeader className="pb-0">
          <div className="flex items-center justify-between">
            <div className="w-64">
              <div className="relative">
                <Search className="h-4 w-4 absolute left-2.5 top-2.5 text-gray-500" />
                <Input
                  type="text"
                  placeholder="Search tasks..."
                  className="pl-8"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
            </div>
            
            <div className="flex space-x-2">
              <select
                className="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-700"
                value={statusFilter || ''}
                onChange={(e) => setStatusFilter(e.target.value || null)}
              >
                <option value="">All Statuses</option>
                <option value="pending">Pending</option>
                <option value="in-progress">In Progress</option>
                <option value="completed">Completed</option>
                <option value="blocked">Blocked</option>
              </select>
            </div>
          </div>
        </GoogleCardHeader>
        
        <GoogleCardContent>
          {sortedTasks.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-gray-500">No tasks found. Create your first task!</p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full border-collapse">
                <thead>
                  <tr className="text-xs text-gray-500 uppercase tracking-wider border-b border-gray-200">
                    <th className="text-left py-3 px-4 font-medium">
                      <button 
                        className="flex items-center"
                        onClick={() => toggleSort('name')}
                      >
                        Task
                        {sortField === 'name' && (
                          <ArrowUpDown className="h-3 w-3 ml-1" />
                        )}
                      </button>
                    </th>
                    <th className="text-left py-3 px-4 font-medium">
                      <button 
                        className="flex items-center"
                        onClick={() => toggleSort('status')}
                      >
                        Status
                        {sortField === 'status' && (
                          <ArrowUpDown className="h-3 w-3 ml-1" />
                        )}
                      </button>
                    </th>
                    <th className="text-left py-3 px-4 font-medium">
                      <button 
                        className="flex items-center"
                        onClick={() => toggleSort('due_date')}
                      >
                        Due Date
                        {sortField === 'due_date' && (
                          <ArrowUpDown className="h-3 w-3 ml-1" />
                        )}
                      </button>
                    </th>
                    <th className="text-left py-3 px-4 font-medium">
                      <button 
                        className="flex items-center"
                        onClick={() => toggleSort('priority')}
                      >
                        Priority
                        {sortField === 'priority' && (
                          <ArrowUpDown className="h-3 w-3 ml-1" />
                        )}
                      </button>
                    </th>
                    <th className="text-left py-3 px-4 font-medium">
                      <button 
                        className="flex items-center"
                        onClick={() => toggleSort('assignee_id')}
                      >
                        Assignee
                        {sortField === 'assignee_id' && (
                          <ArrowUpDown className="h-3 w-3 ml-1" />
                        )}
                      </button>
                    </th>
                    <th className="text-right py-3 px-4 font-medium">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {sortedTasks.map((task) => (
                    <tr key={task.id} className="border-b border-gray-200 hover:bg-gray-50">
                      <td className="py-3 px-4">
                        <Link 
                          href={`/dashboard/projects/${projectId}/tasks/${task.id}/edit`}
                          className="text-blue-600 hover:text-blue-800 font-medium"
                        >
                          {task.name}
                        </Link>
                        {task.description && (
                          <p className="text-xs text-gray-500 mt-1 truncate max-w-xs">
                            {task.description}
                          </p>
                        )}
                      </td>
                      <td className="py-3 px-4">
                        <GoogleBadge variant={getStatusBadgeVariant(task.status)} className="text-xs">
                          {getStatusIcon(task.status)}
                          <span>{task.status || 'Not Set'}</span>
                        </GoogleBadge>
                      </td>
                      <td className="py-3 px-4 text-sm text-gray-700">
                        {task.due_date ? formatDate(task.due_date) : 'Not set'}
                      </td>
                      <td className="py-3 px-4 text-sm text-gray-700">
                        {task.priority || 'Not set'}
                      </td>
                      <td className="py-3 px-4 text-sm text-gray-700">
                        {task.assignee_id && userProfiles[task.assignee_id] ? 
                          userProfiles[task.assignee_id].full_name || userProfiles[task.assignee_id].email : 
                          'Unassigned'}
                      </td>
                      <td className="py-3 px-4 text-right">
                        <Link 
                          href={`/dashboard/projects/${projectId}/tasks/${task.id}/edit`}
                          className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                        >
                          Edit
                        </Link>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </GoogleCardContent>
      </GoogleCard>
    </div>
  );
}

export default ProjectTasksTab; 