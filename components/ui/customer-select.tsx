'use client'

import React, { useState, useMemo } from 'react'
import { Check, ChevronsUpDown, Search, Plus } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from '@/components/ui/command'
import { cn } from '@/lib/utils'
import { Customer } from '@/models'

interface CustomerSelectProps {
  customers: Customer[]
  value: string
  onValueChange: (value: string) => void
  placeholder?: string
  disabled?: boolean
  error?: string
  onCreateNew?: () => void
}

export const CustomerSelect = React.memo(function CustomerSelect({
  customers,
  value,
  onValueChange,
  placeholder = "Välj kund...",
  disabled = false,
  error,
  onCreateNew
}: CustomerSelectProps) {
  const [open, setOpen] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')

  const filteredCustomers = useMemo(() => {
    if (!searchQuery) return customers
    const query = searchQuery.toLowerCase()
    return customers.filter(customer =>
      customer.name.toLowerCase().includes(query) ||
      (customer.email && customer.email.toLowerCase().includes(query)) ||
      (customer.company_name && customer.company_name.toLowerCase().includes(query))
    )
  }, [customers, searchQuery])

  const selectedCustomer = customers.find(customer => customer.id === value)

  return (
    <div className="space-y-2">
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className={cn(
              "w-full justify-between",
              error && "border-red-500",
              !value && "text-muted-foreground"
            )}
            disabled={disabled}
          >
            {selectedCustomer ? (
              <div className="flex flex-col items-start">
                <span className="font-medium">{selectedCustomer.name}</span>
                {selectedCustomer.email && (
                  <span className="text-sm text-gray-500">{selectedCustomer.email}</span>
                )}
              </div>
            ) : (
              placeholder
            )}
            <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-full p-0" align="start">
          <Command>
            <div className="flex items-center border-b px-3">
              <Search className="mr-2 h-4 w-4 shrink-0 opacity-50" />
              <Input
                placeholder="Sök kunder..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="border-0 focus:ring-0 focus:outline-none"
              />
            </div>
            <CommandEmpty>
              <div className="py-6 text-center">
                <p className="text-sm text-gray-500 mb-3">Ingen kund hittades</p>
                {onCreateNew && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      setOpen(false)
                      onCreateNew()
                    }}
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Skapa ny kund
                  </Button>
                )}
              </div>
            </CommandEmpty>
            <CommandGroup className="max-h-64 overflow-auto">
              {filteredCustomers.map((customer) => (
                <CommandItem
                  key={customer.id}
                  value={customer.id}
                  onSelect={() => {
                    onValueChange(customer.id === value ? "" : customer.id)
                    setOpen(false)
                  }}
                  className="flex items-center justify-between"
                >
                  <div className="flex flex-col">
                    <span className="font-medium">{customer.name}</span>
                    <div className="flex items-center gap-2 text-sm text-gray-500">
                      {customer.email && <span>{customer.email}</span>}
                      {customer.company_name && (
                        <>
                          {customer.email && <span>•</span>}
                          <span>{customer.company_name}</span>
                        </>
                      )}
                    </div>
                  </div>
                  <Check
                    className={cn(
                      "ml-auto h-4 w-4",
                      value === customer.id ? "opacity-100" : "opacity-0"
                    )}
                  />
                </CommandItem>
              ))}
            </CommandGroup>
            {onCreateNew && (
              <div className="border-t p-2">
                <Button
                  variant="ghost"
                  size="sm"
                  className="w-full justify-start"
                  onClick={() => {
                    setOpen(false)
                    onCreateNew()
                  }}
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Skapa ny kund
                </Button>
              </div>
            )}
          </Command>
        </PopoverContent>
      </Popover>
      {error && (
        <p className="text-sm text-red-600">{error}</p>
      )}
    </div>
  )
})
