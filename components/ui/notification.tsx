"use client";

import { toast } from "sonner";
import { logAction } from "@/lib/data/changelog";

/**
 * Enhanced toast notification options - simplified to match Sonner types
 */
interface ToastOptions {
  description?: string;
  duration?: number;
  id?: string | number;
  dismissible?: boolean;
  onDismiss?: () => void;
  onAutoClose?: () => void;
}

/**
 * A wrapper for the toast notification that also logs actions to the changelog
 * with enhanced styling and better UX
 */
export const notify = {
  /**
   * Show a success toast and log the action
   */
  success: (
    title: string,
    description?: string,
    options?: ToastOptions
  ) => {
    logAction("Success", description || title);
    return toast.success(title, {
      description,
      duration: 4000,
      dismissible: true,
      ...options,
    });
  },

  /**
   * Show an error toast and log the action
   */
  error: (
    title: string,
    description?: string,
    options?: ToastOptions
  ) => {
    logAction("Error", description || title);
    return toast.error(title, {
      description,
      duration: 6000, // Longer duration for errors
      dismissible: true,
      ...options,
    });
  },

  /**
   * Show an info toast and log the action
   */
  info: (
    title: string,
    description?: string,
    options?: ToastOptions
  ) => {
    logAction("Info", description || title);
    return toast.info(title, {
      description,
      duration: 4000,
      dismissible: true,
      ...options,
    });
  },

  /**
   * Show a warning toast and log the action
   */
  warning: (
    title: string,
    description?: string,
    options?: ToastOptions
  ) => {
    logAction("Warning", description || title);
    return toast.warning(title, {
      description,
      duration: 5000, // Slightly longer for warnings
      dismissible: true,
      ...options,
    });
  },

  /**
   * Show a promise toast and log actions at each stage
   */
  promise: <T,>(
    promise: Promise<T>,
    {
      loading,
      success,
      error,
    }: {
      loading: string;
      success: string | ((data: T) => string);
      error: string | ((error: any) => string);
    },
    options?: ToastOptions
  ) => {
    // Log the starting action
    logAction("Process Started", loading);

    // Return the toast promise
    return toast.promise(
      promise.then((data) => {
        // Log the success action
        const successMessage = typeof success === "function" ? success(data) : success;
        logAction("Process Completed", successMessage, "Youssef Mekidiche");
        return data;
      }).catch((err) => {
        // Log the error action
        const errorMessage = typeof error === "function" ? error(err) : error;
        logAction("Process Failed", errorMessage, "Youssef Mekidiche");
        throw err;
      }),
      {
        loading,
        success,
        error,
        duration: 4000,
        dismissible: true,
        ...options,
      }
    );
  },

  /**
   * Show a loading toast that can be updated
   */
  loading: (
    title: string,
    description?: string,
    options?: ToastOptions
  ) => {
    logAction("Loading", description || title);
    return toast.loading(title, {
      description,
      dismissible: false,
      ...options,
    });
  },

  /**
   * Dismiss a specific toast by ID
   */
  dismiss: (toastId?: string | number) => {
    return toast.dismiss(toastId);
  },

  /**
   * Dismiss all toasts
   */
  dismissAll: () => {
    return toast.dismiss();
  },
};