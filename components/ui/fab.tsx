import * as React from "react"
import { But<PERSON> } from "@/components/ui/button"
import { cn } from "@/lib/utils"

interface FloatingActionButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  icon: React.ReactNode
  position?: "bottom-right" | "bottom-left" | "top-right" | "top-left"
  mini?: boolean
  extended?: boolean
  label?: string
}

export function FloatingActionButton({
  icon,
  position = "bottom-right",
  mini = false,
  extended = false,
  label,
  className,
  ...props
}: FloatingActionButtonProps) {
  // Position classes
  const positionClasses = {
    "bottom-right": "bottom-6 right-6",
    "bottom-left": "bottom-6 left-6",
    "top-right": "top-6 right-6",
    "top-left": "top-6 left-6",
  }

  return (
    <Button
      variant="fab"
      size={mini ? "sm" : "fab"}
      className={cn(
        "fixed z-50 shadow-xl",
        positionClasses[position],
        extended && "rounded-full px-6 flex gap-2 w-auto",
        className
      )}
      {...props}
    >
      {icon}
      {extended && label && <span className="ml-1">{label}</span>}
    </Button>
  )
} 