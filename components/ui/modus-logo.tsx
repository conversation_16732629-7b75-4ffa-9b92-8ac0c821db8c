import React from 'react'

interface ModusLogoProps {
  className?: string
  size?: 'sm' | 'md' | 'lg' | 'xl'
  variant?: 'light' | 'dark'
}

export const ModusLogo = React.memo(function ModusLogo({ 
  className = '', 
  size = 'md',
  variant = 'light'
}: ModusLogoProps) {
  const sizeClasses = {
    sm: 'w-6 h-6',
    md: 'w-8 h-8',
    lg: 'w-12 h-12',
    xl: 'w-16 h-16'
  }

  const strokeColor = variant === 'light' ? 'white' : '#1f2937'
  const fillColor = variant === 'light' ? 'white' : '#1f2937'

  return (
    <svg 
      className={`${sizeClasses[size]} ${className}`} 
      viewBox="0 0 40 40" 
      fill="none" 
      xmlns="http://www.w3.org/2000/svg"
    >
      {/* Outer circle */}
      <circle 
        cx="20" 
        cy="20" 
        r="18" 
        stroke={strokeColor} 
        strokeWidth="2" 
        fill="none"
      />
      
      {/* Inner geometric pattern - representing "M" for Modus */}
      <path 
        d="M12 14 L16 26 L20 18 L24 26 L28 14" 
        stroke={strokeColor} 
        strokeWidth="2.5" 
        strokeLinecap="round" 
        strokeLinejoin="round"
        fill="none"
      />
      
      {/* Central dot */}
      <circle 
        cx="20" 
        cy="20" 
        r="2" 
        fill={fillColor}
      />
      
      {/* Accent elements */}
      <circle 
        cx="20" 
        cy="8" 
        r="1.5" 
        fill={strokeColor} 
        opacity="0.6"
      />
      <circle 
        cx="20" 
        cy="32" 
        r="1.5" 
        fill={strokeColor} 
        opacity="0.6"
      />
    </svg>
  )
})
