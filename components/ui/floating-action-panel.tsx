"use client";

import * as React from "react";
import { AnimatePresence, motion } from "framer-motion";
import { cn } from "@/lib/utils";
import { MoreHorizontal } from "lucide-react";

interface FloatingActionPanelProps {
  children: React.ReactNode;
  className?: string;
  triggerText?: string;
}

export function FloatingActionPanel({ 
  children, 
  className, 
  triggerText = "Actions" 
}: FloatingActionPanelProps) {
  const [isOpen, setIsOpen] = React.useState(false);

  const closePanel = () => setIsOpen(false);

  // Clone children and pass closePanel function to each FloatingActionItem
  const childrenWithProps = React.Children.map(children, (child) => {
    if (React.isValidElement(child) && child.type === FloatingActionItem) {
      return React.cloneElement(child as React.ReactElement<FloatingActionItemProps>, { closePanel });
    }
    return child;
  });

  return (
    <div className={cn("relative inline-block", className)}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="bg-blue-500 hover:bg-blue-600 text-white px-3 py-1.5 h-8 rounded-md flex items-center space-x-1.5 text-sm font-medium border border-blue-600 transition-colors shadow-sm"
      >
        <MoreHorizontal className="w-4 h-4" />
        <span>{triggerText}</span>
      </button>

      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, scale: 0.95, y: -8 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: -8 }}
            transition={{ 
              duration: 0.15, 
              ease: [0.16, 1, 0.3, 1],
              opacity: { duration: 0.1 }
            }}
            className="absolute top-full right-0 mt-2 bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 py-1 z-50 min-w-[200px] overflow-hidden"
            style={{
              boxShadow: "0 10px 25px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)"
            }}
          >
            <div className="py-1">
              {childrenWithProps}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}

interface FloatingActionItemProps {
  icon: React.ReactNode;
  label: string;
  onClick: () => void;
  variant?: "default" | "success" | "warning" | "danger";
  className?: string;
  closePanel?: () => void;
  preventAutoClose?: boolean;
}

export function FloatingActionItem({ 
  icon, 
  label, 
  onClick, 
  variant = "default", 
  className,
  closePanel,
  preventAutoClose = false
}: FloatingActionItemProps) {
  const getVariantStyles = () => {
    switch (variant) {
      case "success":
        return "text-green-600 hover:bg-green-50 dark:hover:bg-green-900/20";
      case "warning":
        return "text-orange-600 hover:bg-orange-50 dark:hover:bg-orange-900/20";
      case "danger":
        return "text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20";
      default:
        return "text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700";
    }
  };

  const handleClick = () => {
    onClick();
    if (!preventAutoClose && closePanel) {
      closePanel();
    }
  };

  return (
    <button
      onClick={handleClick}
      className={cn(
        "w-full flex items-center space-x-3 px-4 py-2.5 text-left transition-all duration-150 hover:scale-[0.98]",
        getVariantStyles(),
        className
      )}
    >
      <div className="flex-shrink-0 w-4 h-4 flex items-center justify-center">{icon}</div>
      <span className="text-sm font-medium">{label}</span>
    </button>
  );
}

interface FloatingActionSeparatorProps {
  label?: string;
}

export function FloatingActionSeparator({ label }: FloatingActionSeparatorProps) {
  return (
    <div className="px-4 py-1">
      <div className="border-t border-gray-200 dark:border-gray-600"></div>
      {label && (
        <div className="text-xs text-gray-500 dark:text-gray-400 mt-2 font-medium uppercase tracking-wide">
          {label}
        </div>
      )}
    </div>
  );
}