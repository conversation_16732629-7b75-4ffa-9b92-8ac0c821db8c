import React, { ReactNode } from 'react';
import { Menu, Plus } from 'lucide-react';
import { cn } from '@/lib/utils';

interface GoogleLayoutProps {
  children: ReactNode;
  title?: string;
  projectName?: string;
  activeTab?: string;
  onTabChange?: (tab: string) => void;
  showTabs?: {
    overview?: boolean;
    tasks?: boolean;
    resources?: boolean;
    timeline?: boolean;
    create?: boolean;
    settings?: boolean;
  };
}

export function GoogleLayout({
  children,
  title = 'Project Details',
  projectName,
  activeTab = 'overview',
  onTabChange = () => {},
  showTabs = {
    overview: true,
    tasks: true,
    resources: true,
    timeline: true,
    create: false,
    settings: true
  },
}: GoogleLayoutProps) {
  return (
    <div className="google-page">
      {/* Google header removed - using Apple top bar instead */}
      
      <div className="google-tabs">
        {showTabs.overview && (
          <div 
            className={cn("google-tab", activeTab === "overview" && "active")}
            onClick={() => onTabChange("overview")}
          >
            <span className="google-tab-icon">
              <Menu size={16} />
            </span>
            Overview
          </div>
        )}
        
        {showTabs.tasks && (
          <div 
            className={cn("google-tab", activeTab === "tasks" && "active")}
            onClick={() => onTabChange("tasks")}
          >
            <span className="google-tab-icon">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M4 13H10V19H4V13Z" fill="currentColor"/>
                <path d="M4 5H10V11H4V5Z" fill="currentColor"/>
                <path d="M12 5H18V11H12V5Z" fill="currentColor"/>
                <path d="M12 13H18V19H12V13Z" fill="currentColor"/>
              </svg>
            </span>
            Tasks
          </div>
        )}
        
        {showTabs.resources && (
          <div 
            className={cn("google-tab", activeTab === "resources" && "active")}
            onClick={() => onTabChange("resources")}
          >
            <span className="google-tab-icon">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M16 17V19H2V17H16ZM18.5 2C19.3 2 20 2.7 20 3.5V13.5C20 14.3 19.3 15 18.5 15H9.5C8.7 15 8 14.3 8 13.5V3.5C8 2.7 8.7 2 9.5 2H18.5ZM14 9H18V5H14V9Z" fill="currentColor"/>
              </svg>
            </span>
            Staffing
          </div>
        )}
        
        {showTabs.timeline && (
          <div 
            className={cn("google-tab", activeTab === "timeline" && "active")}
            onClick={() => onTabChange("timeline")}
          >
            <span className="google-tab-icon">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M19 4H18V2H16V4H8V2H6V4H5C3.89 4 3.01 4.9 3.01 6L3 20C3 21.1 3.89 22 5 22H19C20.1 22 21 21.1 21 20V6C21 4.9 20.1 4 19 4ZM19 20H5V10H19V20ZM9 14H7V12H9V14ZM13 14H11V12H13V14ZM17 14H15V12H17V14ZM9 18H7V16H9V18ZM13 18H11V16H13V18ZM17 18H15V16H17V18Z" fill="currentColor"/>
              </svg>
            </span>
            Planning
          </div>
        )}
        
        {showTabs.create && (
          <div 
            className={cn("google-tab", activeTab === "create" && "active")}
            onClick={() => onTabChange("create")}
          >
            <span className="google-tab-icon">
              <Plus size={16} />
            </span>
            Create
          </div>
        )}
        
        {showTabs.settings && (
          <div 
            className={cn("google-tab", activeTab === "settings" && "active")}
            onClick={() => onTabChange("settings")}
          >
            <span className="google-tab-icon">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M19.4 13C19.5 12.3 19.5 11.7 19.4 11L21.5 9.5C21.7 9.3 21.8 9 21.7 8.7L19.7 5.3C19.5 5 19.2 4.9 19 5L16.6 6.1C16 5.7 15.3 5.3 14.6 5.1L14.2 2.4C14.1 2.2 13.9 2 13.7 2H9.9C9.7 2 9.5 2.2 9.5 2.4L9.1 5.1C8.3 5.3 7.7 5.7 7 6.1L4.6 5C4.4 4.9 4.1 5 3.9 5.3L1.9 8.7C1.8 9 1.8 9.3 2.1 9.5L4.2 11C4.1 11.7 4.1 12.3 4.2 13L2.1 14.5C1.9 14.7 1.8 15 1.9 15.3L3.9 18.7C4.1 19 4.4 19.1 4.6 19L7 17.9C7.6 18.3 8.3 18.7 9 18.9L9.4 21.6C9.5 21.8 9.7 22 9.9 22H13.7C13.9 22 14.1 21.8 14.2 21.6L14.6 18.9C15.4 18.7 16 18.3 16.7 17.9L19.1 19C19.3 19.1 19.6 19 19.8 18.7L21.8 15.3C21.9 15 21.9 14.7 21.6 14.5L19.4 13ZM11.8 15.5C10 15.5 8.5 14 8.5 12.2C8.5 10.4 10 8.9 11.8 8.9C13.6 8.9 15.1 10.4 15.1 12.2C15.1 14 13.6 15.5 11.8 15.5Z" fill="currentColor"/>
              </svg>
            </span>
            Settings
          </div>
        )}
      </div>
      
      <main className="google-content">
        {children}
      </main>
    </div>
  );
}

export default GoogleLayout; 