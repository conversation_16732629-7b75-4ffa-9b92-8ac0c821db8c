import React from 'react';
import { TrendingUp, TrendingDown } from 'lucide-react';

interface StatMetric {
  label: string;
  value: string | number;
  icon: React.ReactNode;
  change?: string | number;
  changeType?: 'positive' | 'negative' | 'neutral';
  changeLabel?: string;
}

interface StatsCardsProps {
  metrics: StatMetric[];
  className?: string;
  gridCols?: string;
}

export function StatsCards({ 
  metrics, 
  className = "",
  gridCols = "grid-cols-1 md:grid-cols-2 lg:grid-cols-5"
}: StatsCardsProps) {
  return (
    <div className={`grid gap-4 ${gridCols} my-6 ${className}`}>
      {metrics.map((metric, index) => (
        <div 
          key={index} 
          className="shadow-sm border-0 bg-white dark:bg-gray-800 rounded-xl overflow-hidden hover:shadow-md transition-shadow duration-300"
        >
          <div className="p-5">
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center">
                <div className="w-10 h-10 rounded-full bg-gray-50 dark:bg-gray-700 flex items-center justify-center mr-3">
                  {metric.icon}
                </div>
                <div>
                  <div className="text-xs font-medium text-gray-500 dark:text-gray-400">
                    {metric.label}
                  </div>
                  <div className="text-lg font-normal text-gray-900 dark:text-white">
                    {metric.value}
                  </div>
                </div>
              </div>
            </div>
            {metric.change && (
              <div className="flex items-center">
                <span className={`text-xs font-medium flex items-center ${
                  metric.changeType === 'positive' 
                    ? 'text-green-600 dark:text-green-400' 
                    : metric.changeType === 'negative' 
                      ? 'text-red-600 dark:text-red-400'
                      : 'text-gray-600 dark:text-gray-400'
                }`}>
                  {metric.changeType === 'positive' ? (
                    <TrendingUp className="w-3 h-3 mr-1" />
                  ) : metric.changeType === 'negative' ? (
                    <TrendingDown className="w-3 h-3 mr-1" />
                  ) : (
                    <span className="w-3 h-3 mr-1">•</span>
                  )}
                  {metric.change}
                </span>
                {metric.changeLabel && (
                  <span className="text-xs text-gray-500 dark:text-gray-400 ml-2">
                    {metric.changeLabel}
                  </span>
                )}
              </div>
            )}
          </div>
        </div>
      ))}
    </div>
  );
} 