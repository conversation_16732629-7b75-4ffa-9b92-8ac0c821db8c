import React, { ButtonHTMLAttributes, HTMLAttributes, ReactNode } from 'react';
import { cn } from '@/lib/utils';

// Google Card
export interface GoogleCardProps extends HTMLAttributes<HTMLDivElement> {
  children: ReactNode;
}

export function GoogleCard({
  children,
  className,
  ...props
}: GoogleCardProps) {
  return (
    <div
      className={cn(
        'bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow duration-200',
        className
      )}
      {...props}
    >
      {children}
    </div>
  );
}

export interface GoogleCardHeaderProps extends HTMLAttributes<HTMLDivElement> {
  children: ReactNode;
}

export function GoogleCardHeader({
  children,
  className,
  ...props
}: GoogleCardHeaderProps) {
  return (
    <div
      className={cn('px-6 py-4 border-b border-gray-200', className)}
      {...props}
    >
      {children}
    </div>
  );
}

export interface GoogleCardTitleProps extends HTMLAttributes<HTMLHeadingElement> {
  children: ReactNode;
}

export function GoogleCardTitle({
  children,
  className,
  ...props
}: GoogleCardTitleProps) {
  return (
    <h3
      className={cn('text-lg font-medium text-gray-900', className)}
      {...props}
    >
      {children}
    </h3>
  );
}

export interface GoogleCardContentProps extends HTMLAttributes<HTMLDivElement> {
  children: ReactNode;
}

export function GoogleCardContent({
  children,
  className,
  ...props
}: GoogleCardContentProps) {
  return (
    <div
      className={cn('px-6 py-4', className)}
      {...props}
    >
      {children}
    </div>
  );
}

// Google Button
export interface GoogleButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {
  children: ReactNode;
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost';
  size?: 'small' | 'medium' | 'large';
}

export function GoogleButton({
  children,
  className,
  variant = 'primary',
  size = 'medium',
  ...props
}: GoogleButtonProps) {
  const variants = {
    primary: 'bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500',
    secondary: 'bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500',
    outline: 'border border-gray-300 text-gray-700 hover:bg-gray-50 focus:ring-blue-500',
    ghost: 'text-gray-700 hover:bg-gray-100 focus:ring-gray-500'
  };
  
  const sizes = {
    small: 'px-3 py-1.5 text-sm',
    medium: 'px-4 py-2 text-sm',
    large: 'px-6 py-3 text-base'
  };
  
  return (
    <button
      className={cn(
        'inline-flex items-center justify-center rounded-md font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none',
        variants[variant],
        sizes[size],
        className
      )}
      {...props}
    >
      {children}
    </button>
  );
}

// Google Badge
export interface GoogleBadgeProps extends HTMLAttributes<HTMLSpanElement> {
  children: ReactNode;
  variant?: 'default' | 'success' | 'warning' | 'error' | 'info';
}

export function GoogleBadge({
  children,
  className,
  variant = 'default',
  ...props
}: GoogleBadgeProps) {
  const variants = {
    default: 'bg-gray-100 text-gray-800',
    success: 'bg-green-100 text-green-800',
    warning: 'bg-yellow-100 text-yellow-800',
    error: 'bg-red-100 text-red-800',
    info: 'bg-blue-100 text-blue-800'
  };
  
  return (
    <span
      className={cn(
        'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
        variants[variant],
        className
      )}
      {...props}
    >
      {children}
    </span>
  );
}

// Google Chip
export interface GoogleChipProps extends HTMLAttributes<HTMLDivElement> {
  children: ReactNode;
  selected?: boolean;
}

export function GoogleChip({
  children,
  className,
  selected = false,
  ...props
}: GoogleChipProps) {
  return (
    <div
      className={cn(
        'inline-flex items-center px-3 py-1 rounded-full text-sm font-medium cursor-pointer transition-colors',
        selected 
          ? 'bg-blue-100 text-blue-800 border border-blue-200' 
          : 'bg-gray-100 text-gray-700 hover:bg-gray-200 border border-gray-200',
        className
      )}
      {...props}
    >
      {children}
    </div>
  );
}

// Google Progress
export interface GoogleProgressProps extends HTMLAttributes<HTMLDivElement> {
  value: number;
}

export function GoogleProgress({
  value,
  className,
  ...props
}: GoogleProgressProps) {
  return (
    <div 
      className={cn("google-progress", className)} 
      {...props}
    >
      <div 
        className="google-progress-bar" 
        style={{ width: `${value}%` }}
      />
    </div>
  );
}

// Google Divider
export function GoogleDivider({
  className,
  ...props
}: HTMLAttributes<HTMLDivElement>) {
  return (
    <div 
      className={cn("google-divider", className)} 
      {...props}
    />
  );
}

export default {
  GoogleCard,
  GoogleCardHeader,
  GoogleCardTitle,
  GoogleCardContent,
  GoogleButton,
  GoogleBadge,
  GoogleChip,
  GoogleProgress,
  GoogleDivider
}; 