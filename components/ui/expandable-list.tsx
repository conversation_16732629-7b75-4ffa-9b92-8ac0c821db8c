import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

interface Column {
  key: string;
  label: string;
  className?: string;
}

interface ExpandableListProps<T> {
  data: T[];
  columns: Column[];
  renderCell: (item: T, columnKey: string) => React.ReactNode;
  renderExpandedContent: (item: T) => React.ReactNode;
  getRowKey: (item: T) => string;
  onRowClick?: (item: T) => void;
  className?: string;
}

export function ExpandableList<T>({
  data,
  columns,
  renderCell,
  renderExpandedContent,
  getRowKey,
  onRowClick,
  className = ""
}: ExpandableListProps<T>) {
  const [expanded, setExpanded] = useState<string | null>(null);

  const handleRowClick = (item: T) => {
    const key = getRowKey(item);
    setExpanded(expanded === key ? null : key);
    onRowClick?.(item);
  };

  return (
    <div className={`relative rounded-2xl border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 shadow-sm overflow-hidden ${className}`}>
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
          <thead className="bg-gray-50 dark:bg-gray-800/50">
            <tr>
              {columns.map((column) => (
                <th
                  key={column.key}
                  className={`px-6 py-4 text-left text-xs font-semibold text-gray-600 dark:text-gray-400 uppercase tracking-wider ${column.className || ''}`}
                >
                  {column.label}
                </th>
              ))}
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
            {data.map((item) => {
              const key = getRowKey(item);
              const isExpanded = expanded === key;
              
              return (
                <React.Fragment key={key}>
                  <motion.tr
                    className={`hover:bg-gray-50 dark:hover:bg-gray-800/50 cursor-pointer transition-all duration-200 ${
                      isExpanded ? 'bg-blue-50 dark:bg-blue-900/20 shadow-sm' : ''
                    }`}
                    onClick={() => handleRowClick(item)}
                    whileHover={{ backgroundColor: 'rgba(59, 130, 246, 0.04)' }}
                    transition={{ duration: 0.2 }}
                  >
                    {columns.map((column) => (
                      <td
                        key={column.key}
                        className={`px-6 py-4 ${column.className || ''}`}
                      >
                        {renderCell(item, column.key)}
                      </td>
                    ))}
                  </motion.tr>
                  <AnimatePresence>
                    {isExpanded && (
                      <motion.tr
                        initial={{ opacity: 0, height: 0 }}
                        animate={{ opacity: 1, height: 'auto' }}
                        exit={{ opacity: 0, height: 0 }}
                        transition={{ duration: 0.3, ease: [0.4, 0.0, 0.2, 1] }}
                      >
                        <td
                          colSpan={columns.length}
                          className="bg-gradient-to-b from-blue-50 to-blue-50/50 dark:from-blue-900/10 dark:to-blue-900/5 px-8 py-8 border-t border-blue-200/50 dark:border-blue-800/50"
                        >
                          <motion.div
                            initial={{ opacity: 0, y: -10 }}
                            animate={{ opacity: 1, y: 0 }}
                            exit={{ opacity: 0, y: -10 }}
                            transition={{ duration: 0.25, delay: 0.1, ease: [0.4, 0.0, 0.2, 1] }}
                            className="rounded-xl bg-white/60 dark:bg-gray-800/60 backdrop-blur-sm p-6 shadow-sm border border-white/20 dark:border-gray-700/20"
                          >
                            {renderExpandedContent(item)}
                          </motion.div>
                        </td>
                      </motion.tr>
                    )}
                  </AnimatePresence>
                </React.Fragment>
              );
            })}
          </tbody>
        </table>
      </div>
    </div>
  );
} 