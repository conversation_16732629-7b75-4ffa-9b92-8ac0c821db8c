"use client";

import * as React from "react";
import { ChangeEvent, useRef, useEffect } from "react";
import { cn } from "@/lib/utils";

interface AutogrowingTextareaProps extends React.ComponentProps<"textarea"> {
  defaultRows?: number;
  maxRows?: number;
}

const AutogrowingTextarea = React.forwardRef<HTMLTextAreaElement, AutogrowingTextareaProps>(
  ({ className, defaultRows = 1, maxRows, onChange, onBlur, ...props }, ref) => {
    const textareaRef = useRef<HTMLTextAreaElement>(null);
    const internalRef = ref || textareaRef;

    const handleInput = (e: ChangeEvent<HTMLTextAreaElement>) => {
      const textarea = e.target;
      textarea.style.height = "auto";

      const style = window.getComputedStyle(textarea);
      const borderHeight = parseInt(style.borderTopWidth) + parseInt(style.borderBottomWidth);
      const paddingHeight = parseInt(style.paddingTop) + parseInt(style.paddingBottom);

      const lineHeight = parseInt(style.lineHeight);
      const maxHeight = maxRows ? lineHeight * maxRows + borderHeight + paddingHeight : Infinity;

      const newHeight = Math.min(textarea.scrollHeight + borderHeight, maxHeight);

      textarea.style.height = `${newHeight}px`;

      // Call original onChange if provided
      if (onChange) {
        onChange(e);
      }
    };

    const resetToDefaultHeight = () => {
      const textarea = (internalRef as React.RefObject<HTMLTextAreaElement>)?.current;
      if (textarea) {
        textarea.style.height = "auto";
        
        const style = window.getComputedStyle(textarea);
        const borderHeight = parseInt(style.borderTopWidth) + parseInt(style.borderBottomWidth);
        const paddingHeight = parseInt(style.paddingTop) + parseInt(style.paddingBottom);
        const lineHeight = parseInt(style.lineHeight);
        
        const defaultHeight = lineHeight * defaultRows + borderHeight + paddingHeight;
        textarea.style.height = `${defaultHeight}px`;
      }
    };

    const handleBlur = (e: React.FocusEvent<HTMLTextAreaElement>) => {
      // Small delay to ensure smooth transition
      setTimeout(() => {
        resetToDefaultHeight();
      }, 150);

      // Call original onBlur if provided
      if (onBlur) {
        onBlur(e);
      }
    };

    // Set initial height
    useEffect(() => {
      resetToDefaultHeight();
    }, [defaultRows]);

    return (
      <textarea
        ref={internalRef}
        className={cn(
          "flex w-full rounded-lg border border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-800 px-3 py-2 text-sm text-gray-900 dark:text-white shadow-sm transition-all duration-150 placeholder:text-gray-500 dark:placeholder:text-gray-400 focus:border-blue-500 focus:ring-blue-500 focus:outline-none disabled:cursor-not-allowed disabled:opacity-50 resize-none min-h-[40px] h-[40px]",
          className,
        )}
        rows={defaultRows}
        onChange={handleInput}
        onBlur={handleBlur}
        {...props}
      />
    );
  },
);

AutogrowingTextarea.displayName = "AutogrowingTextarea";

export { AutogrowingTextarea }; 