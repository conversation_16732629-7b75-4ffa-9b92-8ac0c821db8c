'use client'

import React, { useState } from 'react'
import { <PERSON>, Bell, Settings, User, Command } from 'lucide-react'
import { motion } from 'framer-motion'
import { ModusLogo } from './modus-logo'

interface AppleTopBarProps {
  onSearch?: (query: string) => void
}

export default function AppleTopBar({ onSearch }: AppleTopBarProps) {
  const [searchQuery, setSearchQuery] = useState('')
  const [isSearchFocused, setIsSearchFocused] = useState(false)

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const query = e.target.value
    setSearchQuery(query)
    onSearch?.(query)
  }

  return (
    <div className="sticky top-0 z-50 bg-white/80 dark:bg-gray-900/80 backdrop-blur-xl border-b border-gray-200/50 dark:border-gray-700/50">
      <div className="flex items-center justify-between px-6 py-3">
        {/* Left side - Logo/Brand */}
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <ModusLogo size="sm" variant="dark" className="flex-shrink-0" />
            <motion.span
              initial={{ opacity: 0 }}
              animate={{
                opacity: 1,
                transition: { duration: 0.5 }
              }}
              className="font-medium whitespace-pre text-xl bg-gradient-to-r from-blue-600 via-indigo-500 to-emerald-400 bg-clip-text text-transparent font-bold relative"
            >
              Modus
              <motion.span
                className="absolute inset-0 bg-gradient-to-r from-emerald-400 via-blue-600 to-indigo-500 bg-clip-text text-transparent"
                initial={{ opacity: 0 }}
                animate={{
                  opacity: [0, 0.5, 0],
                  transition: {
                    duration: 3,
                    repeat: Infinity,
                    repeatType: "reverse"
                  }
                }}
              >
                Modus
              </motion.span>
            </motion.span>
          </div>
        </div>

        {/* Center - Search Bar */}
        <div className="flex-1 max-w-2xl mx-8">
          <div className={`relative transition-all duration-200 ${isSearchFocused ? 'scale-105' : ''}`}>
            <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
              <Search className={`h-4 w-4 transition-colors duration-200 ${
                isSearchFocused ? 'text-blue-500' : 'text-gray-400'
              }`} />
            </div>
            <input
              type="text"
              placeholder="Sök projekt, kunder, fakturor..."
              value={searchQuery}
              onChange={handleSearchChange}
              onFocus={() => setIsSearchFocused(true)}
              onBlur={() => setIsSearchFocused(false)}
              className={`
                w-full pl-11 pr-4 py-2.5 
                bg-gray-100/70 dark:bg-gray-800/70 
                border border-transparent
                rounded-xl
                text-sm text-gray-900 dark:text-white
                placeholder-gray-500 dark:placeholder-gray-400
                transition-all duration-200
                focus:outline-none focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500/30
                focus:bg-white dark:focus:bg-gray-800
                hover:bg-gray-100 dark:hover:bg-gray-800/90
              `}
            />
            {searchQuery && (
              <div className="absolute inset-y-0 right-0 pr-4 flex items-center">
                <button
                  onClick={() => {
                    setSearchQuery('')
                    onSearch?.('')
                  }}
                  className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
                >
                  <Command className="h-4 w-4" />
                </button>
              </div>
            )}
          </div>
        </div>

        {/* Right side - Actions */}
        <div className="flex items-center space-x-3">
          {/* Notifications */}
          <button className="relative p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-all duration-200">
            <Bell className="h-5 w-5" />
            <span className="absolute top-1 right-1 w-2 h-2 bg-red-500 rounded-full"></span>
          </button>

          {/* Settings */}
          <button className="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-all duration-200">
            <Settings className="h-5 w-5" />
          </button>

          {/* User Profile */}
          <button className="flex items-center space-x-2 p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-all duration-200">
            <div className="w-7 h-7 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
              <span className="text-white font-medium text-xs">Y</span>
            </div>
            <span className="text-sm font-medium hidden md:block">Youssef</span>
          </button>
        </div>
      </div>
    </div>
  )
} 