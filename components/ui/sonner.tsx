"use client"

import { useTheme } from "next-themes"
import { Toaster as Son<PERSON> } from "sonner"

type ToasterProps = React.ComponentProps<typeof Sonner>

const Toaster = ({ ...props }: ToasterProps) => {
  const { theme = "system" } = useTheme()

  return (
    <Sonner
      theme={theme as ToasterProps["theme"]}
      className="toaster group"
      position="bottom-right"
      expand={true}
      richColors={true}
      closeButton={true}
      duration={4000}
      gap={8}
      offset={16}
      toastOptions={{
        style: {
          background: 'var(--toast-background)',
          border: '1px solid var(--toast-border)',
          color: 'var(--toast-foreground)',
          borderRadius: '12px',
          boxShadow: 'var(--toast-shadow)',
          backdropFilter: 'var(--toast-backdrop-blur)',
          WebkitBackdropFilter: 'var(--toast-backdrop-blur)',
          padding: '16px',
          fontSize: '14px',
          fontWeight: '500',
          lineHeight: '1.4',
          minHeight: '48px',
        },
        classNames: {
          toast: "group toast",
          title: "group-[.toast]:font-semibold group-[.toast]:text-[14px] group-[.toast]:leading-tight",
          description: "group-[.toast]:text-[13px] group-[.toast]:opacity-80 group-[.toast]:mt-1",
          actionButton: "group-[.toast]:bg-primary group-[.toast]:text-primary-foreground group-[.toast]:rounded-md group-[.toast]:px-3 group-[.toast]:py-1.5 group-[.toast]:text-xs group-[.toast]:font-medium group-[.toast]:transition-all group-[.toast]:hover:scale-105",
          cancelButton: "group-[.toast]:bg-muted group-[.toast]:text-muted-foreground group-[.toast]:rounded-md group-[.toast]:px-3 group-[.toast]:py-1.5 group-[.toast]:text-xs group-[.toast]:font-medium group-[.toast]:transition-all group-[.toast]:hover:bg-muted/80",
          closeButton: "group-[.toast]:bg-transparent group-[.toast]:border-0 group-[.toast]:text-foreground/50 group-[.toast]:hover:text-foreground group-[.toast]:transition-colors",
          success: "group-[.toast]:bg-[var(--toast-success-bg)] group-[.toast]:border-[var(--toast-success-border)] group-[.toast]:text-[var(--toast-success-text)]",
          error: "group-[.toast]:bg-[var(--toast-error-bg)] group-[.toast]:border-[var(--toast-error-border)] group-[.toast]:text-[var(--toast-error-text)]",
          warning: "group-[.toast]:bg-[var(--toast-warning-bg)] group-[.toast]:border-[var(--toast-warning-border)] group-[.toast]:text-[var(--toast-warning-text)]",
          info: "group-[.toast]:bg-[var(--toast-info-bg)] group-[.toast]:border-[var(--toast-info-border)] group-[.toast]:text-[var(--toast-info-text)]",
        },
      }}
      {...props}
    />
  )
}

export { Toaster }