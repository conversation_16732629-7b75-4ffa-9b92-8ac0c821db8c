"use client";

import { toast } from "sonner";
import { Button } from "@/components/ui/button";
import { Toaster } from "@/components/ui/sonner";
import { notify } from "@/components/ui/notification";

export default function ToasterDemo() {
  return (
    <div className="flex flex-col gap-6 p-6">
      <Toaster />

      <div className="space-y-4">
        <h2 className="text-2xl font-bold">Toast Notification Demo</h2>
        <p className="text-muted-foreground">
          Test the improved toast notifications with better visibility and modern design.
        </p>
      </div>

      <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
        <Button
          variant="outline"
          onClick={() =>
            toast("Event Created", {
              description: "Your event has been successfully scheduled",
              action: {
                label: "View",
                onClick: () => console.log("View event"),
              },
            })
          }
        >
          Default Toast
        </Button>

        <Button
          variant="outline"
          onClick={() =>
            notify.success("Success!", "Your action was completed successfully")
          }
        >
          Success Toast
        </Button>

        <Button
          variant="outline"
          onClick={() =>
            notify.error("Error Occurred", "Something went wrong. Please try again.")
          }
        >
          Error Toast
        </Button>

        <Button
          variant="outline"
          onClick={() =>
            notify.warning("Warning", "Please review your settings before continuing.")
          }
        >
          Warning Toast
        </Button>

        <Button
          variant="outline"
          onClick={() =>
            notify.info("Information", "Here's some helpful information for you.")
          }
        >
          Info Toast
        </Button>

        <Button
          variant="outline"
          onClick={() =>
            notify.promise(
              new Promise((resolve) => setTimeout(resolve, 2000)),
              {
                loading: "Processing...",
                success: "Operation completed successfully!",
                error: "Operation failed. Please try again.",
              }
            )
          }
        >
          Promise Toast
        </Button>

        <Button
          variant="outline"
          onClick={() =>
            toast("Rich Toast", {
              description: "This toast has multiple actions",
              action: {
                label: "Action",
                onClick: () => notify.success("Action clicked!"),
              },
              cancel: {
                label: "Cancel",
                onClick: () => notify.info("Cancelled"),
              },
            })
          }
        >
          Rich Toast
        </Button>

        <Button
          variant="outline"
          onClick={() => notify.dismissAll()}
        >
          Dismiss All
        </Button>
      </div>

      <div className="space-y-2">
        <h3 className="text-lg font-semibold">Features:</h3>
        <ul className="text-sm text-muted-foreground space-y-1">
          <li>• Enhanced visibility with proper contrast</li>
          <li>• Modern blur effects and shadows</li>
          <li>• Consistent positioning (bottom-right)</li>
          <li>• Smooth animations and transitions</li>
          <li>• Support for actions and cancellation</li>
          <li>• Automatic logging to changelog</li>
        </ul>
      </div>
    </div>
  );
}

export { ToasterDemo };