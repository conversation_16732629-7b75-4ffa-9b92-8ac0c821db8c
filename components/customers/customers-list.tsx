'use client';

import React, { useEffect, useState } from 'react';
import { <PERSON>, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { supabase } from '@/lib/supabase/supabase';
import { Building, Mail, Phone, MapPin, Plus, Eye, Edit, Search, Filter, MoreHorizontal, X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { motion, AnimatePresence } from 'framer-motion';
import { Badge } from '@/components/ui/badge';

// Helper functions for project stages
const getProjectStageColor = (stage?: string): string => {
  switch (stage) {
    case 'completed_paid':
      return 'bg-green-100 text-green-800 border-green-200';
    case 'accepted':
      return 'bg-blue-100 text-blue-800 border-blue-200';
    case 'quote_sent':
      return 'bg-yellow-100 text-yellow-800 border-yellow-200';
    case 'rejected':
      return 'bg-red-100 text-red-800 border-red-200';
    case 'calculation_in_progress':
      return 'bg-purple-100 text-purple-800 border-purple-200';
    case 'inquiry':
      return 'bg-gray-100 text-gray-800 border-gray-200';
    default:
      return 'bg-gray-100 text-gray-800 border-gray-200';
  }
};

const getProjectStageLabel = (stage?: string): string => {
  switch (stage) {
    case 'completed_paid':
      return 'Slutförd (inbetald)';
    case 'accepted':
      return 'Accepterat';
    case 'quote_sent':
      return 'Offert skickad';
    case 'rejected':
      return 'Ej accepterat';
    case 'calculation_in_progress':
      return 'Pågående kalkyl';
    case 'inquiry':
      return 'Förfrågan';
    default:
      return 'Okänd status';
  }
};

interface Customer {
  id: string;
  name: string;
  company_name?: string;
  email?: string;
  phone?: string;
  address?: string;
  city?: string;
  postal_code?: string;
  country?: string;
  created_at: string;
  updated_at: string;
  project_description?: string;
  site_visit_date?: string;
  calculation_status?: string;
  quote_status?: string;
  acceptance_status?: string;
  project_status_notes?: string;
  project_stage?: string;
  source?: string;
}

interface CustomerWithProjects extends Customer {
  projects_count?: number;
  estimates_count?: number;
  total_revenue?: number;
}

export function CustomersList() {
  const [customers, setCustomers] = useState<CustomerWithProjects[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [projectStageFilter, setProjectStageFilter] = useState<string>('all');
  const [sortBy, setSortBy] = useState<{ field: keyof Customer, direction: 'asc' | 'desc' }>({ field: 'name', direction: 'asc' });
  const [expandedCustomer, setExpandedCustomer] = useState<string | null>(null);
  
  // Modal states
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [selectedCustomer, setSelectedCustomer] = useState<CustomerWithProjects | null>(null);
  
  // Customer form
  const [customerForm, setCustomerForm] = useState({
    name: '',
    company_name: '',
    email: '',
    phone: '',
    address: '',
    city: '',
    postal_code: '',
    country: 'Sverige'
  });

  useEffect(() => {
    fetchCustomers();
  }, []);

  const fetchCustomers = async () => {
    try {
      setLoading(true);
      
      // Fetch customers
      const { data: customersData, error: customersError } = await supabase
        .from('customers')
        .select('*')
        .order('name');

      if (customersError) throw customersError;

      // For each customer, fetch project and estimate counts
      const customersWithStats = await Promise.all(
        (customersData || []).map(async (customer) => {
          // Get projects count
          const { count: projectsCount } = await supabase
            .from('projects')
            .select('*', { count: 'exact', head: true })
            .eq('customer_id', customer.id);

          // Get estimates count
          const { count: estimatesCount } = await supabase
            .from('estimates')
            .select('*', { count: 'exact', head: true })
            .eq('customer_id', customer.id);

          // Get total revenue from accepted estimates
          const { data: estimates } = await supabase
            .from('estimates')
            .select('total_amount')
            .eq('customer_id', customer.id)
            .eq('status', 'accepted');

          const totalRevenue = estimates?.reduce((sum, est) => sum + (est.total_amount || 0), 0) || 0;

          return {
            ...customer,
            projects_count: projectsCount || 0,
            estimates_count: estimatesCount || 0,
            total_revenue: totalRevenue
          };
        })
      );

      setCustomers(customersWithStats);
    } catch (error) {
      console.error('Error fetching customers:', error);
    } finally {
      setLoading(false);
    }
  };

  const filteredAndSortedCustomers = customers
    .filter(customer => 
      customer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      customer.company_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      customer.email?.toLowerCase().includes(searchTerm.toLowerCase())
    )
    .sort((a, b) => {
      const aValue = a[sortBy.field] || '';
      const bValue = b[sortBy.field] || '';
      
      if (sortBy.direction === 'asc') {
        return aValue.toString().localeCompare(bValue.toString());
      } else {
        return bValue.toString().localeCompare(aValue.toString());
      }
    });

  const handleSort = (field: keyof Customer) => {
    setSortBy(prev => ({
      field,
      direction: prev.field === field && prev.direction === 'asc' ? 'desc' : 'asc'
    }));
  };

  const handleRowClick = (customerId: string) => {
    setExpandedCustomer(expandedCustomer === customerId ? null : customerId);
  };

  const handleAddCustomer = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      const { data, error } = await supabase
        .from('customers')
        .insert([customerForm])
        .select()
        .single();

      if (error) throw error;

      setCustomers(prev => [...prev, { ...data, projects_count: 0, estimates_count: 0, total_revenue: 0 }]);
      setShowAddModal(false);
      setCustomerForm({
        name: '',
        company_name: '',
        email: '',
        phone: '',
        address: '',
        city: '',
        postal_code: '',
        country: 'Sverige'
      });
    } catch (error) {
      console.error('Error adding customer:', error);
      alert('Kunde inte lägga till kund');
    }
  };

  const handleEditCustomer = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!selectedCustomer) return;

    try {
      const { data, error } = await supabase
        .from('customers')
        .update(customerForm)
        .eq('id', selectedCustomer.id)
        .select()
        .single();

      if (error) throw error;

      setCustomers(prev => prev.map(customer => 
        customer.id === selectedCustomer.id 
          ? { ...customer, ...data }
          : customer
      ));
      setShowEditModal(false);
      setSelectedCustomer(null);
    } catch (error) {
      console.error('Error updating customer:', error);
      alert('Kunde inte uppdatera kund');
    }
  };

  const openAddModal = () => {
    setCustomerForm({
      name: '',
      company_name: '',
      email: '',
      phone: '',
      address: '',
      city: '',
      postal_code: '',
      country: 'Sverige'
    });
    setShowAddModal(true);
  };

  const openEditModal = (customer: CustomerWithProjects) => {
    setSelectedCustomer(customer);
    setCustomerForm({
      name: customer.name,
      company_name: customer.company_name || '',
      email: customer.email || '',
      phone: customer.phone || '',
      address: customer.address || '',
      city: customer.city || '',
      postal_code: customer.postal_code || '',
      country: customer.country || 'Sverige'
    });
    setShowEditModal(true);
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('sv-SE', {
      style: 'currency',
      currency: 'SEK'
    }).format(amount);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Kunder</h1>
          <p className="text-gray-500 dark:text-gray-400">Hantera dina kunder och deras information</p>
        </div>
        <Button onClick={openAddModal} className="flex items-center gap-2">
          <Plus className="w-4 h-4" />
          Lägg till kund
        </Button>
      </div>

      {/* Search and Filter */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <Input
            placeholder="Sök kunder..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        <Select value={`${sortBy.field}-${sortBy.direction}`} onValueChange={(value) => {
          const [field, direction] = value.split('-') as [keyof Customer, 'asc' | 'desc'];
          setSortBy({ field, direction });
        }}>
          <SelectTrigger className="w-48">
            <SelectValue placeholder="Sortera efter" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="name-asc">Namn (A-Ö)</SelectItem>
            <SelectItem value="name-desc">Namn (Ö-A)</SelectItem>
            <SelectItem value="company_name-asc">Företag (A-Ö)</SelectItem>
            <SelectItem value="company_name-desc">Företag (Ö-A)</SelectItem>
            <SelectItem value="created_at-desc">Senast tillagd</SelectItem>
            <SelectItem value="created_at-asc">Först tillagd</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Customers List */}
      <Card>
        <CardContent className="p-0">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50 dark:bg-gray-800">
                <tr>
                  <th 
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700"
                    onClick={() => handleSort('name')}
                  >
                    Kund
                  </th>
                  <th 
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700"
                    onClick={() => handleSort('company_name')}
                  >
                    Företag
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Kontakt
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Statistik
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Åtgärder
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
                {filteredAndSortedCustomers.map((customer) => (
                  <React.Fragment key={customer.id}>
                    <tr 
                      className="hover:bg-gray-50 dark:hover:bg-gray-800 cursor-pointer transition-colors"
                      onClick={() => handleRowClick(customer.id)}
                    >
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="flex-shrink-0 h-10 w-10">
                            <div className="h-10 w-10 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center">
                              <Building className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                            </div>
                          </div>
                          <div className="ml-4">
                            <div className="text-sm font-medium text-gray-900 dark:text-white">
                              {customer.name}
                            </div>
                            <div className="text-sm text-gray-500 dark:text-gray-400">
                              ID: {customer.id.slice(0, 8)}...
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900 dark:text-white">
                          {customer.company_name || '-'}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900 dark:text-white">
                          {customer.email && (
                            <div className="flex items-center gap-1 mb-1">
                              <Mail className="w-3 h-3 text-gray-400" />
                              {customer.email}
                            </div>
                          )}
                          {customer.phone && (
                            <div className="flex items-center gap-1">
                              <Phone className="w-3 h-3 text-gray-400" />
                              {customer.phone}
                            </div>
                          )}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex flex-col gap-1">
                          <Badge variant="outline" className="text-xs">
                            {customer.projects_count} projekt
                          </Badge>
                          <Badge variant="outline" className="text-xs">
                            {customer.estimates_count} offerter
                          </Badge>
                          {customer.total_revenue && customer.total_revenue > 0 && (
                            <div className="text-xs text-green-600 dark:text-green-400 font-medium">
                              {formatCurrency(customer.total_revenue)}
                            </div>
                          )}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div className="flex items-center justify-end gap-2" onClick={(e) => e.stopPropagation()}>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => openEditModal(customer)}
                            className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
                          >
                            <Edit className="w-4 h-4" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                    
                    {/* Expanded Content */}
                    <AnimatePresence>
                      {expandedCustomer === customer.id && (
                        <motion.tr
                          initial={{ opacity: 0, height: 0 }}
                          animate={{ opacity: 1, height: 'auto' }}
                          exit={{ opacity: 0, height: 0 }}
                          transition={{ duration: 0.2 }}
                        >
                          <td colSpan={5} className="px-6 py-4 bg-gray-50 dark:bg-gray-800">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                              {/* Address Information */}
                              <div>
                                <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-3">
                                  Adressinformation
                                </h4>
                                <div className="space-y-2 text-sm text-gray-600 dark:text-gray-400">
                                  {customer.address && (
                                    <div className="flex items-start gap-2">
                                      <MapPin className="w-4 h-4 mt-0.5 flex-shrink-0" />
                                      <div>
                                        <div>{customer.address}</div>
                                        {(customer.postal_code || customer.city) && (
                                          <div>
                                            {customer.postal_code} {customer.city}
                                          </div>
                                        )}
                                        {customer.country && (
                                          <div>{customer.country}</div>
                                        )}
                                      </div>
                                    </div>
                                  )}
                                  {!customer.address && (
                                    <div className="text-gray-400 italic">Ingen adress registrerad</div>
                                  )}
                                </div>
                              </div>

                              {/* Quick Actions */}
                              <div>
                                <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-3">
                                  Snabbåtgärder
                                </h4>
                                <div className="flex flex-wrap gap-2">
                                  <Button variant="outline" size="sm">
                                    Skapa offert
                                  </Button>
                                  <Button variant="outline" size="sm">
                                    Visa projekt
                                  </Button>
                                  <Button variant="outline" size="sm">
                                    Skicka e-post
                                  </Button>
                                </div>
                              </div>
                            </div>
                          </td>
                        </motion.tr>
                      )}
                    </AnimatePresence>
                  </React.Fragment>
                ))}
              </tbody>
            </table>
          </div>
          
          {filteredAndSortedCustomers.length === 0 && (
            <div className="text-center py-12">
              <Building className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">Inga kunder</h3>
              <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                {searchTerm ? 'Inga kunder matchar din sökning.' : 'Kom igång genom att lägga till din första kund.'}
              </p>
              {!searchTerm && (
                <div className="mt-6">
                  <Button onClick={openAddModal}>
                    <Plus className="w-4 h-4 mr-2" />
                    Lägg till kund
                  </Button>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Add Customer Modal */}
      <AnimatePresence>
        {showAddModal && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4"
            onClick={() => setShowAddModal(false)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              transition={{ type: "spring", damping: 20, stiffness: 300 }}
              className="bg-white dark:bg-gray-900 rounded-2xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-hidden"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                  Lägg till ny kund
                </h3>
                <button
                  onClick={() => setShowAddModal(false)}
                  className="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
                >
                  <X className="w-5 h-5 text-gray-500 dark:text-gray-400" />
                </button>
              </div>

              <form onSubmit={handleAddCustomer} className="p-6 space-y-4 max-h-[70vh] overflow-auto">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="name">Namn *</Label>
                    <Input
                      id="name"
                      value={customerForm.name}
                      onChange={(e) => setCustomerForm(prev => ({ ...prev, name: e.target.value }))}
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="company_name">Företagsnamn</Label>
                    <Input
                      id="company_name"
                      value={customerForm.company_name}
                      onChange={(e) => setCustomerForm(prev => ({ ...prev, company_name: e.target.value }))}
                    />
                  </div>
                  <div>
                    <Label htmlFor="email">E-post</Label>
                    <Input
                      id="email"
                      type="email"
                      value={customerForm.email}
                      onChange={(e) => setCustomerForm(prev => ({ ...prev, email: e.target.value }))}
                    />
                  </div>
                  <div>
                    <Label htmlFor="phone">Telefon</Label>
                    <Input
                      id="phone"
                      value={customerForm.phone}
                      onChange={(e) => setCustomerForm(prev => ({ ...prev, phone: e.target.value }))}
                    />
                  </div>
                  <div className="md:col-span-2">
                    <Label htmlFor="address">Adress</Label>
                    <Input
                      id="address"
                      value={customerForm.address}
                      onChange={(e) => setCustomerForm(prev => ({ ...prev, address: e.target.value }))}
                    />
                  </div>
                  <div>
                    <Label htmlFor="city">Stad</Label>
                    <Input
                      id="city"
                      value={customerForm.city}
                      onChange={(e) => setCustomerForm(prev => ({ ...prev, city: e.target.value }))}
                    />
                  </div>
                  <div>
                    <Label htmlFor="postal_code">Postnummer</Label>
                    <Input
                      id="postal_code"
                      value={customerForm.postal_code}
                      onChange={(e) => setCustomerForm(prev => ({ ...prev, postal_code: e.target.value }))}
                    />
                  </div>
                  <div className="md:col-span-2">
                    <Label htmlFor="country">Land</Label>
                    <Input
                      id="country"
                      value={customerForm.country}
                      onChange={(e) => setCustomerForm(prev => ({ ...prev, country: e.target.value }))}
                    />
                  </div>
                </div>

                <div className="flex justify-end gap-3 pt-4">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => setShowAddModal(false)}
                  >
                    Avbryt
                  </Button>
                  <Button type="submit">
                    Lägg till kund
                  </Button>
                </div>
              </form>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Edit Customer Modal */}
      <AnimatePresence>
        {showEditModal && selectedCustomer && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4"
            onClick={() => setShowEditModal(false)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              transition={{ type: "spring", damping: 20, stiffness: 300 }}
              className="bg-white dark:bg-gray-900 rounded-2xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-hidden"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                  Redigera kund
                </h3>
                <button
                  onClick={() => setShowEditModal(false)}
                  className="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
                >
                  <X className="w-5 h-5 text-gray-500 dark:text-gray-400" />
                </button>
              </div>

              <form onSubmit={handleEditCustomer} className="p-6 space-y-4 max-h-[70vh] overflow-auto">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="edit-name">Namn *</Label>
                    <Input
                      id="edit-name"
                      value={customerForm.name}
                      onChange={(e) => setCustomerForm(prev => ({ ...prev, name: e.target.value }))}
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="edit-company_name">Företagsnamn</Label>
                    <Input
                      id="edit-company_name"
                      value={customerForm.company_name}
                      onChange={(e) => setCustomerForm(prev => ({ ...prev, company_name: e.target.value }))}
                    />
                  </div>
                  <div>
                    <Label htmlFor="edit-email">E-post</Label>
                    <Input
                      id="edit-email"
                      type="email"
                      value={customerForm.email}
                      onChange={(e) => setCustomerForm(prev => ({ ...prev, email: e.target.value }))}
                    />
                  </div>
                  <div>
                    <Label htmlFor="edit-phone">Telefon</Label>
                    <Input
                      id="edit-phone"
                      value={customerForm.phone}
                      onChange={(e) => setCustomerForm(prev => ({ ...prev, phone: e.target.value }))}
                    />
                  </div>
                  <div className="md:col-span-2">
                    <Label htmlFor="edit-address">Adress</Label>
                    <Input
                      id="edit-address"
                      value={customerForm.address}
                      onChange={(e) => setCustomerForm(prev => ({ ...prev, address: e.target.value }))}
                    />
                  </div>
                  <div>
                    <Label htmlFor="edit-city">Stad</Label>
                    <Input
                      id="edit-city"
                      value={customerForm.city}
                      onChange={(e) => setCustomerForm(prev => ({ ...prev, city: e.target.value }))}
                    />
                  </div>
                  <div>
                    <Label htmlFor="edit-postal_code">Postnummer</Label>
                    <Input
                      id="edit-postal_code"
                      value={customerForm.postal_code}
                      onChange={(e) => setCustomerForm(prev => ({ ...prev, postal_code: e.target.value }))}
                    />
                  </div>
                  <div className="md:col-span-2">
                    <Label htmlFor="edit-country">Land</Label>
                    <Input
                      id="edit-country"
                      value={customerForm.country}
                      onChange={(e) => setCustomerForm(prev => ({ ...prev, country: e.target.value }))}
                    />
                  </div>
                </div>

                <div className="flex justify-end gap-3 pt-4">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => setShowEditModal(false)}
                  >
                    Avbryt
                  </Button>
                  <Button type="submit">
                    Spara ändringar
                  </Button>
                </div>
              </form>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
} 