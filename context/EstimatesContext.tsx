'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';
import { estimateService } from '@/services/estimateService';
import type { Estimate } from '@/models';

interface EstimatesContextType {
  estimates: Estimate[];
  loading: boolean;
  error: string | null;
  refreshEstimates: () => Promise<void>;
  createEstimate: (estimate: Omit<Estimate, 'id' | 'created_at' | 'updated_at'>) => Promise<Estimate | null>;
  updateEstimate: (id: string, updates: Partial<Estimate>) => Promise<Estimate | null>;
  deleteEstimate: (id: string) => Promise<boolean>;
}

const EstimatesContext = createContext<EstimatesContextType | undefined>(undefined);

export function EstimatesProvider({ children }: { children: React.ReactNode }) {
  const [estimates, setEstimates] = useState<Estimate[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const refreshEstimates = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await estimateService.getAllEstimates();
      setEstimates(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch estimates');
    } finally {
      setLoading(false);
    }
  };

  const createEstimate = async (estimate: Omit<Estimate, 'id' | 'created_at' | 'updated_at'>) => {
    try {
      const newEstimate = await estimateService.createEstimate(estimate);
      if (newEstimate) {
        setEstimates(prev => [newEstimate, ...prev]);
      }
      return newEstimate;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create estimate');
      return null;
    }
  };

  const updateEstimate = async (id: string, updates: Partial<Estimate>) => {
    try {
      const updatedEstimate = await estimateService.updateEstimate(id, updates);
      if (updatedEstimate) {
        setEstimates(prev => 
          prev.map(estimate => 
            estimate.id === id ? updatedEstimate : estimate
          )
        );
      }
      return updatedEstimate;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update estimate');
      return null;
    }
  };

  const deleteEstimate = async (id: string) => {
    try {
      const success = await estimateService.deleteEstimate(id);
      if (success) {
        setEstimates(prev => prev.filter(estimate => estimate.id !== id));
      }
      return success;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete estimate');
      return false;
    }
  };

  useEffect(() => {
    refreshEstimates();
  }, []);

  const value = {
    estimates,
    loading,
    error,
    refreshEstimates,
    createEstimate,
    updateEstimate,
    deleteEstimate,
  };

  return (
    <EstimatesContext.Provider value={value}>
      {children}
    </EstimatesContext.Provider>
  );
}

export function useEstimates() {
  const context = useContext(EstimatesContext);
  if (context === undefined) {
    throw new Error('useEstimates must be used within an EstimatesProvider');
  }
  return context;
} 