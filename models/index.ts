// Data Models
// Export all data models and types from this central location

// User models
export interface User {
  id: string;
  email: string;
  name?: string;
  avatar_url?: string;
  created_at: string;
  updated_at: string;
}

// Project models
export interface Project {
  id: string;
  name: string;
  description?: string;
  status: 'planning' | 'active' | 'completed' | 'on-hold';
  start_date?: string;
  end_date?: string;
  budget?: number;
  client_id?: string;
  created_at: string;
  updated_at: string;
}

// Customer models
export interface Customer {
  id: string;
  name: string;
  company_name?: string;
  is_company?: boolean;
  email?: string;
  phone?: string;
  address?: string;
  city?: string;
  postal_code?: string;
  country?: string;
  vat_number?: string;
  notes?: string;
  status?: string;
  website?: string;
  industry?: string;
  annual_revenue?: number;
  source?: string;
  assigned_to?: string;
  created_at: string;
  updated_at: string;
}

// Estimate models
export interface Estimate {
  id: string;
  title: string;
  description?: string;
  status: 'draft' | 'sent' | 'approved' | 'rejected';
  total_amount: number;
  customer_id?: string;
  valid_until?: string;
  created_at: string;
  updated_at: string;
}

export interface EstimateLineItem {
  id: string;
  estimate_id: string;
  description: string;
  quantity: number;
  unit_price: number;
  total: number;
  created_at: string;
  updated_at: string;
}

// Employee models
export interface Employee {
  id: string;
  name: string;
  email: string;
  role: string;
  department?: string;
  hourly_rate?: number;
  hire_date?: string;
  created_at: string;
  updated_at: string;
}

// Re-export types from lib/types for backward compatibility
export * from '@/lib/types'; 